{"private": true, "scripts": {"dev": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js"}, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@heroicons/react": "^2.2.0", "@mui/material": "^5.11.0", "@mui/styled-engine-sc": "^5.11.0", "@radix-ui/react-slot": "^1.2.3", "animate.css": "^4.1.1", "aos": "^2.3.4", "bcryptjs": "^3.0.2", "bootstrap": "^5.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^1.0.2", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "eslint-config-next": "^13.0.7", "formidable": "^3.5.4", "framer-motion": "^12.19.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "midtrans-client": "^1.4.3", "mysql2": "^3.14.1", "next": "^13.0.7", "node-fetch": "^3.3.2", "node-polyfill-webpack-plugin": "^4.1.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-anchor-link-smooth-scroll": "^1.0.12", "react-animated-css": "^1.2.1", "react-awesome-reveal": "^4.1.0", "react-countup": "^6.5.0", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-fontawesome": "^1.7.1", "react-icons": "^5.5.0", "react-medium-image-zoom": "^4.3.4", "react-modal": "^3.16.3", "react-modal-video": "^2.0.1", "react-owl-carousel": "^2.3.1", "react-photoswipe-gallery": "^2.2.2", "react-quill": "^2.0.0", "react-redux": "^7.2.4", "react-responsive-masonry": "^2.1.5", "react-router-dom": "^5.1.2", "react-scroll": "^1.7.16", "react-slick": "^0.29.0", "react-sticky-header": "^0.2.0", "react-toastify": "^9.1.3", "reactstrap": "^9.1.5", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "sass": "^1.54.4", "sharp": "^0.31.2", "simple-react-validator": "^1.5.0", "slick-carousel": "^1.8.1", "speakeasy": "^2.0.0", "styled-components": "^5.3.6", "swiper": "^8.4.5", "tailwind-merge": "^3.3.1", "yet-another-react-lightbox": "^3.17.0"}, "devDependencies": {"@types/node": "24.0.6", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}}