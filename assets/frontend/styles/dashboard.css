@tailwind base;
@tailwind components;
@tailwind utilities;

/* AINT GOLD Professional Dark & Purple Theme */
/* Reset and Base Styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

/* Dashboard Layout Styles */
.dashboard-container {
	display: flex;
	min-height: 100vh;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
	font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
	color: #d4d5f1;
}

/* Sidebar Styles */
.dashboard-sidebar {
	width: 280px;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	color: #d4d5f1;
	position: fixed;
	height: 100vh;
	left: 0;
	top: 0;
	transform: translateX(-100%);
	transition: transform 0.3s ease;
	z-index: 1000;
	overflow-y: auto;
	display: flex;
	flex-direction: column;
	border-right: 2px solid #ff3bd4;
	box-shadow: 4px 0 20px rgba(255, 59, 212, 0.15);
}

.dashboard-sidebar.sidebar-open {
	transform: translateX(0);
}

.dashboard-sidebar-header {
	padding: 1.5rem;
	border-bottom: 1px solid rgba(255, 59, 212, 0.2);
	flex-shrink: 0;
	background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.logo {
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 1.5rem;
	font-weight: 700;
	color: #ff3bd4;
	text-shadow: 0 2px 4px rgba(255, 59, 212, 0.3);
	transition: all 0.3s ease;
}

.logo:hover {
	color: #ccceef;
	transform: scale(1.05);
	text-shadow: 0 4px 8px rgba(255, 59, 212, 0.5);
}

.logo img {
	height: 40px;
	width: auto;
	filter: drop-shadow(0 2px 4px rgba(255, 59, 212, 0.3));
}

.dashboard-sidebar-nav {
	padding: 1rem 0;
	flex: 1;
	overflow-y: auto;
}

.dashboard-sidebar-nav a {
	width: -webkit-fill-available;
	margin-top: 4px;
	margin-bottom: 4px;
	text-decoration: none;
}

.nav-item {
	display: flex;
	align-items: center;
	padding: 0.75rem 1.5rem;
	color: rgba(212, 213, 241, 0.8);
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	font-size: 14px;
	font-weight: 500;
	border-left: 3px solid transparent;
}

.nav-item:hover {
	background: linear-gradient(
		90deg,
		rgba(255, 59, 212, 0.1) 0%,
		rgba(113, 48, 195, 0.05) 100%
	);
	color: #ff3bd4;
	border-left-color: #ff3bd4;
	transform: translateX(5px);
	box-shadow: 0 2px 10px rgba(255, 59, 212, 0.2);
}

.nav-item.active {
	background: linear-gradient(
		90deg,
		rgba(255, 59, 212, 0.2) 0%,
		rgba(113, 48, 195, 0.1) 100%
	);
	color: #ff3bd4;
	border-left-color: #ff3bd4;
	box-shadow: 0 2px 15px rgba(255, 59, 212, 0.3);
}

.nav-icon {
	margin-right: 0.75rem;
	display: flex;
	align-items: center;
	width: 20px;
	height: 20px;
	filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.nav-text {
	flex: 1;
	font-weight: 500;
}

.submenu-arrow {
	margin-left: auto;
	transition: transform 0.2s ease;
	color: #ff3bd4;
}

.submenu {
	list-style: none;
	margin: 0;
	padding: 0;
	background: linear-gradient(
		135deg,
		rgba(15, 16, 33, 0.3) 0%,
		rgba(26, 26, 46, 0.3) 100%
	);
	margin-left: 30px;
	margin-bottom: 10px;
	display: block;
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.submenu li {
	margin-bottom: 10px;
	color: #d4d5f1;
}

.submenu a {
	text-decoration: none;
	color: #d4d5f1;
	transition: color 0.3s ease;
}

.submenu a:hover {
	color: #ff3bd4;
	text-shadow: 0 1px 3px rgba(255, 59, 212, 0.5);
}

.submenu-item {
	display: flex;
	align-items: center;
	padding: 0.5rem 1.5rem 0.5rem 3.5rem;
	color: rgba(212, 213, 241, 0.7);
	cursor: pointer;
	transition: all 0.3s ease;
}

.submenu-item:hover {
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	transform: translateX(3px);
}

.submenu-item.active {
	background: rgba(255, 59, 212, 0.15);
	color: #ff3bd4;
}

/* Main Content Area */
.main-content {
	flex: 1;
	margin-left: 0;
	transition: margin-left 0.3s ease;
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
}

.top-header {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-bottom: 2px solid #ff3bd4;
	padding: 1rem 1.5rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: sticky;
	top: 0;
	z-index: 100;
	height: 64px;
	flex-shrink: 0;
	box-shadow: 0 4px 20px rgba(255, 59, 212, 0.15);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 1rem;
}

.sidebar-toggle {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	cursor: pointer;
	padding: 0.5rem;
	border-radius: 8px;
	color: #ff3bd4;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.sidebar-toggle:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
	transform: scale(1.05);
	box-shadow: 0 4px 12px rgba(255, 59, 212, 0.3);
}

.page-title {
	font-size: 1.5rem;
	font-weight: 600;
	color: #ccceef;
	margin: 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.header-right {
	display: flex;
	align-items: center;
	gap: 1rem;
}

.user-profile {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	padding: 6px 12px;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1px solid rgba(255, 59, 212, 0.2);
}

.user-profile:hover {
	background: rgba(255, 59, 212, 0.1);
	border-color: #ff3bd4;
	box-shadow: 0 4px 12px rgba(255, 59, 212, 0.2);
}

.user-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	overflow: hidden;
	border: 2px solid #ff3bd4;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	color: #d4d5f1;
	font-weight: 700;
	box-shadow: 0 2px 8px rgba(255, 59, 212, 0.3);
}

.user-info {
	display: flex;
	flex-direction: column;
}

.user-name {
	font-weight: 500;
	color: #ccceef;
	font-size: 0.875rem;
	line-height: 1.2;
}

.user-role {
	font-size: 12px;
	color: #ff3bd4;
	line-height: 1.2;
}

/* Page Content */
.page-content {
	padding: 0;
	min-height: calc(100vh - 80px);
	flex: 1;
	overflow-y: auto;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
}

/* Dashboard Content Styles */
.dashboard-content {
	padding: 2rem;
	max-width: 1200px;
	margin: 0 auto;
	width: 100%;
}

.welcome-section {
	margin-bottom: 2rem;
}

.welcome-section h2 {
	font-size: 1.875rem;
	font-weight: 700;
	color: #ccceef;
	margin-bottom: 0.5rem;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.welcome-section p {
	color: #d4d5f1;
	font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 1.5rem;
	margin-bottom: 2rem;
}

.stat-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 12px;
	padding: 1.5rem;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stat-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #ff3bd4 0%, #7130c3 100%);
}

.stat-card:hover {
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.2);
	transform: translateY(-5px);
	border-color: #ff3bd4;
}

.stat-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 0.75rem;
}

.stat-header h3 {
	font-size: 0.875rem;
	font-weight: 500;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.stat-change {
	font-size: 0.75rem;
	font-weight: 600;
	padding: 0.25rem 0.5rem;
	border-radius: 6px;
}

.stat-change.positive {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.stat-change.negative {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	color: #ef4444;
	border: 1px solid rgba(239, 68, 68, 0.3);
}

.stat-value {
	font-size: 1.875rem;
	font-weight: 700;
	color: #ff3bd4;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Content Grid */
.content-grid {
	display: grid;
	grid-template-columns: 2fr 1fr;
	gap: 1.5rem;
	margin-bottom: 2rem;
}

.chart-card,
.transactions-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 12px;
	padding: 1.5rem;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	overflow: hidden;
	transition: all 0.3s ease;
}

.chart-card:hover,
.transactions-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 1.5rem;
}

.card-header h3 {
	font-size: 1.125rem;
	font-weight: 600;
	color: #ccceef;
	margin: 0;
}

.time-selector {
	padding: 0.5rem 0.75rem;
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	font-size: 0.875rem;
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	cursor: pointer;
	transition: all 0.3s ease;
}

.time-selector:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

.view-all-btn {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	color: #ff3bd4;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	padding: 0.5rem 1rem;
	border-radius: 6px;
	transition: all 0.3s ease;
}

.view-all-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(255, 59, 212, 0.3);
}

.chart-placeholder {
	height: 200px;
	background: linear-gradient(
		135deg,
		rgba(15, 16, 33, 0.5) 0%,
		rgba(26, 26, 46, 0.5) 100%
	);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: rgba(212, 213, 241, 0.6);
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.chart-mock {
	height: 100%;
	width: 100%;
	padding: 20px;
}

/* Transactions */
.transactions-list {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.transaction-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 1rem;
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 8px;
	transition: all 0.3s ease;
	background: rgba(255, 59, 212, 0.05);
}

.transaction-item:hover {
	background: rgba(255, 59, 212, 0.1);
	border-color: #ff3bd4;
	transform: translateX(5px);
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.transaction-info {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	flex: 1;
}

.transaction-type {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.type-badge {
	padding: 0.25rem 0.5rem;
	border-radius: 4px;
	font-size: 0.75rem;
	font-weight: 500;
	text-transform: uppercase;
}

.type-badge.buy {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.type-badge.sell {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	color: #ef4444;
	border: 1px solid rgba(239, 68, 68, 0.3);
}

.token-name {
	font-weight: 500;
	color: #ccceef;
}

.transaction-details {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}

.amount {
	font-size: 0.875rem;
	color: #d4d5f1;
}

.time {
	font-size: 0.75rem;
	color: rgba(255, 59, 212, 0.8);
}

.transaction-value {
	font-weight: 600;
	color: #ff3bd4;
}

/* Quick Actions */
.quick-actions {
	margin-top: 2rem;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	padding: 20px;
	border-radius: 12px;
	border: 1px solid rgba(255, 59, 212, 0.2);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.quick-actions h3 {
	font-size: 1.125rem;
	font-weight: 600;
	color: #ccceef;
	margin-bottom: 1rem;
}

.actions-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 1rem;
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
	padding: 1rem;
	border-radius: 8px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	border: none;
	text-decoration: none;
	font-size: 14px;
	position: relative;
	overflow: hidden;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.action-btn.primary:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.5);
	background: linear-gradient(135deg, #7130c3 0%, #ff3bd4 100%);
}

.action-btn.secondary {
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.action-btn.secondary:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
	transform: translateY(-3px);
	box-shadow: 0 8px 24px rgba(255, 59, 212, 0.3);
}

/* Profile Styles */
.profile-content {
	max-width: 1000px;
	margin: 0 auto;
	padding: 24px;
}

.profile-header {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	padding: 32px;
	border-radius: 12px;
	border: 1px solid rgba(255, 59, 212, 0.2);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	margin-bottom: 24px;
	display: flex;
	gap: 32px;
	align-items: center;
}

.profile-avatar {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16px;
}

.avatar-circle {
	width: 120px;
	height: 120px;
	background: linear-gradient(135deg, #ff3bd4, #7130c3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36px;
	font-weight: 700;
	color: #d4d5f1;
	box-shadow: 0 8px 24px rgba(255, 59, 212, 0.3);
}

.change-avatar-btn {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	padding: 8px 16px;
	border-radius: 6px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s ease;
	color: #ff3bd4;
}

.change-avatar-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

.profile-info {
	flex: 1;
}

.profile-info h1 {
	font-size: 32px;
	font-weight: 700;
	color: #ccceef;
	margin: 0 0 8px 0;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.profile-email {
	font-size: 16px;
	color: #d4d5f1;
	margin: 0 0 24px 0;
}

.profile-stats {
	display: flex;
	gap: 32px;
}

.stat {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.stat-label {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
}

/* Tab Navigation */
.tab-navigation {
	display: flex;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 12px;
	border: 1px solid rgba(255, 59, 212, 0.2);
	padding: 8px;
	margin-bottom: 24px;
	gap: 4px;
}

.tab-btn {
	flex: 1;
	padding: 12px 16px;
	border: none;
	background: none;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	color: rgba(212, 213, 241, 0.7);
}

.tab-btn.active {
	background: linear-gradient(135deg, #ff3bd4, #7130c3);
	color: #d4d5f1;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.tab-btn:not(.active):hover {
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
}

.tab-content {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 12px;
	border: 1px solid rgba(255, 59, 212, 0.2);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Form Styles */
.form-section {
	padding: 32px;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.form-section:last-child {
	border-bottom: none;
}

.form-section h3 {
	font-size: 20px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 24px 0;
}

.form-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
	margin-bottom: 20px;
}

.form-group {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.form-group.full-width {
	grid-column: 1 / -1;
}

.form-group label {
	font-size: 14px;
	font-weight: 500;
	color: #d4d5f1;
}

.form-input,
.form-textarea {
	padding: 12px 16px;
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 8px;
	font-size: 16px;
	transition: all 0.3s ease;
	background: rgba(255, 59, 212, 0.05);
	color: #d4d5f1;
	width: 100%;
}

.form-input:focus,
.form-textarea:focus {
	outline: none;
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.2);
	background: rgba(255, 59, 212, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
	color: rgba(212, 213, 241, 0.5);
}

.form-textarea {
	resize: vertical;
	min-height: 100px;
}

.form-actions {
	display: flex;
	gap: 12px;
	margin-top: 24px;
}

/* Button Styles */
.save-btn,
.btn-primary {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	border: none;
	padding: 12px 24px;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.save-btn:hover,
.btn-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.5);
	background: linear-gradient(135deg, #7130c3 0%, #ff3bd4 100%);
}

.cancel-btn,
.btn-cancel {
	background: rgba(212, 213, 241, 0.1);
	color: #d4d5f1;
	border: 1px solid rgba(212, 213, 241, 0.2);
	padding: 12px 24px;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cancel-btn:hover,
.btn-cancel:hover {
	background: rgba(212, 213, 241, 0.15);
	color: #ccceef;
	border-color: rgba(212, 213, 241, 0.3);
}

.btn-danger {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.8) 0%,
		rgba(239, 68, 68, 0.6) 100%
	);
	color: white;
	padding: 6px 10px;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-weight: 500;
	transition: all 0.3s ease;
}

.btn-danger:hover {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 1) 0%,
		rgba(239, 68, 68, 0.8) 100%
	);
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn-success {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.8) 0%,
		rgba(34, 197, 94, 0.6) 100%
	);
	color: white;
	padding: 6px 10px;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-weight: 500;
	transition: all 0.3s ease;
}

.btn-success:hover {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 1) 0%,
		rgba(34, 197, 94, 0.8) 100%
	);
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
}

.btn-secondary {
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	padding: 6px 10px;
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 4px;
	cursor: pointer;
	font-weight: 500;
	transition: all 0.3s ease;
}

.btn-secondary:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
	transform: translateY(-1px);
}

/* Security and Preference Options */
.security-option,
.preference-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 0;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.security-option:last-child,
.preference-option:last-child {
	border-bottom: none;
}

.option-info h4 {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.option-info p {
	font-size: 14px;
	color: #d4d5f1;
	margin: 0;
}

.toggle-btn {
	padding: 6px 16px;
	border-radius: 6px;
	border: none;
	font-size: 12px;
	font-weight: 500;
	cursor: pointer;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.toggle-btn.enabled {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.toggle-btn.disabled {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	color: #ef4444;
	border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Switch Component */
.switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 24px;
}

.switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(212, 213, 241, 0.2);
	transition: 0.3s;
	border-radius: 24px;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.slider:before {
	position: absolute;
	content: "";
	height: 18px;
	width: 18px;
	left: 3px;
	bottom: 2px;
	background-color: #d4d5f1;
	transition: 0.3s;
	border-radius: 50%;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

input:checked + .slider {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
}

input:checked + .slider:before {
	transform: translateX(26px);
	background-color: #070710;
}

/* Password Field */
.password-field {
	position: relative;
	display: flex;
	align-items: center;
}

.password-field input {
	flex: 1;
	padding-right: 35px;
}

.toggle-password {
	background: none;
	border: none;
	position: absolute;
	right: 10px;
	cursor: pointer;
	color: rgba(212, 213, 241, 0.6);
}

.toggle-password:hover {
	color: #ff3bd4;
}

/* Table and Pagination Styles */
.category-container {
	padding: 20px;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
}

.category-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.category-table {
	width: 100%;
	border-collapse: collapse;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
}

.category-table th {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	padding: 12px 16px;
	text-align: center;
	vertical-align: middle;
	font-weight: 600;
}

.category-table td {
	padding: 12px 16px;
	text-align: center;
	vertical-align: middle;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
	color: #d4d5f1;
}

.category-table tr:hover {
	background: rgba(255, 59, 212, 0.1);
}

.pagination {
	margin-top: 20px;
	display: flex;
	gap: 8px;
}

.page-btn {
	padding: 6px 12px;
	border: 1px solid rgba(255, 59, 212, 0.3);
	background: rgba(255, 59, 212, 0.1);
	cursor: pointer;
	border-radius: 4px;
	color: #ff3bd4;
	transition: all 0.3s ease;
}

.page-btn.active {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	border-color: #ff3bd4;
}

.page-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

/* Modal Styles */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	backdrop-filter: blur(4px);
}

.modal-content {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	padding: 20px;
	width: 100%;
	max-width: 400px;
	border-radius: 12px;
	position: relative;
	animation: fadeIn 0.3s ease-in-out;
	border: 1px solid rgba(255, 59, 212, 0.2);
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
	color: #d4d5f1;
}

.modal-large {
	max-width: 800px;
	padding: 30px;
	border-radius: 12px;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	font-size: 14px;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	border: 1px solid rgba(255, 59, 212, 0.2);
	color: #d4d5f1;
}

.modal-xlarge {
	max-width: 800px;
	padding: 30px;
	border-radius: 12px;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	font-size: 14px;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	border: 1px solid rgba(255, 59, 212, 0.2);
	color: #d4d5f1;
}

.modal-title {
	font-size: 24px;
	font-weight: 600;
	text-align: center;
	margin-bottom: 25px;
	color: #ccceef;
}

.modal-title-wrapper {
	display: flex;
	align-items: flex-end;
	gap: 15px;
	margin-bottom: 25px;
}

.modal-user-info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16px 30px;
	margin-bottom: 20px;
}

.modal-scroll-body {
	overflow-y: auto;
	padding-right: 10px;
	max-height: 75vh;
	margin-bottom: 20px;
}

.modal-actions {
	margin-top: 15px;
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.puser-avatar {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	object-fit: cover;
	border: 2px solid #ff3bd4;
}

/* Search Input */
.search-input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	max-width: 300px;
	width: 100%;
}

.search-icon {
	position: absolute;
	left: 12px;
	font-size: 18px;
	color: #ff3bd4;
}

.search-input {
	padding: 10px 12px 10px 36px;
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 25px;
	width: 100%;
	font-size: 14px;
	outline: none;
	transition: all 0.3s ease;
	background: rgba(255, 59, 212, 0.05);
	color: #d4d5f1;
}

.search-input:focus {
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.2);
	background: rgba(255, 59, 212, 0.1);
}

.search-input::placeholder {
	color: rgba(212, 213, 241, 0.5);
}

/* Editor Styles */
.form-editor .ql-container {
	min-height: 150px;
}

.form-editor .ql-editor {
	min-height: 120px;
}

/* Mobile Overlay */
.mobile-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.8);
	z-index: 999;
	display: none;
}

/* Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(-15px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Professional Glow Effects */
.glow-purple {
	box-shadow: 0 0 20px rgba(255, 59, 212, 0.3);
}

.glow-purple:hover {
	box-shadow: 0 0 30px rgba(255, 59, 212, 0.5);
}

/* Responsive Design */
@media (min-width: 1024px) {
	.dashboard-sidebar {
		position: fixed;
		transform: translateX(0);
	}

	.main-content {
		margin-left: 280px;
	}

	.sidebar-toggle {
		display: none;
	}
}

@media (max-width: 1023px) {
	.mobile-overlay {
		display: block;
		background: rgba(0, 0, 0, 0.8);
	}

	.main-content {
		margin-left: 0;
	}

	.dashboard-sidebar {
		width: 280px;
	}

	.content-grid {
		grid-template-columns: 1fr;
	}

	.stats-grid {
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	}

	.actions-grid {
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	}
}

@media (max-width: 768px) {
	.user-info {
		display: none;
	}

	.page-content {
		padding: 16px;
	}

	.top-header {
		padding: 12px 16px;
	}

	.dashboard-content {
		padding: 1rem;
	}

	.stats-grid {
		grid-template-columns: 1fr;
	}

	.actions-grid {
		grid-template-columns: 1fr;
	}

	.welcome-section h2 {
		font-size: 24px;
	}

	.card-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.content-grid {
		grid-template-columns: 1fr;
	}

	.profile-header {
		flex-direction: column;
		text-align: center;
		gap: 24px;
	}

	.profile-stats {
		justify-content: center;
	}

	.form-grid {
		grid-template-columns: 1fr;
	}

	.tab-navigation {
		flex-direction: column;
	}

	.form-section {
		padding: 24px 20px;
	}

	.security-option,
	.preference-option {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.transaction-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 0.75rem;
	}
}

@media (max-width: 640px) {
	.dashboard-content {
		padding: 1rem;
	}

	.stats-grid {
		grid-template-columns: 1fr;
	}

	.actions-grid {
		grid-template-columns: 1fr;
	}

	.transaction-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 0.75rem;
	}

	.user-info {
		display: none;
	}
}

/* Additional Professional Effects */
.professional-gradient {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 50%, #0f1021 100%);
}

.purple-accent {
	border-left: 3px solid #ff3bd4;
}

.shadow-purple {
	box-shadow: 0 4px 20px rgba(255, 59, 212, 0.15);
}

.text-purple {
	color: #ff3bd4;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.border-purple {
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.hover-lift {
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(255, 59, 212, 0.2);
}

select {
	background: rgba(
		255,
		59,
		212,
		0.05
	) !important; /* light background with bs-primary tint */
	color: #d4d5f1 !important; /* body text color */
	border: 1px solid rgba(255, 59, 212, 0.3) !important; /* subtle primary border */
}

select:focus {
	box-shadow: 0 0 0 2px rgba(255, 59, 212, 0.2) !important;
}

option {
	background-color: #211831;
	color: #fff;
	padding: 10px;
	border-radius: 20px;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar-track {
	background: rgba(255, 59, 212, 0.05); /* Same as select background */
}

::-webkit-scrollbar-thumb {
	background-color: rgba(255, 59, 212, 0.3); /* Subtle primary tint */
	border-radius: 10px;
	border: 2px solid rgba(255, 59, 212, 0.1); /* Optional border for better contrast */
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(255, 59, 212, 0.5);
}

/* Firefox Scrollbar (optional) */
* {
	scrollbar-color: rgba(255, 59, 212, 0.3) rgba(255, 59, 212, 0.05);
	scrollbar-width: thin;
}

/* ===============================
   BUY AINT PAGE STYLES
   =============================== */

/* Buy AINT Container */
.buy-aint-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
	color: #d4d5f1;
	font-family: "DM Sans", serif;
}

/* Hero Section */
.buy-aint-hero {
	padding: 60px 24px 40px;
	text-align: center;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-bottom: 1px solid rgba(255, 59, 212, 0.2);
	position: relative;
	overflow: hidden;
}

.buy-aint-hero::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(
		circle at 50% 50%,
		rgba(255, 59, 212, 0.1) 0%,
		transparent 70%
	);
	pointer-events: none;
}

.hero-content {
	max-width: 1000px;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}

.hero-badge {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.2) 0%,
		rgba(113, 48, 195, 0.2) 100%
	);
	border: 1px solid rgba(255, 59, 212, 0.3);
	padding: 8px 16px;
	border-radius: 50px;
	font-size: 14px;
	font-weight: 500;
	color: #ff3bd4;
	margin-bottom: 24px;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.badge-icon {
	width: 16px;
	height: 16px;
}

.hero-title {
	font-size: 48px;
	font-weight: 700;
	color: #ccceef;
	margin-bottom: 16px;
	text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
	font-family: "Roobert PRO Bold", serif;
}

.hero-description {
	font-size: 18px;
	color: #d4d5f1;
	margin-bottom: 40px;
	line-height: 1.6;
	opacity: 0.9;
}

.hero-stats {
	display: flex;
	justify-content: center;
	gap: 40px;
	flex-wrap: wrap;
}

.hero-stat {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	min-width: 120px;
}

.stat-icon {
	width: 24px;
	height: 24px;
	color: #ff3bd4;
}

.stat-label {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	font-weight: 500;
}

.stat-value {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
}

.stat-value.positive {
	color: #22c55e;
}

/* Main Content */
.buy-aint-content {
	display: grid;
	grid-template-columns: 2fr 1fr;
	gap: 32px;
	max-width: 1400px;
	margin: 0 auto;
	padding: 40px 24px;
}

/* Purchase Section */
.purchase-section {
	display: flex;
	flex-direction: column;
}

.purchase-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 32px;
	box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.purchase-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(90deg, #ff3bd4 0%, #7130c3 100%);
}

.card-header-buy {
	display: flex;
	align-items: center;
	gap: 16px;
	margin-bottom: 32px;
	padding-bottom: 24px;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.header-icon {
	width: 48px;
	height: 48px;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #d4d5f1;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.header-text h2 {
	font-size: 24px;
	font-weight: 700;
	color: #ccceef;
	margin: 0 0 4px 0;
	font-family: "Roobert PRO Bold", serif;
}

.header-text p {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

/* Form Styles */
.purchase-form {
	display: flex;
	flex-direction: column;
	gap: 32px;
}

.form-group-buy {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.form-label-buy {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin-bottom: 4px;
}

/* Amount Input */
.amount-input-container {
	position: relative;
	display: flex;
	align-items: center;
	background: rgba(255, 59, 212, 0.05);
	border: 2px solid rgba(255, 59, 212, 0.3);
	border-radius: 12px;
	overflow: hidden;
	transition: all 0.3s ease;
}

.amount-input-container:hover {
	border-color: rgba(255, 59, 212, 0.5);
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.1);
}

.amount-input-container:focus-within {
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.2);
	background: rgba(255, 59, 212, 0.1);
}

.currency-prefix,
.currency-suffix {
	padding: 16px;
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	font-weight: 600;
	font-size: 18px;
	border-right: 1px solid rgba(255, 59, 212, 0.2);
}

.currency-suffix {
	border-right: none;
	border-left: 1px solid rgba(255, 59, 212, 0.2);
}

.amount-input {
	flex: 1;
	padding: 16px 20px;
	border: none;
	background: transparent;
	color: #d4d5f1;
	font-size: 18px;
	font-weight: 500;
	outline: none;
	text-align: center;
}

.amount-input::placeholder {
	color: rgba(212, 213, 241, 0.4);
}

/* Conversion Info */
.conversion-info {
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 8px;
	padding: 16px;
	margin-top: 12px;
}

.conversion-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
	font-size: 14px;
}

.conversion-row:last-child {
	margin-bottom: 0;
}

.conversion-row span:first-child {
	color: rgba(212, 213, 241, 0.7);
}

.aint-amount {
	color: #ff3bd4;
	font-weight: 600;
	font-size: 16px;
}

.gold-amount {
	color: #ffd700;
	font-weight: 600;
}

/* Payment Methods Grid */
.payment-methods-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: 16px;
}

.payment-method-card {
	background: rgba(255, 59, 212, 0.05);
	border: 2px solid rgba(255, 59, 212, 0.2);
	border-radius: 12px;
	padding: 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.payment-method-card:hover {
	border-color: rgba(255, 59, 212, 0.4);
	background: rgba(255, 59, 212, 0.08);
	transform: translateY(-2px);
	box-shadow: 0 8px 24px rgba(255, 59, 212, 0.15);
}

.payment-method-card.selected {
	border-color: #ff3bd4;
	background: rgba(255, 59, 212, 0.12);
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.25);
}

.payment-method-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.payment-icon {
	width: 24px;
	height: 24px;
	color: #ff3bd4;
}

.payment-fee {
	font-size: 12px;
	padding: 4px 8px;
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border-radius: 4px;
	font-weight: 500;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.payment-method-info h4 {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.payment-method-info p {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

.payment-method-indicator {
	position: absolute;
	top: 12px;
	right: 12px;
}

.check-icon {
	width: 20px;
	height: 20px;
	color: #22c55e;
}

/* Bank Select */
.bank-select-container {
	position: relative;
}

.bank-select {
	width: 100%;
	padding: 16px 20px;
	background: rgba(255, 59, 212, 0.05);
	border: 2px solid rgba(255, 59, 212, 0.3);
	border-radius: 12px;
	color: #d4d5f1;
	font-size: 16px;
	font-weight: 500;
	outline: none;
	transition: all 0.3s ease;
	cursor: pointer;
	appearance: none;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23FF3BD4' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
	background-position: right 16px center;
	background-repeat: no-repeat;
	background-size: 16px;
}

.bank-select:hover {
	border-color: rgba(255, 59, 212, 0.5);
	background: rgba(255, 59, 212, 0.08);
}

.bank-select:focus {
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.2);
	background: rgba(255, 59, 212, 0.1);
}

/* Purchase Button */
.purchase-button-container {
	margin-top: 8px;
}

.purchase-button {
	width: 100%;
	padding: 18px 32px;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	border: none;
	border-radius: 12px;
	font-size: 18px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.4);
	position: relative;
	overflow: hidden;
}

.purchase-button:hover:not(.disabled) {
	transform: translateY(-3px);
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.6);
	background: linear-gradient(135deg, #7130c3 0%, #ff3bd4 100%);
}

.purchase-button.disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.button-content,
.button-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
}

.button-icon,
.loading-icon {
	width: 20px;
	height: 20px;
}

.loading-icon {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Sidebar Section */
.sidebar-section {
	display: flex;
	flex-direction: column;
	gap: 24px;
}

/* Price Card */
.price-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
}

.price-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.price-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 16px;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.price-header h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0;
}

.price-live-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 12px;
	color: #22c55e;
	font-weight: 500;
}

.live-dot {
	width: 8px;
	height: 8px;
	background: #22c55e;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}

.price-details {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
}

.price-row.main-price {
	font-size: 16px;
	font-weight: 600;
	padding: 12px 0;
	border-top: 1px solid rgba(255, 59, 212, 0.1);
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.price-label {
	color: rgba(212, 213, 241, 0.7);
}

.price-value {
	color: #d4d5f1;
	font-weight: 500;
}

.price-value.primary {
	color: #ff3bd4;
	font-size: 20px;
	font-weight: 700;
}

.price-calculation {
	margin-top: 8px;
	text-align: center;
}

.price-calculation small {
	color: rgba(212, 213, 241, 0.5);
	font-size: 12px;
	font-style: italic;
}

/* Features Card */
.features-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
}

.features-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.features-card h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 20px 0;
	text-align: center;
}

.features-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.feature-item {
	display: flex;
	align-items: flex-start;
	gap: 12px;
	padding: 12px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
	transition: all 0.3s ease;
}

.feature-item:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.2);
}

.feature-icon {
	width: 20px;
	height: 20px;
	color: #ff3bd4;
	margin-top: 2px;
	flex-shrink: 0;
}

.feature-content h4 {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.feature-content p {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
	line-height: 1.4;
}

/* Trust Card */
.trust-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
}

.trust-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.trust-card h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 20px 0;
	text-align: center;
}

.trust-indicators {
	display: flex;
	/* flex-direction: column; */
	gap: 12px;
}

.trust-item {
	display: flex;
	justify-content: center;
}

.trust-badge {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px 16px;
	border-radius: 8px;
	font-size: 12px;
	font-weight: 500;
	transition: all 0.3s ease;
}

.trust-badge.verified {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.trust-badge.secure {
	background: linear-gradient(
		135deg,
		rgba(59, 130, 246, 0.2) 0%,
		rgba(59, 130, 246, 0.1) 100%
	);
	color: #3b82f6;
	border: 1px solid rgba(59, 130, 246, 0.3);
}

.trust-badge.regulated {
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.2) 0%,
		rgba(113, 48, 195, 0.2) 100%
	);
	color: #ff3bd4;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.trust-icon {
	width: 16px;
	height: 16px;
}

/* Responsive Design for Buy AINT */
@media (max-width: 1200px) {
	.buy-aint-content {
		grid-template-columns: 1fr;
		gap: 32px;
	}

	.sidebar-section {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 24px;
	}
}

@media (max-width: 768px) {
	.hero-title {
		font-size: 36px;
	}

	.hero-description {
		font-size: 16px;
	}

	.hero-stats {
		gap: 24px;
	}

	.buy-aint-content {
		padding: 24px 16px;
	}

	.purchase-card {
		padding: 24px;
	}

	.payment-methods-grid {
		grid-template-columns: 1fr;
	}

	.sidebar-section {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 640px) {
	.buy-aint-hero {
		padding: 40px 16px 32px;
	}

	.hero-title {
		font-size: 28px;
	}

	.hero-stats {
		flex-direction: column;
		gap: 16px;
	}

	.hero-stat {
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		min-width: auto;
		width: 100%;
		max-width: 200px;
		margin: 0 auto;
	}

	.card-header-buy {
		flex-direction: column;
		text-align: center;
		gap: 12px;
	}

	.amount-input {
		font-size: 16px;
	}

	.purchase-button {
		font-size: 16px;
		padding: 16px 24px;
	}
}

/* ____________wallet css */

/* Additional Professional Effects */
.professional-gradient {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 50%, #0f1021 100%);
}

.purple-accent {
	border-left: 3px solid #ff3bd4;
}

.shadow-purple {
	box-shadow: 0 4px 20px rgba(255, 59, 212, 0.15);
}

.text-purple {
	color: #ff3bd4;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.border-purple {
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.hover-lift {
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(255, 59, 212, 0.2);
}

select {
	background: rgba(
		255,
		59,
		212,
		0.05
	) !important; /* light background with bs-primary tint */
	color: #d4d5f1 !important; /* body text color */
	border: 1px solid rgba(255, 59, 212, 0.3) !important; /* subtle primary border */
}

select:focus {
	box-shadow: 0 0 0 2px rgba(255, 59, 212, 0.2) !important;
}

option {
	background-color: #211831;
	color: #fff;
	padding: 10px;
	border-radius: 20px;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar-track {
	background: rgba(255, 59, 212, 0.05); /* Same as select background */
}

::-webkit-scrollbar-thumb {
	background-color: rgba(255, 59, 212, 0.3); /* Subtle primary tint */
	border-radius: 10px;
	border: 2px solid rgba(255, 59, 212, 0.1); /* Optional border for better contrast */
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(255, 59, 212, 0.5);
}

/* Firefox Scrollbar (optional) */
* {
	scrollbar-color: rgba(255, 59, 212, 0.3) rgba(255, 59, 212, 0.05);
	scrollbar-width: thin;
}

/* ===============================
   BUY AINT PAGE STYLES
   =============================== */

/* Buy AINT Container */
.buy-aint-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
	color: #d4d5f1;
	font-family: "DM Sans", serif;
}

/* Hero Section */
.buy-aint-hero {
	padding: 60px 24px 40px;
	text-align: center;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-bottom: 1px solid rgba(255, 59, 212, 0.2);
	position: relative;
	overflow: hidden;
}

.buy-aint-hero::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(
		circle at 50% 50%,
		rgba(255, 59, 212, 0.1) 0%,
		transparent 70%
	);
	pointer-events: none;
}

.hero-content {
	max-width: 1000px;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}

.hero-badge {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.2) 0%,
		rgba(113, 48, 195, 0.2) 100%
	);
	border: 1px solid rgba(255, 59, 212, 0.3);
	padding: 8px 16px;
	border-radius: 50px;
	font-size: 14px;
	font-weight: 500;
	color: #ff3bd4;
	margin-bottom: 24px;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.badge-icon {
	width: 16px;
	height: 16px;
}

.hero-title {
	font-size: 48px;
	font-weight: 700;
	color: #ccceef;
	margin-bottom: 16px;
	text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
	font-family: "Roobert PRO Bold", serif;
}

.hero-description {
	font-size: 18px;
	color: #d4d5f1;
	margin-bottom: 40px;
	line-height: 1.6;
	opacity: 0.9;
}

.hero-stats {
	display: flex;
	justify-content: center;
	gap: 40px;
	flex-wrap: wrap;
}

.hero-stat {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	min-width: 120px;
}

.stat-icon {
	width: 24px;
	height: 24px;
	color: #ff3bd4;
}

.stat-label {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	font-weight: 500;
}

.stat-value {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
}

.stat-value.positive {
	color: #22c55e;
}

/* Main Content */
.buy-aint-content {
	display: grid;
	grid-template-columns: 2fr 1fr;
	gap: 32px;
	max-width: 1400px;
	margin: 0 auto;
	padding: 40px 24px;
}

/* Purchase Section */
.purchase-section {
	display: flex;
	flex-direction: column;
}

.purchase-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 32px;
	box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	margin-bottom: 24px;
}

.purchase-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(90deg, #ff3bd4 0%, #7130c3 100%);
}

.card-header-buy {
	display: flex;
	align-items: center;
	gap: 16px;
	margin-bottom: 32px;
	padding-bottom: 24px;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.header-icon {
	width: 48px;
	height: 48px;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #d4d5f1;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.header-text h2 {
	font-size: 24px;
	font-weight: 700;
	color: #ccceef;
	margin: 0 0 4px 0;
	font-family: "Roobert PRO Bold", serif;
}

.header-text p {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

/* Form Styles */
.purchase-form {
	display: flex;
	flex-direction: column;
	gap: 32px;
}

.form-group-buy {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.form-label-buy {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin-bottom: 4px;
}

/* Amount Input */
.amount-input-container {
	position: relative;
	display: flex;
	align-items: center;
	background: rgba(255, 59, 212, 0.05);
	border: 2px solid rgba(255, 59, 212, 0.3);
	border-radius: 12px;
	overflow: hidden;
	transition: all 0.3s ease;
}

.amount-input-container:hover {
	border-color: rgba(255, 59, 212, 0.5);
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.1);
}

.amount-input-container:focus-within {
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.2);
	background: rgba(255, 59, 212, 0.1);
}

.currency-prefix,
.currency-suffix {
	padding: 16px;
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	font-weight: 600;
	font-size: 18px;
	border-right: 1px solid rgba(255, 59, 212, 0.2);
}

.currency-suffix {
	border-right: none;
	border-left: 1px solid rgba(255, 59, 212, 0.2);
}

.amount-input {
	flex: 1;
	padding: 16px 20px;
	border: none;
	background: transparent;
	color: #d4d5f1;
	font-size: 18px;
	font-weight: 500;
	outline: none;
	text-align: center;
}

.amount-input::placeholder {
	color: rgba(212, 213, 241, 0.4);
}

/* Conversion Info */
.conversion-info {
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 8px;
	padding: 16px;
	margin-top: 12px;
}

.conversion-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
	font-size: 14px;
}

.conversion-row:last-child {
	margin-bottom: 0;
}

.conversion-row span:first-child {
	color: rgba(212, 213, 241, 0.7);
}

.aint-amount {
	color: #ff3bd4;
	font-weight: 600;
	font-size: 16px;
}

.gold-amount {
	color: #ffd700;
	font-weight: 600;
}

/* Payment Methods Grid */
.payment-methods-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: 16px;
}

.payment-method-card {
	background: rgba(255, 59, 212, 0.05);
	border: 2px solid rgba(255, 59, 212, 0.2);
	border-radius: 12px;
	padding: 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.payment-method-card:hover {
	border-color: rgba(255, 59, 212, 0.4);
	background: rgba(255, 59, 212, 0.08);
	transform: translateY(-2px);
	box-shadow: 0 8px 24px rgba(255, 59, 212, 0.15);
}

.payment-method-card.selected {
	border-color: #ff3bd4;
	background: rgba(255, 59, 212, 0.12);
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.25);
}

.payment-method-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.payment-icon {
	width: 24px;
	height: 24px;
	color: #ff3bd4;
}

.payment-fee {
	font-size: 12px;
	padding: 4px 8px;
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border-radius: 4px;
	font-weight: 500;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.payment-method-info h4 {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.payment-method-info p {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

.payment-method-indicator {
	position: absolute;
	top: 12px;
	right: 12px;
}

.check-icon {
	width: 20px;
	height: 20px;
	color: #22c55e;
}

/* Bank Select */
.bank-select-container {
	position: relative;
}

.bank-select {
	width: 100%;
	padding: 16px 20px;
	background: rgba(255, 59, 212, 0.05);
	border: 2px solid rgba(255, 59, 212, 0.3);
	border-radius: 12px;
	color: #d4d5f1;
	font-size: 16px;
	font-weight: 500;
	outline: none;
	transition: all 0.3s ease;
	cursor: pointer;
	appearance: none;
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23FF3BD4' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
	background-position: right 16px center;
	background-repeat: no-repeat;
	background-size: 16px;
}

.bank-select:hover {
	border-color: rgba(255, 59, 212, 0.5);
	background: rgba(255, 59, 212, 0.08);
}

.bank-select:focus {
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.2);
	background: rgba(255, 59, 212, 0.1);
}

/* Purchase Button */
.purchase-button-container {
	margin-top: 8px;
}

.purchase-button {
	width: 100%;
	padding: 18px 32px;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	border: none;
	border-radius: 12px;
	font-size: 18px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.4);
	position: relative;
	overflow: hidden;
}

.purchase-button:hover:not(.disabled) {
	transform: translateY(-3px);
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.6);
	background: linear-gradient(135deg, #7130c3 0%, #ff3bd4 100%);
}

.purchase-button.disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.button-content,
.button-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
}

.button-icon,
.loading-icon {
	width: 20px;
	height: 20px;
}

.loading-icon {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Sidebar Section */
.sidebar-section {
	display: flex;
	flex-direction: column;
	gap: 24px;
}

/* Price Card */
.price-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
}

.price-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.price-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 16px;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.price-header h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0;
}

.price-live-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 12px;
	color: #22c55e;
	font-weight: 500;
}

.live-dot {
	width: 8px;
	height: 8px;
	background: #22c55e;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}

.price-details {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
}

.price-row.main-price {
	font-size: 16px;
	font-weight: 600;
	padding: 12px 0;
	border-top: 1px solid rgba(255, 59, 212, 0.1);
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.price-label {
	color: rgba(212, 213, 241, 0.7);
}

.price-value {
	color: #d4d5f1;
	font-weight: 500;
}

.price-value.primary {
	color: #ff3bd4;
	font-size: 20px;
	font-weight: 700;
}

.price-calculation {
	margin-top: 8px;
	text-align: center;
}

.price-calculation small {
	color: rgba(212, 213, 241, 0.5);
	font-size: 12px;
	font-style: italic;
}

/* Features Card */
.features-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
}

.features-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.features-card h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 20px 0;
	text-align: center;
}

.features-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.feature-item {
	display: flex;
	align-items: flex-start;
	gap: 12px;
	padding: 12px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
	transition: all 0.3s ease;
}

.feature-item:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.2);
}

.feature-icon {
	width: 20px;
	height: 20px;
	color: #ff3bd4;
	margin-top: 2px;
	flex-shrink: 0;
}

.feature-content h4 {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.feature-content p {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
	line-height: 1.4;
}

/* Trust Card */
.trust-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
}

.trust-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.trust-card h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 20px 0;
	text-align: center;
}

.trust-indicators {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.trust-item {
	display: flex;
	justify-content: center;
}

.trust-badge {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px 16px;
	border-radius: 8px;
	font-size: 12px;
	font-weight: 500;
	transition: all 0.3s ease;
}

.trust-badge.verified {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.trust-badge.secure {
	background: linear-gradient(
		135deg,
		rgba(59, 130, 246, 0.2) 0%,
		rgba(59, 130, 246, 0.1) 100%
	);
	color: #3b82f6;
	border: 1px solid rgba(59, 130, 246, 0.3);
}

.trust-badge.regulated {
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.2) 0%,
		rgba(113, 48, 195, 0.2) 100%
	);
	color: #ff3bd4;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.trust-icon {
	width: 16px;
	height: 16px;
}

/* ===============================
   WALLET PAGE STYLES
   =============================== */

/* Wallet Overview */
.wallet-overview {
	margin-bottom: 32px;
}

.balance-card {
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.1) 0%,
		rgba(113, 48, 195, 0.1) 100%
	);
	border: 2px solid rgba(255, 59, 212, 0.3);
	border-radius: 16px;
	padding: 24px;
	text-align: center;
	position: relative;
	overflow: hidden;
}

.balance-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #ff3bd4 0%, #7130c3 100%);
}

.balance-header {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 12px;
	margin-bottom: 16px;
}

.balance-label {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.visibility-toggle {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	padding: 6px;
	cursor: pointer;
	color: #ff3bd4;
	transition: all 0.3s ease;
}

.visibility-toggle:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

.balance-amount {
	font-size: 36px;
	font-weight: 700;
	color: #ccceef;
	margin-bottom: 12px;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
	font-family: "Roobert PRO Bold", serif;
}

.balance-change {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6px;
	font-size: 14px;
	font-weight: 600;
	padding: 6px 12px;
	border-radius: 8px;
	margin: 0 auto;
	width: fit-content;
}

.balance-change.positive {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.balance-change.negative {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	color: #ef4444;
	border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Wallet Actions */
.wallet-actions-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 16px;
}

.wallet-action-btn {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 16px 20px;
	border-radius: 12px;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: left;
	position: relative;
	overflow: hidden;
}

.wallet-action-btn.primary {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	color: #d4d5f1;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.wallet-action-btn.primary:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.5);
	background: linear-gradient(135deg, #7130c3 0%, #ff3bd4 100%);
}

.wallet-action-btn.secondary {
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.wallet-action-btn.secondary:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
	transform: translateY(-3px);
	box-shadow: 0 8px 24px rgba(255, 59, 212, 0.3);
}

.action-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
}

.action-content {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.action-title {
	font-size: 16px;
	font-weight: 600;
	line-height: 1.2;
}

.action-desc {
	font-size: 12px;
	opacity: 0.8;
	line-height: 1.2;
}

/* Token Holdings */
.token-holdings {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.token-card {
	align-items: center;
	gap: 16px;
	padding: 20px;
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 12px;
	transition: all 0.3s ease;
}

.token-card:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.3);
	transform: translateY(-2px);
	box-shadow: 0 8px 24px rgba(255, 59, 212, 0.15);
}

.token-info {
	display: flex;
	align-items: center;
	gap: 12px;
	flex: 1;
}

.token-icon {
	width: 48px;
	height: 48px;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.token-symbol {
	font-size: 14px;
	font-weight: 700;
	color: #d4d5f1;
}

.gold-badge {
	position: absolute;
	top: -4px;
	right: -4px;
	width: 16px;
	height: 16px;
	color: #ffd700;
	background: rgba(255, 215, 0, 0.2);
	border-radius: 50%;
	padding: 2px;
}

.token-details h4 {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.token-balance {
	font-size: 14px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

.token-value {
	text-align: right;
	min-width: 120px;
}

.value-amount {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin-bottom: 4px;
}

.value-change {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 4px;
	font-size: 12px;
	font-weight: 500;
}

.value-change.positive {
	color: #22c55e;
}

.value-change.negative {
	color: #ef4444;
}

.token-price {
	text-align: right;
	min-width: 100px;
}

.price-label {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.5);
	margin-bottom: 2px;
}

.price-value {
	font-size: 14px;
	font-weight: 500;
	color: #d4d5f1;
}

/* Transaction Cards */
.transaction-card {
	display: flex;
	align-items: center;
	gap: 16px;
	padding: 16px;
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 12px;
	transition: all 0.3s ease;
}

.transaction-card:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.3);
	transform: translateX(5px);
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.transaction-icon {
	width: 40px;
	height: 40px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.tx-icon {
	width: 20px;
	height: 20px;
}

.tx-icon.buy {
	color: #22c55e;
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	border: 1px solid rgba(34, 197, 94, 0.3);
	border-radius: 10px;
	padding: 10px;
}

.tx-icon.send {
	color: #ef4444;
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	border: 1px solid rgba(239, 68, 68, 0.3);
	border-radius: 10px;
	padding: 10px;
}

.tx-icon.receive {
	color: #3b82f6;
	background: linear-gradient(
		135deg,
		rgba(59, 130, 246, 0.2) 0%,
		rgba(59, 130, 246, 0.1) 100%
	);
	border: 1px solid rgba(59, 130, 246, 0.3);
	border-radius: 10px;
	padding: 10px;
}

.transaction-details {
	flex: 1;
}

.tx-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4px;
}

.tx-type {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
}

.tx-amount {
	font-size: 16px;
	font-weight: 600;
	color: #ff3bd4;
}

.tx-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.tx-time {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.6);
}

.tx-value {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
}

.transaction-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8px;
}

.status-badge {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 4px 8px;
	border-radius: 6px;
	font-size: 11px;
	font-weight: 500;
	text-transform: uppercase;
}

.status-badge.completed {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge.pending {
	background: linear-gradient(
		135deg,
		rgba(255, 193, 7, 0.2) 0%,
		rgba(255, 193, 7, 0.1) 100%
	);
	color: #ffc107;
	border: 1px solid rgba(255, 193, 7, 0.3);
}

.tx-actions {
	display: flex;
	gap: 4px;
}

.tx-action-btn {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 4px;
	padding: 4px;
	cursor: pointer;
	color: #ff3bd4;
	transition: all 0.3s ease;
}

.tx-action-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

/* Wallet Address */
.wallet-address {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.address-display {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px 16px;
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 8px;
}

.address-text {
	flex: 1;
	font-family: "Monaco", "Menlo", monospace;
	font-size: 14px;
	color: #d4d5f1;
}

.copy-btn {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	padding: 6px;
	cursor: pointer;
	color: #ff3bd4;
	transition: all 0.3s ease;
}

.copy-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

.address-qr {
	display: flex;
	justify-content: center;
}

.qr-placeholder {
	width: 120px;
	height: 120px;
	background: rgba(255, 59, 212, 0.05);
	border: 2px dashed rgba(255, 59, 212, 0.3);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: rgba(212, 213, 241, 0.5);
	font-size: 12px;
}

/* Portfolio Stats */
.portfolio-stats {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.stat-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
	transition: all 0.3s ease;
}

.stat-item:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.2);
}

.stat-icon-wrapper {
	width: 36px;
	height: 36px;
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.stat-content h4 {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 2px 0;
}

.stat-content p {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

/* Responsive Design for Wallet */
@media (max-width: 1200px) {
	.buy-aint-content {
		grid-template-columns: 1fr;
		gap: 32px;
	}

	.sidebar-section {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 24px;
	}
}

@media (max-width: 768px) {
	.hero-title {
		font-size: 36px;
	}

	.hero-description {
		font-size: 16px;
	}

	.hero-stats {
		gap: 24px;
	}

	.buy-aint-content {
		padding: 24px 16px;
	}

	.purchase-card {
		padding: 24px;
	}

	.wallet-actions-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.sidebar-section {
		grid-template-columns: 1fr;
	}

	.balance-amount {
		font-size: 28px;
	}

	.token-card {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.token-value,
	.token-price {
		text-align: left;
		min-width: auto;
	}
}

@media (max-width: 640px) {
	.buy-aint-hero {
		padding: 40px 16px 32px;
	}

	.hero-title {
		font-size: 28px;
	}

	.hero-stats {
		flex-direction: column;
		gap: 16px;
	}

	.hero-stat {
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		min-width: auto;
		width: 100%;
		max-width: 200px;
		margin: 0 auto;
	}

	.card-header-buy {
		flex-direction: column;
		text-align: center;
		gap: 12px;
	}

	.wallet-actions-grid {
		grid-template-columns: 1fr;
	}

	.balance-amount {
		font-size: 24px;
	}

	.transaction-card {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.transaction-status {
		align-items: flex-start;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
	}
}

/* ===============================
   CHART PAGE STYLES
   =============================== */

/* Chart Container */
.chart-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
	color: #d4d5f1;
	font-family: "DM Sans", serif;
}

/* Hero Section */
.chart-hero {
	padding: 60px 24px 40px;
	text-align: center;
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-bottom: 1px solid rgba(255, 59, 212, 0.2);
	position: relative;
	overflow: hidden;
}

.chart-hero::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(
		circle at 50% 50%,
		rgba(255, 59, 212, 0.1) 0%,
		transparent 70%
	);
	pointer-events: none;
}

/* Price Ticker */
.price-ticker {
	display: flex;
	justify-content: center;
	gap: 32px;
	margin: 32px 0;
	padding: 20px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 12px;
	border: 1px solid rgba(255, 59, 212, 0.2);
	overflow-x: auto;
	animation: tickerGlow 3s ease-in-out infinite alternate;
}

@keyframes tickerGlow {
	0% {
		box-shadow: 0 4px 20px rgba(255, 59, 212, 0.1);
	}
	100% {
		box-shadow: 0 8px 40px rgba(255, 59, 212, 0.2);
	}
}

.ticker-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4px;
	min-width: 120px;
	animation: fadeInUp 0.6s ease-out;
}

.ticker-symbol {
	font-size: 14px;
	font-weight: 600;
	color: #ff3bd4;
	text-transform: uppercase;
}

.ticker-price {
	font-size: 18px;
	font-weight: 700;
	color: #ccceef;
}

.ticker-change {
	font-size: 12px;
	font-weight: 500;
	padding: 2px 8px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	gap: 4px;
}

.ticker-change.positive {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.ticker-change.negative {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	color: #ef4444;
	border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Main Content */
.chart-content {
	display: grid;
	grid-template-columns: 1fr 350px;
	gap: 32px;
	max-width: 1600px;
	margin: 0 auto;
	padding: 40px 24px;
}

.chart-main {
	display: flex;
	flex-direction: column;
	gap: 24px;
}

/* Chart Controls */
.chart-controls-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	animation: slideInDown 0.6s ease-out;
}

.controls-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 24px;
	flex-wrap: wrap;
}

.token-selector h3 {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 12px 0;
}

.token-buttons {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.token-btn {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 8px 16px;
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 8px;
	color: #d4d5f1;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.token-btn:hover {
	background: rgba(255, 59, 212, 0.15);
	border-color: rgba(255, 59, 212, 0.5);
	transform: translateY(-2px);
}

.token-btn.active {
	background: linear-gradient(135deg, #ff3bd4 0%, #7130c3 100%);
	border-color: #ff3bd4;
	color: #d4d5f1;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.3);
}

.gold-icon {
	width: 14px;
	height: 14px;
	color: #ffd700;
}

.chart-options {
	display: flex;
	align-items: center;
	gap: 16px;
	flex-wrap: wrap;
}

.timeframe-selector {
	display: flex;
	gap: 4px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	padding: 4px;
	border: 1px solid rgba(255, 59, 212, 0.2);
}

.timeframe-btn {
	padding: 6px 12px;
	background: transparent;
	border: none;
	border-radius: 4px;
	color: rgba(212, 213, 241, 0.7);
	font-size: 12px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.timeframe-btn:hover {
	background: rgba(255, 59, 212, 0.1);
	color: #ff3bd4;
}

.timeframe-btn.active {
	background: #ff3bd4;
	color: #d4d5f1;
	box-shadow: 0 2px 8px rgba(255, 59, 212, 0.3);
}

.chart-type-selector {
	display: flex;
	gap: 4px;
}

.chart-type-btn {
	padding: 8px;
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	color: #d4d5f1;
	cursor: pointer;
	transition: all 0.3s ease;
}

.chart-type-btn:hover {
	background: rgba(255, 59, 212, 0.15);
	border-color: rgba(255, 59, 212, 0.5);
}

.chart-type-btn.active {
	background: #ff3bd4;
	border-color: #ff3bd4;
	box-shadow: 0 2px 8px rgba(255, 59, 212, 0.3);
}

.realtime-toggle {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 8px 12px;
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	color: #d4d5f1;
	font-size: 12px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.realtime-toggle:hover {
	background: rgba(255, 59, 212, 0.15);
	border-color: rgba(255, 59, 212, 0.5);
}

.realtime-toggle.active {
	background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
	border-color: #22c55e;
	box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

/* Price Header */
.price-header-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	display: flex;
	justify-content: space-between;
	align-items: center;
	animation: slideInLeft 0.6s ease-out;
}

.price-info {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.token-title {
	display: flex;
	align-items: center;
	gap: 12px;
}

.token-title h2 {
	font-size: 24px;
	font-weight: 700;
	color: #ccceef;
	margin: 0;
	font-family: "Roobert PRO Bold", serif;
}

.token-symbol {
	font-size: 14px;
	font-weight: 600;
	color: #ff3bd4;
	background: rgba(255, 59, 212, 0.1);
	padding: 4px 8px;
	border-radius: 4px;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.gold-backed-badge {
	display: flex;
	align-items: center;
	gap: 4px;
	background: linear-gradient(
		135deg,
		rgba(255, 215, 0, 0.2) 0%,
		rgba(255, 215, 0, 0.1) 100%
	);
	color: #ffd700;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
	border: 1px solid rgba(255, 215, 0, 0.3);
}

.price-display {
	display: flex;
	align-items: baseline;
	gap: 16px;
}

.current-price {
	font-size: 36px;
	font-weight: 700;
	color: #ff3bd4;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
	animation: priceUpdate 0.5s ease-out;
}

@keyframes priceUpdate {
	0% {
		transform: scale(1.05);
	}
	100% {
		transform: scale(1);
	}
}

.price-change {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: 16px;
	font-weight: 600;
}

.price-change.positive {
	color: #22c55e;
}

.price-change.negative {
	color: #ef4444;
}

.timeframe-label {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.6);
	font-weight: 400;
}

.price-stats {
	display: flex;
	flex-direction: column;
	gap: 12px;
	text-align: right;
}

.stat-item {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.stat-label {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.6);
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.stat-value {
	font-size: 16px;
	font-weight: 600;
	color: #ccceef;
}

/* Enhanced Chart Area */
.chart-area-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	animation: slideInUp 0.6s ease-out;
	position: relative;
	overflow: hidden;
}

.chart-area-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1px;
	background: linear-gradient(90deg, transparent, #ff3bd4, transparent);
	animation: chartGlow 2s ease-in-out infinite alternate;
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	position: relative;
	z-index: 2;
}

.chart-header h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0;
	display: flex;
	align-items: center;
	gap: 8px;
}

.chart-header h3::before {
	content: "📊";
	font-size: 16px;
}

.chart-actions {
	display: flex;
	gap: 8px;
}

.chart-action-btn {
	padding: 8px;
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	color: #d4d5f1;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.chart-action-btn::before {
	content: "";
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(255, 255, 255, 0.1),
		transparent
	);
	transition: left 0.5s;
}

.chart-action-btn:hover::before {
	left: 100%;
}

.chart-action-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(255, 59, 212, 0.3);
}

.chart-canvas {
	height: 400px;
	position: relative;
	border-radius: 12px;
	overflow: hidden;
	background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.chart-canvas::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(
			circle at 20% 80%,
			rgba(255, 59, 212, 0.1) 0%,
			transparent 50%
		),
		radial-gradient(
			circle at 80% 20%,
			rgba(0, 212, 255, 0.1) 0%,
			transparent 50%
		);
	pointer-events: none;
}

/* Chart Grid Lines */
.chart-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	opacity: 0.1;
}

.chart-grid-line {
	position: absolute;
	left: 0;
	right: 0;
	height: 1px;
	background: linear-gradient(90deg, transparent, #ff3bd4, transparent);
}

.chart-grid-line:nth-child(1) {
	top: 20%;
}
.chart-grid-line:nth-child(2) {
	top: 40%;
}
.chart-grid-line:nth-child(3) {
	top: 60%;
}
.chart-grid-line:nth-child(4) {
	top: 80%;
}

.chart-placeholder {
	width: 100%;
	height: 100%;
	background: linear-gradient(
		135deg,
		rgba(15, 16, 33, 0.5) 0%,
		rgba(26, 26, 46, 0.5) 100%
	);
	border: 1px solid rgba(255, 59, 212, 0.1);
	border-radius: 8px;
	position: relative;
	overflow: hidden;
}

.chart-animation {
	display: flex;
	align-items: end;
	justify-content: space-between;
	height: 100%;
	padding: 20px;
	gap: 3px;
	z-index: 2;
}

.chart-bar {
	background: linear-gradient(to top, #ff3bd4, #7130c3);
	border-radius: 3px 3px 0 0;
	min-height: 10px;
	flex: 1;
	max-width: 8px;
	animation: chartBarGrow 1s ease-out forwards;
	opacity: 0.8;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(255, 59, 212, 0.3);
}

.chart-bar:hover {
	opacity: 1;
	transform: scaleY(1.1);
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.5);
}

.chart-line {
	background: linear-gradient(to top, #00d4ff, #0099cc);
	border-radius: 0;
	box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
}

.chart-line:hover {
	box-shadow: 0 4px 16px rgba(0, 212, 255, 0.5);
}

@keyframes chartBarGrow {
	from {
		height: 0;
		opacity: 0;
	}
	to {
		opacity: 0.8;
	}
}

@keyframes chartGlow {
	from {
		box-shadow: 0 0 5px rgba(255, 59, 212, 0.3);
	}
	to {
		box-shadow: 0 0 20px rgba(255, 59, 212, 0.6);
	}
}

.chart-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	color: rgba(212, 213, 241, 0.4);
	pointer-events: none;
	background: rgba(26, 26, 46, 0.9);
	border-radius: 12px;
	padding: 2rem;
	border: 1px solid rgba(255, 59, 212, 0.2);
	backdrop-filter: blur(10px);
}

.chart-icon {
	color: rgba(255, 59, 212, 0.6);
	margin-bottom: 1rem;
	filter: drop-shadow(0 4px 8px rgba(255, 59, 212, 0.3));
}

.chart-overlay p {
	color: #d4d5f1;
	font-size: 1.1rem;
	margin: 0 0 0.5rem 0;
	font-weight: 500;
}

.chart-timeframe {
	color: #8b8b9f;
	font-size: 0.875rem;
	background: rgba(255, 59, 212, 0.1);
	padding: 0.25rem 0.75rem;
	border-radius: 20px;
	border: 1px solid rgba(255, 59, 212, 0.2);
}

.chart-overlay .live-indicator {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	margin-top: 0.5rem;
	font-size: 0.75rem;
	color: #00d4ff;
}

.chart-overlay .live-dot {
	width: 8px;
	height: 8px;
	background: #00d4ff;
	border-radius: 50%;
	animation: pulse 1.5s ease-in-out infinite;
}

/* Chart Tooltip */
.chart-tooltip {
	position: absolute;
	background: rgba(26, 26, 46, 0.95);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 8px;
	padding: 0.75rem;
	color: #d4d5f1;
	font-size: 0.875rem;
	pointer-events: none;
	z-index: 100;
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
	transform: translateX(-50%);
	opacity: 0;
	transition: opacity 0.3s ease;
	min-width: 120px;
}

.chart-tooltip.show {
	opacity: 1;
}

.chart-tooltip::before {
	content: "";
	position: absolute;
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
	border: 5px solid transparent;
	border-top-color: rgba(255, 59, 212, 0.3);
}

.tooltip-content {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}

.tooltip-time {
	font-size: 0.75rem;
	color: #8b8b9f;
	font-weight: 500;
}

.tooltip-price {
	font-size: 1rem;
	color: #d4d5f1;
	font-weight: 600;
}

.tooltip-volume {
	font-size: 0.75rem;
	color: #8b8b9f;
}

/* Chart Loading State */
.chart-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	flex-direction: column;
	gap: 1rem;
}

.chart-loading-spinner {
	width: 40px;
	height: 40px;
	border: 3px solid rgba(255, 59, 212, 0.2);
	border-top: 3px solid #ff3bd4;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.chart-loading-text {
	color: #8b8b9f;
	font-size: 0.875rem;
}

/* Chart Empty State */
.chart-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	flex-direction: column;
	gap: 1rem;
	color: #8b8b9f;
}

.chart-empty-icon {
	font-size: 3rem;
	opacity: 0.5;
}

.chart-empty-text {
	font-size: 1rem;
	text-align: center;
}

/* Chart Legend */
.chart-legend {
	display: flex;
	justify-content: center;
	gap: 2rem;
	margin-top: 1rem;
	padding: 1rem;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-size: 0.875rem;
	color: #d4d5f1;
}

.legend-color {
	width: 12px;
	height: 12px;
	border-radius: 2px;
}

.legend-color.aint {
	background: linear-gradient(to right, #ff3bd4, #7130c3);
}

.legend-color.gag {
	background: linear-gradient(to right, #ff6b35, #ff8c42);
}

.legend-color.gold {
	background: linear-gradient(to right, #ffd700, #ffa500);
}

.legend-color.btc {
	background: linear-gradient(to right, #f7931a, #ff9500);
}

.legend-color.eth {
	background: linear-gradient(to right, #627eea, #4c6ef5);
}

/* Chart Responsive */
@media (max-width: 768px) {
	.chart-canvas {
		height: 300px;
	}

	.chart-animation {
		gap: 2px;
		padding: 1rem;
	}

	.chart-bar {
		max-width: 6px;
	}

	.chart-overlay {
		padding: 1.5rem;
	}

	.chart-overlay p {
		font-size: 1rem;
	}
}

.chart-icon {
	margin-bottom: 8px;
	opacity: 0.3;
}

/* Technical Indicators */
.indicators-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	animation: slideInRight 0.6s ease-out;
}

.indicators-card h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 20px 0;
}

.indicators-grid {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.indicator-item {
	padding: 16px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.indicator-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.indicator-name {
	font-size: 14px;
	font-weight: 500;
	color: #d4d5f1;
}

.indicator-value {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
}

.indicator-value.positive {
	color: #22c55e;
}

.indicator-value.negative {
	color: #ef4444;
}

.indicator-bar {
	height: 6px;
	background: rgba(255, 59, 212, 0.1);
	border-radius: 3px;
	overflow: hidden;
	position: relative;
}

.indicator-fill {
	height: 100%;
	background: linear-gradient(90deg, #ff3bd4 0%, #7130c3 100%);
	border-radius: 3px;
	transition: width 1s ease-out;
	animation: indicatorFill 1.5s ease-out;
}

.indicator-fill.positive {
	background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
}

@keyframes indicatorFill {
	from {
		width: 0;
	}
}

/* Sidebar */
.chart-sidebar {
	display: flex;
	flex-direction: column;
	gap: 24px;
}

.sidebar-card {
	background: linear-gradient(135deg, #0f1021 0%, #1a1a2e 100%);
	border-radius: 16px;
	padding: 24px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.2);
	transition: all 0.3s ease;
	animation: fadeInRight 0.6s ease-out;
}

.sidebar-card:hover {
	border-color: #ff3bd4;
	box-shadow: 0 12px 48px rgba(255, 59, 212, 0.15);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 16px;
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.card-header h3 {
	font-size: 18px;
	font-weight: 600;
	color: #ccceef;
	margin: 0;
}

.live-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 12px;
	color: #22c55e;
	font-weight: 500;
}

.live-dot {
	width: 8px;
	height: 8px;
	background: #22c55e;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

/* Market List */
.market-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.market-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.1);
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.market-item:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.2);
	transform: translateX(4px);
}

.market-item.active {
	background: rgba(255, 59, 212, 0.12);
	border-color: #ff3bd4;
	box-shadow: 0 4px 16px rgba(255, 59, 212, 0.2);
}

.market-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.market-symbol {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: 14px;
	font-weight: 600;
	color: #ff3bd4;
}

.mini-gold-icon {
	width: 12px;
	height: 12px;
	color: #ffd700;
}

.market-name {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
}

.market-price {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

.market-price .price {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
}

.market-price .change {
	font-size: 12px;
	font-weight: 500;
}

/* Performance Stats */
.performance-stats {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.perf-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.perf-icon {
	width: 32px;
	height: 32px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.perf-icon.positive {
	background: linear-gradient(
		135deg,
		rgba(34, 197, 94, 0.2) 0%,
		rgba(34, 197, 94, 0.1) 100%
	);
	color: #22c55e;
	border: 1px solid rgba(34, 197, 94, 0.3);
}

.perf-icon.negative {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.2) 0%,
		rgba(239, 68, 68, 0.1) 100%
	);
	color: #ef4444;
	border: 1px solid rgba(239, 68, 68, 0.3);
}

.perf-icon.neutral {
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.2) 0%,
		rgba(113, 48, 195, 0.2) 100%
	);
	color: #ff3bd4;
	border: 1px solid rgba(255, 59, 212, 0.3);
}

.perf-content h4 {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.perf-content p {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0;
}

/* Market Insights */
.insights-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.insight-item {
	display: flex;
	gap: 12px;
	padding: 12px;
	background: rgba(255, 59, 212, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
	transition: all 0.3s ease;
}

.insight-item:hover {
	background: rgba(255, 59, 212, 0.08);
	border-color: rgba(255, 59, 212, 0.2);
}

.insight-icon {
	width: 32px;
	height: 32px;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.3);
	flex-shrink: 0;
}

.insight-icon-svg {
	width: 16px;
	height: 16px;
	color: #ff3bd4;
}

.insight-content {
	flex: 1;
}

.insight-content h4 {
	font-size: 14px;
	font-weight: 600;
	color: #ccceef;
	margin: 0 0 4px 0;
}

.insight-content p {
	font-size: 12px;
	color: rgba(212, 213, 241, 0.7);
	margin: 0 0 6px 0;
	line-height: 1.4;
}

.insight-time {
	font-size: 11px;
	color: rgba(255, 59, 212, 0.8);
	font-weight: 500;
}

/* Animations */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInDown {
	from {
		opacity: 0;
		transform: translateY(-20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInLeft {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideInRight {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInRight {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}

/* Responsive Design */
@media (max-width: 1200px) {
	.chart-content {
		grid-template-columns: 1fr;
		gap: 24px;
	}

	.chart-sidebar {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 24px;
	}
}

@media (max-width: 768px) {
	.chart-hero {
		padding: 40px 16px 32px;
	}

	.hero-title {
		font-size: 28px;
	}

	.price-ticker {
		flex-direction: column;
		gap: 16px;
	}

	.ticker-item {
		flex-direction: row;
		justify-content: space-between;
		min-width: auto;
	}

	.chart-content {
		padding: 24px 16px;
	}

	.controls-header {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.chart-options {
		justify-content: space-between;
	}

	.price-header-card {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.price-display {
		flex-direction: column;
		gap: 8px;
	}

	.current-price {
		font-size: 28px;
	}

	.chart-canvas {
		height: 300px;
	}

	.chart-sidebar {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 640px) {
	.token-buttons {
		grid-template-columns: repeat(2, 1fr);
	}

	.chart-options {
		flex-direction: column;
		gap: 12px;
	}

	.timeframe-selector {
		justify-content: center;
	}

	.indicators-grid {
		gap: 12px;
	}

	.market-item {
		flex-direction: column;
		align-items: stretch;
		gap: 8px;
	}

	.market-price {
		align-items: flex-start;
	}
}

.Toastify {
	background: #16172a !important;
	color: #ff3bd4 !important;
	border-left: 4px solid #ff3bd4 !important;
	box-shadow: 0 2px 15px rgba(255, 59, 212, 0.3) !important;
}
/* Global Toastify Styles */
.Toastify__toast {
	background: #16172a !important;
	color: #ff3bd4 !important;
	border-left: 4px solid #ff3bd4 !important;
	box-shadow: 0 2px 15px rgba(255, 59, 212, 0.3) !important;
	font-weight: 500;
}

/* Optional: Customize success, error, info, warning separately */
.Toastify__toast--success {
	border-left: 4px solid #ff3bd4 !important;
}

.Toastify__toast--error {
	border-left: 4px solid #ff3bd4 !important;
}

.Toastify__toast--info {
	border-left: 4px solid #ff3bd4 !important;
}

/* OMNICORED Connection Page Styles */
/* Using the specified color theme for professional AINT GOLD exchange */

:root {
	--bs-body-font-family: "DM Sans", serif;
	--bs-heading-font-family: "Roobert PRO Bold";
	--bs-body-bg: #070710;
	--bs-body-bg-rgb: 7, 7, 16;
	--bs-body-color: #d4d5f1;
	--bs-body-color-rgb: 212, 213, 241;
	--bs-heading-color: #ccceef;
	--bs-light: #0f1021;
	--bs-light-rgb: 15, 16, 33;
	--bs-primary: #ff3bd4;
	--bs-primary-rgb: 255, 59, 212;
	--bs-secondary: #7130c3;
	--bs-secondary-rgb: 113, 48, 195;
	--bs-border-color: #2f336d;
	--bs-border-color-translucent: 47, 51, 109;
	--bs-dark: #070710;
	--bs-dark-rgb: 7, 7, 16;

	/* Additional theme colors */
	--gold-primary: #d4af37;
	--gold-secondary: #b8941f;
	--gold-light: #e6c547;
	--success-color: #10b981;
	--warning-color: #f59e0b;
	--error-color: #ef4444;
}

/* Container Styles */
.omnicore-container {
	padding: 2rem;
	background: var(--bs-body-bg);
	min-height: 100vh;
	font-family: var(--bs-body-font-family);
	color: var(--bs-body-color);
}

/* Header Styles */
.omnicore-header {
	margin-bottom: 2rem;
	padding: 2rem;
	background: linear-gradient(
		135deg,
		var(--bs-light) 0%,
		rgba(47, 51, 109, 0.1) 100%
	);
	border-radius: 16px;
	border: 1px solid var(--bs-border-color);
	position: relative;
	overflow: hidden;
}

.omnicore-header::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		45deg,
		transparent 30%,
		rgba(255, 59, 212, 0.05) 50%,
		transparent 70%
	);
	pointer-events: none;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	z-index: 1;
}

.header-info {
	flex: 1;
}

.page-title {
	font-family: var(--bs-heading-font-family);
	font-size: 2.5rem;
	font-weight: 700;
	color: var(--bs-heading-color);
	margin: 0 0 0.5rem 0;
	display: flex;
	align-items: center;
	gap: 1rem;
}

.title-icon {
	width: 2.5rem;
	height: 2.5rem;
	color: var(--bs-primary);
	filter: drop-shadow(0 0 10px rgba(255, 59, 212, 0.3));
}

.page-subtitle {
	font-size: 1.1rem;
	color: var(--bs-body-color);
	opacity: 0.8;
	margin: 0;
}

.connection-status {
	display: flex;
	align-items: center;
}

.status-badge {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.75rem 1.5rem;
	border-radius: 50px;
	font-weight: 600;
	font-size: 0.9rem;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	border: 2px solid;
	transition: all 0.3s ease;
}

.status-badge.status-success {
	background: linear-gradient(
		135deg,
		rgba(16, 185, 129, 0.1) 0%,
		rgba(16, 185, 129, 0.05) 100%
	);
	color: var(--success-color);
	border-color: var(--success-color);
	box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.status-badge.status-warning {
	background: linear-gradient(
		135deg,
		rgba(245, 158, 11, 0.1) 0%,
		rgba(245, 158, 11, 0.05) 100%
	);
	color: var(--warning-color);
	border-color: var(--warning-color);
	box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.status-badge.status-error {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.1) 0%,
		rgba(239, 68, 68, 0.05) 100%
	);
	color: var(--error-color);
	border-color: var(--error-color);
	box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.status-badge.status-secondary {
	background: linear-gradient(
		135deg,
		rgba(113, 48, 195, 0.1) 0%,
		rgba(113, 48, 195, 0.05) 100%
	);
	color: var(--bs-secondary);
	border-color: var(--bs-secondary);
	box-shadow: 0 0 20px rgba(113, 48, 195, 0.2);
}

/* Grid Layout */
.omnicore-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 2rem;
	grid-template-areas:
		"connection node-info"
		"logs actions";
}

/* Card Styles */
.connection-card {
	grid-area: connection;
}

.node-info-card {
	grid-area: node-info;
}

.logs-card {
	grid-area: logs;
}

.actions-card {
	grid-area: actions;
}

.connection-card,
.node-info-card,
.logs-card,
.actions-card {
	background: linear-gradient(
		135deg,
		var(--bs-light) 0%,
		rgba(15, 16, 33, 0.8) 100%
	);
	border: 1px solid var(--bs-border-color);
	border-radius: 16px;
	overflow: hidden;
	transition: all 0.3s ease;
	position: relative;
}

.connection-card:hover,
.node-info-card:hover,
.logs-card:hover,
.actions-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 59, 212, 0.1);
	border-color: rgba(255, 59, 212, 0.3);
}

.card-title {
	font-family: var(--bs-heading-font-family);
	font-size: 1.25rem;
	font-weight: 600;
	color: var(--bs-heading-color);
	display: flex;
	align-items: center;
	gap: 0.75rem;
	margin: 0 0 1.5rem 0;
}

.card-icon {
	width: 1.25rem;
	height: 1.25rem;
	color: var(--bs-primary);
}

.card-content {
	padding: 1.5rem;
}

/* Form Styles */
.form-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 1.5rem;
	margin-bottom: 2rem;
}

.form-group {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.form-group.ssl-toggle {
	grid-column: 1 / -1;
}

.form-label {
	font-weight: 600;
	color: var(--bs-heading-color);
	font-size: 0.9rem;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.label-icon {
	width: 1rem;
	height: 1rem;
	color: var(--bs-primary);
}

.form-input {
	background: rgba(15, 16, 33, 0.6);
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	padding: 0.75rem 1rem;
	color: var(--bs-body-color);
	font-size: 0.9rem;
	transition: all 0.3s ease;
}

.form-input:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.1);
	background: rgba(15, 16, 33, 0.8);
}

.form-input::placeholder {
	color: rgba(212, 213, 241, 0.5);
}

/* Password Input */
.password-input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

.password-input {
	flex: 1;
	padding-right: 3rem;
}

.password-toggle {
	position: absolute;
	right: 0.5rem;
	background: none;
	border: none;
	color: var(--bs-body-color);
	opacity: 0.7;
	transition: opacity 0.3s ease;
	cursor: pointer;
}

.password-toggle:hover {
	opacity: 1;
	color: var(--bs-primary);
}

/* SSL Toggle */
.ssl-label {
	display: flex;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
	padding: 1rem;
	background: rgba(15, 16, 33, 0.3);
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	transition: all 0.3s ease;
}

.ssl-label:hover {
	background: rgba(15, 16, 33, 0.5);
	border-color: var(--bs-primary);
}

.ssl-switch {
	position: relative;
	width: 50px;
	height: 24px;
}

.ssl-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.ssl-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: var(--bs-border-color);
	transition: 0.3s;
	border-radius: 24px;
}

.ssl-slider:before {
	position: absolute;
	content: "";
	height: 18px;
	width: 18px;
	left: 3px;
	bottom: 3px;
	background: var(--bs-body-color);
	transition: 0.3s;
	border-radius: 50%;
}

.ssl-checkbox:checked + .ssl-slider {
	background: var(--bs-primary);
	box-shadow: 0 0 10px rgba(255, 59, 212, 0.3);
}

.ssl-checkbox:checked + .ssl-slider:before {
	transform: translateX(26px);
}

/* Action Buttons */
.action-buttons {
	display: flex;
	gap: 1rem;
	justify-content: flex-end;
}

.test-button {
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	border: none;
	padding: 0.75rem 2rem;
	border-radius: 8px;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	cursor: pointer;
}

.test-button:hover:not(:disabled) {
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(255, 59, 212, 0.3);
}

.test-button:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.disconnect-button {
	background: transparent;
	color: var(--error-color);
	border: 1px solid var(--error-color);
	padding: 0.75rem 2rem;
	border-radius: 8px;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	cursor: pointer;
}

.disconnect-button:hover {
	background: var(--error-color);
	color: white;
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

/* Node Stats */
.node-stats {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 1.5rem;
}

.stat-item {
	padding: 1rem;
	background: rgba(15, 16, 33, 0.3);
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	transition: all 0.3s ease;
}

.stat-item:hover {
	background: rgba(15, 16, 33, 0.5);
	border-color: var(--bs-primary);
	transform: translateY(-2px);
}

.stat-label {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-size: 0.8rem;
	color: var(--bs-body-color);
	opacity: 0.8;
	margin-bottom: 0.5rem;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.stat-icon {
	width: 0.9rem;
	height: 0.9rem;
	color: var(--bs-primary);
}

.stat-value {
	font-size: 1.1rem;
	font-weight: 700;
	color: var(--bs-heading-color);
}

/* Logs */
.logs-container {
	background: rgba(7, 7, 16, 0.8);
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	padding: 1rem;
	max-height: 300px;
	overflow-y: auto;
	font-family: "Courier New", monospace;
}

.log-entry {
	display: flex;
	gap: 1rem;
	padding: 0.5rem 0;
	border-bottom: 1px solid rgba(47, 51, 109, 0.3);
	font-size: 0.85rem;
}

.log-entry:last-child {
	border-bottom: none;
}

.log-time {
	color: var(--bs-body-color);
	opacity: 0.6;
	white-space: nowrap;
}

.log-message {
	flex: 1;
}

.log-entry.success .log-message {
	color: var(--success-color);
}

.log-entry.info .log-message {
	color: var(--bs-body-color);
}

.log-entry.warning .log-message {
	color: var(--warning-color);
}

.log-entry.error .log-message {
	color: var(--error-color);
}

/* Quick Actions */
.quick-actions {
	display: grid;
	/* grid-template-columns: 1fr 1fr; */
	grid-template-columns: unset;
	gap: 1rem;
}

.action-btn {
	background: rgba(15, 16, 33, 0.6);
	color: var(--bs-body-color);
	border: 1px solid var(--bs-border-color);
	padding: 1rem;
	border-radius: 8px;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	text-align: center;
	cursor: pointer;
}

.action-btn:hover:not(:disabled) {
	background: rgba(255, 59, 212, 0.1);
	border-color: var(--bs-primary);
	color: var(--bs-primary);
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(255, 59, 212, 0.2);
}

.action-btn:disabled {
	opacity: 0.4;
	cursor: not-allowed;
}

/* Animations */
@keyframes pulse {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
}

.animate-spin {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Utility Classes */
.flex {
	display: flex;
}

.items-center {
	align-items: center;
}

.justify-center {
	justify-content: center;
}

.text-center {
	text-align: center;
}

.text-gray-500 {
	color: rgba(212, 213, 241, 0.5);
}

.w-4 {
	width: 1rem;
}

.h-4 {
	height: 1rem;
}

.w-12 {
	width: 3rem;
}

.h-12 {
	height: 3rem;
}

.h-48 {
	height: 12rem;
}

.mb-4 {
	margin-bottom: 1rem;
}

.mx-auto {
	margin-left: auto;
	margin-right: auto;
}

.opacity-30 {
	opacity: 0.3;
}

/* Responsive Design */
@media (max-width: 1200px) {
	.omnicore-grid {
		grid-template-columns: 1fr;
		grid-template-areas:
			"connection"
			"node-info"
			"logs"
			"actions";
	}
}

@media (max-width: 768px) {
	.omnicore-container {
		padding: 1rem;
	}

	.header-content {
		flex-direction: column;
		gap: 1rem;
		text-align: center;
	}

	.page-title {
		font-size: 2rem;
	}

	.form-grid {
		grid-template-columns: 1fr;
	}

	.node-stats {
		grid-template-columns: 1fr;
	}

	.quick-actions {
		grid-template-columns: 1fr;
	}

	.action-buttons {
		flex-direction: column;
	}
}

@media (max-width: 480px) {
	.page-title {
		font-size: 1.5rem;
		flex-direction: column;
		gap: 0.5rem;
	}

	.title-icon {
		width: 2rem;
		height: 2rem;
	}
}

/* Scrollbar Styling */
.logs-container::-webkit-scrollbar {
	width: 6px;
}

.logs-container::-webkit-scrollbar-track {
	background: rgba(47, 51, 109, 0.1);
	border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb {
	background: var(--bs-primary);
	border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
	background: var(--bs-secondary);
}

/* Bank Modal Css */

/* Modal Overlay */
.bank-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

/* Modal Box */
.bank-modal-content {
	background: var(--bs-light);
	border-radius: 16px;
	max-width: 500px;
	width: 90%;
	padding: 30px;
	position: relative;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	font-family: var(--bs-body-font-family);
	animation: fadeIn 0.3s ease-in-out;
	color: var(--bs-body-color);
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(-20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Close Button */
.bank-modal-close-btn {
	position: absolute;
	top: 14px;
	right: 14px;
	border: none;
	background: transparent;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: transform 0.2s ease;
}

.bank-modal-close-btn:hover {
	transform: scale(1.1);
}

/* Modal Title */
.bank-modal-title {
	font-family: var(--bs-heading-font-family);
	font-size: 22px;
	font-weight: bold;
	color: var(--bs-heading-color);
	text-align: center;
	margin-bottom: 20px;
}

/* Info Text */
.bank-modal-info p {
	margin: 8px 0;
	font-size: 14px;
	color: var(--bs-body-color);
}

.bank-modal-aint {
	color: #999;
	font-size: 12px;
}

/* Divider */
.bank-modal-divider {
	border-top: 1px solid var(--bs-border-color);
	margin: 20px 0;
}

/* Upload Section */
.bank-modal-upload-section {
	margin-bottom: 20px;
}

.bank-modal-upload-label {
	font-size: 13px;
	color: var(--bs-body-color);
	font-weight: 500;
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.bank-modal-file-input {
	width: 100%;
	padding: 10px;
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	background-color: var(--bs-body-bg);
	color: var(--bs-body-color);
	font-size: 13px;
}

/* Actions */
.bank-modal-actions {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.bank-modal-cancel-btn {
	background: #2a2a3b;
	border: none;
	padding: 8px 18px;
	border-radius: 8px;
	cursor: pointer;
	color: var(--bs-body-color);
	font-size: 13px;
	transition: background 0.2s ease;
}

.bank-modal-cancel-btn:hover {
	background: #39394d;
}

.bank-modal-submit-btn {
	background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
	border: none;
	padding: 8px 18px;
	border-radius: 8px;
	color: #fff;
	font-size: 13px;
	display: flex;
	align-items: center;
	cursor: pointer;
	transition: opacity 0.2s ease;
}

.bank-modal-submit-btn:hover {
	opacity: 0.85;
}

.bank-modal-submit-btn.disabled {
	background: #555;
	cursor: not-allowed;
	opacity: 0.6;
}

.paying_Amount {
	font-family: var(--bs-heading-font-family);
	font-size: 2.5rem; /* Bigger font */
	color: var(--bs-primary); /* Pink color: #FF3BD4 */
	font-weight: bold;
	margin: 10px 0;
	text-align: center;
	animation: glow 1.8s ease-in-out infinite alternate;
}

/* Subtle glowing effect */
@keyframes glow {
	0% {
		text-shadow: 0 0 8px rgba(255, 59, 212, 0.5),
			0 0 12px rgba(255, 59, 212, 0.2);
		transform: scale(1);
	}
	100% {
		text-shadow: 0 0 16px rgba(255, 59, 212, 0.8),
			0 0 24px rgba(255, 59, 212, 0.4);
		transform: scale(1.05);
	}
}

/* global CSS or styled-components */
.logout-icon:hover {
	color: red;
}

.pagination-controls {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-top: 1.5rem;
}

.pagination-btn {
	background-color: var(--bs-light); /* dark card bg */
	color: var(--bs-body-color); /* main text color */
	border: 1px solid var(--bs-border-color);
	padding: 6px 14px;
	border-radius: 6px;
	font-size: 14px;
	transition: all 0.2s ease;
	cursor: pointer;
}

.pagination-btn:hover {
	background-color: var(--bs-secondary); /* purple hover */
	color: white;
	border-color: var(--bs-secondary);
}

.pagination-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.pagination-controls span {
	font-size: 14px;
	color: var(--bs-heading-color);
}
/* Transfer AINT Page Styles */
:root {
	--bs-body-font-family: "DM Sans", serif;
	--bs-heading-font-family: "Roobert PRO Bold";
	--bs-body-bg: #070710;
	--bs-body-bg-rgb: 7, 7, 16;
	--bs-body-color: #d4d5f1;
	--bs-body-color-rgb: 212, 213, 241;
	--bs-heading-color: #ccceef;
	--bs-light: #0f1021;
	--bs-light-rgb: 15, 16, 33;
	--bs-primary: #ff3bd4;
	--bs-primary-rgb: 255, 59, 212;
	--bs-secondary: #7130c3;
	--bs-secondary-rgb: 113, 48, 195;
	--bs-border-color: #2f336d;
	--bs-border-color-translucent: 47, 51, 109;
	--bs-dark: #070710;
	--bs-dark-rgb: 7, 7, 16;

	/* Additional theme colors */
	--gold-primary: #d4af37;
	--gold-secondary: #b8941f;
	--gold-light: #e6c547;
	--success-color: #10b981;
	--warning-color: #f59e0b;
	--error-color: #ef4444;
}

/* Reset and Base Styles */
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

body {
	font-family: var(--bs-body-font-family);
	background-color: var(--bs-body-bg);
	color: var(--bs-body-color);
	line-height: 1.6;
}

/* Layout Styles */
.transfer-container {
	min-height: 100vh;
	padding: 24px;
	background-color: var(--bs-body-bg);
	color: var(--bs-body-color);
}

.transfer-wrapper {
	max-width: 1200px;
	margin: 0 auto;
}

.transfer-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 24px;
}

@media (min-width: 1024px) {
	.transfer-grid {
		grid-template-columns: 2fr 1fr;
	}
}

/* Header Styles */
.transfer-header {
	margin-bottom: 32px;
}

.transfer-title {
	font-family: var(--bs-heading-font-family);
	font-size: 2rem;
	font-weight: bold;
	color: var(--bs-heading-color);
	margin-bottom: 8px;
}

.transfer-subtitle {
	color: rgba(var(--bs-body-color-rgb), 0.7);
	font-size: 1rem;
}

/* Card Styles */
.transfer-card {
	background: rgba(var(--bs-light-rgb), 0.8);
	border: 1px solid var(--bs-border-color);
	border-radius: 12px;
	padding: 24px;
	backdrop-filter: blur(10px);
	box-shadow: 0 8px 32px rgba(var(--bs-primary-rgb), 0.1);
	transition: all 0.3s ease;
}

.transfer-card:hover {
	box-shadow: 0 12px 40px rgba(var(--bs-primary-rgb), 0.15);
	transform: translateY(-2px);
}

.sidebar-card {
	background: rgba(var(--bs-light-rgb), 0.8);
	border: 1px solid var(--bs-border-color);
	border-radius: 12px;
	padding: 24px;
	backdrop-filter: blur(10px);
	box-shadow: 0 8px 32px rgba(var(--bs-secondary-rgb), 0.1);
}

/* Section Headers */
.section-title {
	font-family: var(--bs-heading-font-family);
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--bs-heading-color);
	margin-bottom: 16px;
}

/* Transfer Method Selection */
.method-buttons {
	display: flex;
	gap: 16px;
	margin-bottom: 24px;
}

.method-button {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 12px 16px;
	border: 1px solid rgba(var(--bs-body-color-rgb), 0.3);
	border-radius: 8px;
	background: transparent;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: all 0.2s ease;
	font-family: var(--bs-body-font-family);
	font-size: 0.875rem;
}

.method-button:hover {
	border-color: rgba(var(--bs-body-color-rgb), 0.5);
	background: rgba(var(--bs-body-color-rgb), 0.05);
}

.method-button.active {
	border-color: var(--bs-primary);
	background: rgba(var(--bs-primary-rgb), 0.1);
	color: var(--bs-primary);
}

.method-button.active.qr {
	border-color: var(--bs-secondary);
	background: rgba(var(--bs-secondary-rgb), 0.1);
	color: var(--bs-secondary);
}

/* Token Selection Grid */
.token-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 12px;
	margin-bottom: 24px;
}

.token-button {
	padding: 12px;
	border: 1px solid rgba(var(--bs-body-color-rgb), 0.3);
	border-radius: 8px;
	background: transparent;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: all 0.2s ease;
	text-align: left;
}

.token-button:hover {
	border-color: rgba(var(--bs-body-color-rgb), 0.5);
	background: rgba(var(--bs-body-color-rgb), 0.05);
}

.token-button.active {
	border-color: var(--bs-primary);
	background: rgba(var(--bs-primary-rgb), 0.1);
}

.token-button.active.aint {
	border-color: var(--gold-primary);
	background: rgba(212, 175, 55, 0.1);
}

.token-button.active.gag {
	border-color: #ff6b35;
	background: rgba(255, 107, 53, 0.1);
}

.token-header {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 4px;
}

.token-icon {
	font-size: 1.125rem;
}

.token-symbol {
	font-weight: 500;
	font-size: 0.875rem;
}

.token-balance {
	font-size: 0.75rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

/* Form Styles */
.form-group {
	margin-bottom: 24px;
}

.form-label {
	display: block;
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 8px;
}

.form-input {
	width: 100%;
	padding: 12px 16px;
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	background: transparent;
	color: var(--bs-body-color);
	font-family: var(--bs-body-font-family);
	font-size: 0.875rem;
	transition: all 0.2s ease;
}

.form-input:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
}

.form-input.error {
	border-color: var(--error-color);
}

.form-input::placeholder {
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

.form-textarea {
	width: 100%;
	padding: 12px 16px;
	border: 1px solid var(--bs-border-color);
	border-radius: 8px;
	background: transparent;
	color: var(--bs-body-color);
	font-family: var(--bs-body-font-family);
	font-size: 0.875rem;
	resize: none;
	transition: all 0.2s ease;
}

.form-textarea:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
}

/* Input with Icon */
.input-with-icon {
	position: relative;
}

.input-icon {
	position: absolute;
	right: 8px;
	top: 8px;
	padding: 8px;
	border-radius: 6px;
	background: transparent;
	border: none;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: all 0.2s ease;
}

.input-icon:hover {
	background: rgba(var(--bs-body-color-rgb), 0.1);
}

.input-suffix {
	position: absolute;
	right: 12px;
	top: 50%;
	transform: translateY(-50%);
	color: rgba(var(--bs-body-color-rgb), 0.7);
	font-size: 0.875rem;
}

/* Balance Info */
.balance-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 8px;
}

.balance-text {
	font-size: 0.75rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.max-button {
	background: none;
	border: none;
	color: var(--bs-primary);
	font-size: 0.75rem;
	cursor: pointer;
	transition: color 0.2s ease;
}

.max-button:hover {
	color: rgba(var(--bs-primary-rgb), 0.8);
}

/* Error Message */
.error-message {
	color: var(--error-color);
	font-size: 0.75rem;
	margin-top: 4px;
}

/* QR Scanner Area */
.qr-scanner-area {
	border: 2px dashed rgba(var(--bs-body-color-rgb), 0.3);
	border-radius: 8px;
	padding: 32px;
	text-align: center;
	transition: all 0.2s ease;
}

.qr-scanner-area:hover {
	border-color: rgba(var(--bs-body-color-rgb), 0.5);
}

.qr-icon {
	width: 48px;
	height: 48px;
	margin: 0 auto 16px;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.qr-text {
	color: rgba(var(--bs-body-color-rgb), 0.7);
	margin-bottom: 16px;
}

.qr-button {
	padding: 12px 24px;
	background: var(--bs-primary);
	color: white;
	border: none;
	border-radius: 8px;
	font-family: var(--bs-body-font-family);
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
}

.qr-button:hover {
	background: rgba(var(--bs-primary-rgb), 0.9);
	transform: scale(1.05);
}

/* Transaction Summary */
.transaction-summary {
	background: rgba(var(--bs-border-color-translucent), 0.3);
	border-radius: 8px;
	padding: 16px;
	margin-bottom: 24px;
}

.summary-title {
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 12px;
}

.summary-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8px;
	font-size: 0.875rem;
}

.summary-label {
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.summary-value {
	color: var(--bs-body-color);
}

.summary-total {
	border-top: 1px solid var(--bs-border-color);
	padding-top: 8px;
	margin-top: 8px;
}

.summary-total .summary-row {
	font-weight: 500;
	margin-bottom: 0;
}

/* Send Button */
.send-button {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	padding: 16px 24px;
	background: var(--bs-primary);
	color: white;
	border: none;
	border-radius: 8px;
	font-family: var(--bs-body-font-family);
	font-weight: 500;
	font-size: 1rem;
	cursor: pointer;
	transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
	background: rgba(var(--bs-primary-rgb), 0.9);
	transform: scale(1.02);
}

.send-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.loading-spinner {
	width: 20px;
	height: 20px;
	border: 2px solid white;
	border-top: 2px solid transparent;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* Recent Contacts */
.contacts-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.contact-item {
	padding: 12px;
	border: 1px solid rgba(var(--bs-body-color-rgb), 0.3);
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.2s ease;
}

.contact-item:hover {
	border-color: rgba(var(--bs-body-color-rgb), 0.5);
	background: rgba(var(--bs-body-color-rgb), 0.05);
}

.contact-content {
	display: flex;
	align-items: center;
	gap: 12px;
}

.contact-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: var(--bs-secondary);
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 500;
	font-size: 0.875rem;
}

.contact-info {
	flex: 1;
	min-width: 0;
}

.contact-name {
	font-weight: 500;
	margin-bottom: 2px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.contact-address {
	font-size: 0.75rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.contact-time {
	font-size: 0.625rem;
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

.copy-button {
	padding: 8px;
	background: transparent;
	border: none;
	color: var(--bs-body-color);
	cursor: pointer;
	border-radius: 6px;
	transition: all 0.2s ease;
}

.copy-button:hover {
	background: rgba(var(--bs-body-color-rgb), 0.1);
}

/* Quick Actions */
.quick-actions {
	margin-top: 24px;
	padding-top: 24px;
	border-top: 1px solid var(--bs-border-color);
}

.quick-actions-title {
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 12px;
}

.quick-actions-list {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.quick-action-button {
	width: 100%;
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 12px;
	border: 1px solid rgba(var(--bs-body-color-rgb), 0.3);
	border-radius: 8px;
	background: transparent;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: all 0.2s ease;
	font-family: var(--bs-body-font-family);
	font-size: 0.875rem;
}

.quick-action-button:hover {
	border-color: rgba(var(--bs-body-color-rgb), 0.5);
	background: rgba(var(--bs-body-color-rgb), 0.05);
}

/* Modal Styles */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(4px);
}

.modal-content {
	background: rgba(var(--bs-light-rgb), 0.95);
	border: 1px solid var(--bs-border-color);
	border-radius: 12px;
	padding: 24px;
	max-width: 400px;
	width: 90%;
	margin: 16px;
	backdrop-filter: blur(10px);
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16px;
}

.modal-title {
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--bs-heading-color);
}

.modal-close {
	padding: 8px;
	background: transparent;
	border: none;
	color: var(--bs-body-color);
	cursor: pointer;
	border-radius: 6px;
	transition: all 0.2s ease;
}

.modal-close:hover {
	background: rgba(var(--bs-body-color-rgb), 0.1);
}

.modal-scanner {
	border: 2px dashed rgba(var(--bs-body-color-rgb), 0.3);
	border-radius: 8px;
	padding: 32px;
	text-align: center;
}

.modal-scanner-icon {
	width: 64px;
	height: 64px;
	margin: 0 auto 16px;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.modal-scanner-text {
	color: rgba(var(--bs-body-color-rgb), 0.7);
	margin-bottom: 16px;
}

.modal-scanner-note {
	font-size: 0.75rem;
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
	.transfer-container {
		padding: 16px;
	}

	.transfer-title {
		font-size: 1.5rem;
	}

	.method-buttons {
		flex-direction: column;
	}

	.token-grid {
		grid-template-columns: 1fr;
	}

	.contact-content {
		gap: 8px;
	}

	.contact-avatar {
		width: 32px;
		height: 32px;
		font-size: 0.75rem;
	}
}

/* Animation Classes */
.fade-in {
	animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.slide-up {
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Hover Effects */
.hover-lift {
	transition: transform 0.2s ease;
}

.hover-lift:hover {
	transform: translateY(-2px);
}

/* Focus States */
.focus-ring:focus {
	outline: none;
	box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.2);
}

/* Success States */
.success-border {
	border-color: var(--success-color) !important;
}

.success-text {
	color: var(--success-color);
}

/* Warning States */
.warning-border {
	border-color: var(--warning-color) !important;
}

.warning-text {
	color: var(--warning-color);
}

/* AINT Token Special Styling */
.aint-highlight {
	background: linear-gradient(
		135deg,
		rgba(212, 175, 55, 0.1),
		rgba(184, 148, 31, 0.1)
	);
	border-color: var(--gold-primary);
}

.aint-text {
	color: var(--gold-primary);
}

.aint-button {
	background: var(--gold-primary);
	color: var(--bs-dark);
}

.aint-button:hover {
	background: var(--gold-secondary);
}

.gag-button {
	background: #ff6b35;
	color: var(--bs-dark);
}

.gag-button:hover {
	background: #ff8c42;
}

/* Receive AINT Page Styles */

:root {
	--bs-body-font-family: "DM Sans", serif;
	--bs-heading-font-family: "Roobert PRO Bold";
	--bs-body-bg: #070710;
	--bs-body-bg-rgb: 7, 7, 16;
	--bs-body-color: #d4d5f1;
	--bs-body-color-rgb: 212, 213, 241;
	--bs-heading-color: #ccceef;
	--bs-light: #0f1021;
	--bs-light-rgb: 15, 16, 33;
	--bs-primary: #ff3bd4;
	--bs-primary-rgb: 255, 59, 212;
	--bs-secondary: #7130c3;
	--bs-secondary-rgb: 113, 48, 195;
	--bs-border-color: #2f336d;
	--bs-border-color-translucent: 47, 51, 109;
	--bs-dark: #070710;
	--bs-dark-rgb: 7, 7, 16;

	/* Additional theme colors */
	--gold-primary: #d4af37;
	--gold-secondary: #b8941f;
	--gold-light: #e6c547;
	--success-color: #10b981;
	--warning-color: #f59e0b;
	--error-color: #ef4444;
}

/* Base styles */
.receive-container {
	min-height: 100vh;
	background: linear-gradient(
		135deg,
		var(--bs-body-bg) 0%,
		var(--bs-light) 50%,
		var(--bs-dark) 100%
	);
	padding: 1.5rem;
	color: var(--bs-body-color);
	font-family: var(--bs-body-font-family);
}

.receive-wrapper {
	max-width: 64rem;
	margin: 0 auto;
}

/* Header */
.receive-header {
	margin-bottom: 2rem;
}

.receive-title {
	font-size: 1.875rem;
	font-weight: 700;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
	font-family: var(--bs-heading-font-family);
}

.receive-subtitle {
	color: var(--bs-body-color);
	opacity: 0.8;
}

/* Grid Layout */
.receive-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 1.5rem;
}

@media (min-width: 1024px) {
	.receive-grid {
		grid-template-columns: 1fr 1fr;
	}
}

/* Card Styles */
.receive-card {
	background: rgba(15, 16, 33, 0.8);
	border-radius: 1rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
		0 10px 10px -5px rgba(0, 0, 0, 0.2);
	padding: 1.5rem;
	border: 1px solid rgba(var(--bs-border-color-translucent), 0.3);
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
}

.receive-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 25px 35px -5px rgba(0, 0, 0, 0.4),
		0 15px 15px -5px rgba(0, 0, 0, 0.3);
	border-color: rgba(var(--bs-primary-rgb), 0.3);
}

/* Section Headers */
.section-header {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 1.5rem;
	display: flex;
	align-items: center;
	color: var(--bs-heading-color);
	font-family: var(--bs-heading-font-family);
}

.section-header svg {
	margin-right: 0.5rem;
	color: var(--bs-primary);
}

/* Form Elements */
.form-group {
	margin-bottom: 1.5rem;
}

.form-label {
	display: block;
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
}

.form-select,
.form-input {
	width: 100%;
	padding: 0.75rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background-color: rgba(var(--bs-light-rgb), 0.6);
	color: var(--bs-body-color);
	font-size: 0.875rem;
	transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.2);
	background-color: rgba(var(--bs-light-rgb), 0.8);
}

.form-input::placeholder {
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

/* QR Code Section */
.qr-display {
	text-align: center;
	margin: 1.5rem 0;
}

.qr-code-container {
	margin: 0 auto;
	width: fit-content;
	border: 2px solid var(--bs-border-color);
	border-radius: 0.5rem;
	padding: 1rem;
	background: #ffffff;
	min-height: 256px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.qr-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 256px;
	color: #6b7280;
	gap: 0.5rem;
}

.loading-spinner {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.qr-code-wrapper {
	display: flex;
	justify-content: center;
	align-items: center;
}

.qr-code-image {
	width: 256px;
	height: 256px;
	max-width: 100%;
	border: 1px solid #e5e7eb;
	border-radius: 8px;
	padding: 8px;
	background-color: #ffffff;
}

.qr-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 256px;
	color: #ef4444;
	gap: 0.5rem;
}

.qr-amount,
.qr-memo {
	margin-top: 8px;
	font-size: 14px;
	color: #6b7280;
	text-align: center;
}

.qr-description {
	text-align: center;
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
	margin-top: 1rem;
}

/* Button Styles */
.btn-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.75rem;
}

@media (min-width: 640px) {
	.btn-grid {
		grid-template-columns: 1fr 1fr 1fr;
	}
}

.btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0.75rem 1rem;
	border-radius: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	transition: all 0.3s ease;
	cursor: pointer;
	text-decoration: none;
	border: none;
	font-family: inherit;
}

.btn svg {
	width: 1rem;
	height: 1rem;
	margin-right: 0.5rem;
}

.btn-outline {
	border: 1px solid var(--bs-border-color);
	background: transparent;
	color: var(--bs-body-color);
}

.btn-outline:hover {
	background: rgba(var(--bs-light-rgb), 0.8);
	border-color: var(--bs-primary);
	color: var(--bs-heading-color);
}

.btn-primary {
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	border: none;
}

.btn-primary:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.4);
}

.btn-secondary {
	background-color: #f3f4f6;
	color: #374151;
	border: 1px solid #d1d5db;
}

.btn-secondary:hover {
	background-color: #e5e7eb;
}

.btn-secondary:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.btn:disabled:hover {
	transform: none;
	box-shadow: none;
}

/* Wallet Address Section */
.address-container {
	background: rgba(var(--bs-dark-rgb), 0.4);
	border-radius: 0.5rem;
	padding: 1rem;
	margin-bottom: 1rem;
	border: 1px solid var(--bs-border-color);
}

.address-display {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.address-text {
	font-family: "Courier New", monospace;
	font-size: 0.875rem;
	color: var(--gold-primary);
	word-break: break-all;
	padding-right: 0.5rem;
	line-height: 1.4;
}

.copy-btn {
	flex-shrink: 0;
	padding: 0.5rem;
	color: rgba(var(--bs-body-color-rgb), 0.6);
	background: transparent;
	border: none;
	cursor: pointer;
	border-radius: 0.25rem;
	transition: all 0.3s ease;
}

.copy-btn:hover {
	color: var(--bs-primary);
	background: rgba(var(--bs-primary-rgb), 0.1);
}

.copy-success {
	color: var(--success-color);
}

/* Success Message */
.success-message {
	font-size: 0.875rem;
	color: var(--success-color);
	margin-bottom: 1rem;
	display: flex;
	align-items: center;
}

.success-message svg {
	width: 1rem;
	height: 1rem;
	margin-right: 0.5rem;
}

/* Input with suffix */
.input-with-suffix {
	position: relative;
}

.input-suffix {
	position: absolute;
	right: 0.75rem;
	top: 50%;
	transform: translateY(-50%);
	color: rgba(var(--bs-body-color-rgb), 0.6);
	font-size: 0.875rem;
	pointer-events: none;
}

/* Security Tips */
.security-tips {
	margin-bottom: 0;
}

.tip-list {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.tip-item {
	display: flex;
	align-items: flex-start;
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.8);
}

.tip-item svg {
	width: 1rem;
	height: 1rem;
	color: var(--success-color);
	margin-right: 0.5rem;
	margin-top: 0.125rem;
	flex-shrink: 0;
}

/* Right Column Spacing */
.right-column {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

/* Real-time Indicators for Trending Page */
.header-badge {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	background: linear-gradient(135deg, #ff3bd4, #7130c3);
	padding: 0.5rem 1rem;
	border-radius: 2rem;
	margin-bottom: 1rem;
	font-size: 0.875rem;
	font-weight: 600;
	color: #ffffff;
	box-shadow: 0 4px 12px rgba(255, 59, 212, 0.3);
}

.badge-icon {
	width: 1rem;
	height: 1rem;
}

.header-stats {
	display: flex;
	gap: 2rem;
	margin-top: 1rem;
}

.header-stat {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 0.25rem;
}

.header-stat .stat-label {
	font-size: 0.75rem;
	color: rgba(212, 213, 241, 0.6);
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

.header-stat .stat-value {
	font-size: 1rem;
	font-weight: 600;
	color: #ff3bd4;
}

.live-indicator {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	color: #10b981;
	font-size: 0.875rem;
	font-weight: 600;
}

.live-dot {
	width: 8px;
	height: 8px;
	background-color: #10b981;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
	}
}

/* Responsive Design */
@media (max-width: 768px) {
	.receive-container {
		padding: 1rem;
	}

	.receive-title {
		font-size: 1.5rem;
	}

	.qr-mock {
		width: 10rem;
		height: 10rem;
	}

	.qr-mock svg {
		width: 6rem;
		height: 6rem;
	}

	.btn-grid {
		grid-template-columns: 1fr;
	}

	.receive-card {
		padding: 1rem;
	}
}

@media (max-width: 480px) {
	.address-display {
		flex-direction: column;
		align-items: stretch;
		gap: 0.5rem;
	}

	.copy-btn {
		align-self: center;
	}
}

/* Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.receive-card {
	animation: fadeIn 0.5s ease-out;
}

.receive-card:nth-child(1) {
	animation-delay: 0.1s;
}

.receive-card:nth-child(2) {
	animation-delay: 0.2s;
}

/* Focus styles for accessibility */
.btn:focus,
.form-select:focus,
.form-input:focus,
.copy-btn:focus {
	outline: 2px solid var(--bs-primary);
	outline-offset: 2px;
}

/* Loading state for buttons */
.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.btn:disabled:hover {
	transform: none;
	box-shadow: none;
}

/* Swap Tokens Page Styles */

:root {
	--bs-body-font-family: "DM Sans", serif;
	--bs-heading-font-family: "Roobert PRO Bold";
	--bs-body-bg: #070710;
	--bs-body-bg-rgb: 7, 7, 16;
	--bs-body-color: #d4d5f1;
	--bs-body-color-rgb: 212, 213, 241;
	--bs-heading-color: #ccceef;
	--bs-light: #0f1021;
	--bs-light-rgb: 15, 16, 33;
	--bs-primary: #ff3bd4;
	--bs-primary-rgb: 255, 59, 212;
	--bs-secondary: #7130c3;
	--bs-secondary-rgb: 113, 48, 195;
	--bs-border-color: #2f336d;
	--bs-border-color-translucent: 47, 51, 109;
	--bs-dark: #070710;
	--bs-dark-rgb: 7, 7, 16;

	/* Additional theme colors */
	--gold-primary: #d4af37;
	--gold-secondary: #b8941f;
	--gold-light: #e6c547;
	--success-color: #10b981;
	--warning-color: #f59e0b;
	--error-color: #ef4444;
}

/* Base styles */
.swap-container {
	min-height: 100vh;
	background: linear-gradient(
		135deg,
		var(--bs-body-bg) 0%,
		var(--bs-light) 50%,
		var(--bs-dark) 100%
	);
	padding: 1.5rem;
	color: var(--bs-body-color);
	font-family: var(--bs-body-font-family);
}

.swap-wrapper {
	max-width: 64rem;
	margin: 0 auto;
}

/* Header */
.swap-header {
	margin-bottom: 2rem;
}

.swap-title {
	font-size: 1.875rem;
	font-weight: 700;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
	font-family: var(--bs-heading-font-family);
}

.swap-subtitle {
	color: var(--bs-body-color);
	opacity: 0.8;
}

/* Grid Layout */
.swap-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 1.5rem;
}

@media (min-width: 1024px) {
	.swap-grid {
		grid-template-columns: 2fr 1fr;
	}
}

/* Card Styles */
.swap-card {
	background: rgba(15, 16, 33, 0.8);
	border-radius: 1rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
		0 10px 10px -5px rgba(0, 0, 0, 0.2);
	padding: 1.5rem;
	border: 1px solid rgba(var(--bs-border-color-translucent), 0.3);
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
}

.swap-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 25px 35px -5px rgba(0, 0, 0, 0.4),
		0 15px 15px -5px rgba(0, 0, 0, 0.3);
	border-color: rgba(var(--bs-primary-rgb), 0.3);
}

.swap-main-card {
	grid-column: span 2;
}

@media (min-width: 1024px) {
	.swap-main-card {
		grid-column: span 1;
	}
}

/* Section Headers */
.section-header {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 1.5rem;
	display: flex;
	align-items: center;
	color: var(--bs-heading-color);
	font-family: var(--bs-heading-font-family);
}

.section-header svg {
	margin-right: 0.5rem;
	color: var(--bs-primary);
}

/* Token Input Containers */
.token-input-container {
	background: rgba(var(--bs-dark-rgb), 0.4);
	border-radius: 0.75rem;
	padding: 1rem;
	margin-bottom: 1rem;
	border: 1px solid var(--bs-border-color);
	transition: all 0.3s ease;
}

.token-input-container:hover {
	border-color: rgba(var(--bs-primary-rgb), 0.3);
	background: rgba(var(--bs-dark-rgb), 0.6);
}

.token-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 0.75rem;
}

.token-select {
	background: transparent;
	border: none;
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--bs-heading-color);
	cursor: pointer;
	outline: none;
	font-family: inherit;
}

.token-select:focus {
	color: var(--bs-primary);
}

.token-balance {
	text-align: right;
}

.balance-label {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.6);
	margin-bottom: 0.25rem;
}

.balance-value {
	font-weight: 500;
	color: var(--bs-body-color);
}

.token-amount-input {
	width: 100%;
	background: transparent;
	border: none;
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--bs-heading-color);
	outline: none;
	font-family: inherit;
}

.token-amount-input::placeholder {
	color: rgba(var(--bs-body-color-rgb), 0.4);
}

.token-amount-input:disabled {
	color: rgba(var(--bs-body-color-rgb), 0.6);
}

.token-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 0.5rem;
}

.token-value {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.6);
}

.max-button {
	background: transparent;
	border: none;
	color: var(--bs-primary);
	font-size: 0.875rem;
	cursor: pointer;
	padding: 0.25rem 0.5rem;
	border-radius: 0.25rem;
	transition: all 0.3s ease;
	font-family: inherit;
}

.max-button:hover {
	background: rgba(var(--bs-primary-rgb), 0.1);
	color: var(--gold-primary);
}

/* Swap Button Container */
.swap-button-container {
	display: flex;
	justify-content: center;
	margin: 1rem 0;
}

.swap-direction-btn {
	padding: 0.75rem;
	background: rgba(var(--bs-primary-rgb), 0.1);
	border: 1px solid rgba(var(--bs-primary-rgb), 0.3);
	border-radius: 50%;
	cursor: pointer;
	transition: all 0.3s ease;
	color: var(--bs-primary);
}

.swap-direction-btn:hover {
	background: rgba(var(--bs-primary-rgb), 0.2);
	transform: rotate(180deg);
	border-color: var(--bs-primary);
}

/* Form Elements */
.form-group {
	margin-bottom: 1.5rem;
}

.form-label {
	display: block;
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
}

/* Slippage Settings */
.slippage-buttons {
	display: flex;
	gap: 0.5rem;
	flex-wrap: wrap;
}

.slippage-btn {
	padding: 0.5rem 1rem;
	border-radius: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1px solid var(--bs-border-color);
	background: transparent;
	color: var(--bs-body-color);
	font-family: inherit;
}

.slippage-btn:hover {
	background: rgba(var(--bs-light-rgb), 0.8);
	border-color: var(--bs-primary);
	color: var(--bs-heading-color);
}

.slippage-btn.active {
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	border-color: var(--bs-primary);
}

.slippage-input {
	padding: 0.5rem 0.75rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background: rgba(var(--bs-light-rgb), 0.6);
	color: var(--bs-body-color);
	font-size: 0.875rem;
	width: 5rem;
	font-family: inherit;
}

.slippage-input:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.2);
}

/* Main Swap Button */
.swap-execute-btn {
	width: 100%;
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	padding: 0.75rem 1.5rem;
	border-radius: 0.75rem;
	font-size: 1rem;
	font-weight: 600;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: inherit;
}

.swap-execute-btn:hover:not(:disabled) {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.4);
}

.swap-execute-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

.swap-execute-btn svg {
	width: 1.25rem;
	height: 1.25rem;
	margin-right: 0.5rem;
}

/* Sidebar */
.swap-sidebar {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

/* Exchange Rate Card */
.rate-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.75rem;
}

.rate-item:last-child {
	margin-bottom: 0;
}

.rate-label {
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.rate-value {
	font-weight: 600;
	color: var(--bs-heading-color);
}

/* Transaction Details */
.detail-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.75rem;
	font-size: 0.875rem;
}

.detail-item:last-child {
	margin-bottom: 0;
}

.detail-label {
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.detail-value {
	color: var(--bs-body-color);
}

.detail-value.success {
	color: var(--success-color);
}

/* Warning/Notice Section */
.notice-list {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.notice-item {
	display: flex;
	align-items: flex-start;
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.8);
}

.notice-item svg {
	width: 1rem;
	height: 1rem;
	color: var(--success-color);
	margin-right: 0.5rem;
	margin-top: 0.125rem;
	flex-shrink: 0;
}

.warning-header svg {
	color: var(--warning-color);
}

/* Loading Animation */
.loading-spinner {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Responsive Design */
@media (max-width: 768px) {
	.swap-container {
		padding: 1rem;
	}

	.swap-title {
		font-size: 1.5rem;
	}

	.swap-card {
		padding: 1rem;
	}

	.slippage-buttons {
		flex-direction: column;
	}

	.slippage-btn {
		width: 100%;
	}
}

@media (max-width: 480px) {
	.token-header {
		flex-direction: column;
		align-items: stretch;
		gap: 0.5rem;
	}

	.token-balance {
		text-align: left;
	}

	.token-footer {
		flex-direction: column;
		align-items: stretch;
		gap: 0.5rem;
	}

	.max-button {
		align-self: flex-end;
		width: fit-content;
	}
}

/* Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.swap-card {
	animation: fadeIn 0.5s ease-out;
}

.swap-card:nth-child(1) {
	animation-delay: 0.1s;
}

.swap-card:nth-child(2) {
	animation-delay: 0.2s;
}

.swap-card:nth-child(3) {
	animation-delay: 0.3s;
}

/* Focus styles for accessibility */
.token-select:focus,
.token-amount-input:focus,
.slippage-input:focus,
.swap-execute-btn:focus,
.swap-direction-btn:focus,
.max-button:focus {
	outline: 2px solid var(--bs-primary);
	outline-offset: 2px;
}

/* Hover effects for better UX */
.token-input-container:focus-within {
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.1);
}

/* Custom scrollbar for select elements */
.token-select option {
	background: var(--bs-light);
	color: var(--bs-body-color);
	padding: 0.5rem;
}

/* Withdraw to Fiat/Gold Page Styles */

:root {
	--bs-body-font-family: "DM Sans", serif;
	--bs-heading-font-family: "Roobert PRO Bold";
	--bs-body-bg: #070710;
	--bs-body-bg-rgb: 7, 7, 16;
	--bs-body-color: #d4d5f1;
	--bs-body-color-rgb: 212, 213, 241;
	--bs-heading-color: #ccceef;
	--bs-light: #0f1021;
	--bs-light-rgb: 15, 16, 33;
	--bs-primary: #ff3bd4;
	--bs-primary-rgb: 255, 59, 212;
	--bs-secondary: #7130c3;
	--bs-secondary-rgb: 113, 48, 195;
	--bs-border-color: #2f336d;
	--bs-border-color-translucent: 47, 51, 109;
	--bs-dark: #070710;
	--bs-dark-rgb: 7, 7, 16;

	/* Additional theme colors */
	--gold-primary: #d4af37;
	--gold-secondary: #b8941f;
	--gold-light: #e6c547;
	--success-color: #10b981;
	--warning-color: #f59e0b;
	--error-color: #ef4444;
}

/* Base styles */
.withdraw-container {
	min-height: 100vh;
	background: linear-gradient(
		135deg,
		var(--bs-body-bg) 0%,
		var(--bs-light) 50%,
		var(--bs-dark) 100%
	);
	padding: 1.5rem;
	color: var(--bs-body-color);
	font-family: var(--bs-body-font-family);
}

.withdraw-wrapper {
	max-width: 64rem;
	margin: 0 auto;
}

/* Header */
.withdraw-header {
	margin-bottom: 2rem;
}

.withdraw-title {
	font-size: 1.875rem;
	font-weight: 700;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
	font-family: var(--bs-heading-font-family);
}

.withdraw-subtitle {
	color: var(--bs-body-color);
	opacity: 0.8;
}

/* Grid Layout */
.withdraw-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 1.5rem;
}

@media (min-width: 1024px) {
	.withdraw-grid {
		grid-template-columns: 2fr 1fr;
	}
}

/* Card Styles */
.withdraw-card {
	background: rgba(15, 16, 33, 0.8);
	border-radius: 1rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
		0 10px 10px -5px rgba(0, 0, 0, 0.2);
	padding: 1.5rem;
	border: 1px solid rgba(var(--bs-border-color-translucent), 0.3);
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
}

.withdraw-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 25px 35px -5px rgba(0, 0, 0, 0.4),
		0 15px 15px -5px rgba(0, 0, 0, 0.3);
	border-color: rgba(var(--bs-primary-rgb), 0.3);
}

.withdraw-main-card {
	grid-column: span 2;
}

@media (min-width: 1024px) {
	.withdraw-main-card {
		grid-column: span 1;
	}
}

/* Section Headers */
.section-header {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 1.5rem;
	display: flex;
	align-items: center;
	color: var(--bs-heading-color);
	font-family: var(--bs-heading-font-family);
}

.section-header svg {
	margin-right: 0.5rem;
	color: var(--bs-primary);
}

/* Form Elements */
.form-group {
	margin-bottom: 1.5rem;
}

.form-label {
	display: block;
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
}

.form-select,
.form-input {
	width: 100%;
	padding: 0.75rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background-color: rgba(var(--bs-light-rgb), 0.6);
	color: var(--bs-body-color);
	font-size: 0.875rem;
	transition: all 0.3s ease;
	font-family: inherit;
}

.form-select:focus,
.form-input:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.2);
	background-color: rgba(var(--bs-light-rgb), 0.8);
}

.form-input::placeholder {
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

.form-input-large {
	font-size: 1.125rem;
	padding: 0.875rem 1rem;
}

/* Withdrawal Type Selection */
.withdraw-type-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.75rem;
	margin-bottom: 1.5rem;
}

.withdraw-type-option {
	padding: 1rem;
	border: 2px solid var(--bs-border-color);
	border-radius: 0.75rem;
	cursor: pointer;
	transition: all 0.3s ease;
	background: rgba(var(--bs-dark-rgb), 0.4);
}

.withdraw-type-option:hover {
	border-color: rgba(var(--bs-primary-rgb), 0.5);
	background: rgba(var(--bs-dark-rgb), 0.6);
}

.withdraw-type-option.active {
	border-color: var(--bs-primary);
	background: rgba(var(--bs-primary-rgb), 0.1);
}

.withdraw-type-content {
	display: flex;
	align-items: center;
}

.withdraw-type-content svg {
	width: 1.25rem;
	height: 1.25rem;
	margin-right: 0.75rem;
	color: var(--bs-primary);
}

.withdraw-type-content.gold svg {
	color: var(--gold-primary);
}

.withdraw-type-title {
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.25rem;
}

.withdraw-type-desc {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

/* Amount Input with Suffix */
.amount-input-container {
	position: relative;
}

.amount-input-suffix {
	position: absolute;
	right: 0.75rem;
	top: 50%;
	transform: translateY(-50%);
	color: rgba(var(--bs-body-color-rgb), 0.6);
	font-size: 0.875rem;
	pointer-events: none;
}

.amount-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 0.5rem;
	font-size: 0.875rem;
}

.amount-available {
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.max-button {
	background: transparent;
	border: none;
	color: var(--bs-primary);
	cursor: pointer;
	padding: 0.25rem 0.5rem;
	border-radius: 0.25rem;
	transition: all 0.3s ease;
	font-family: inherit;
}

.max-button:hover {
	background: rgba(var(--bs-primary-rgb), 0.1);
	color: var(--gold-primary);
}

/* Currency Info */
.currency-info {
	margin-top: 0.5rem;
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.currency-amount {
	font-weight: 600;
	color: var(--bs-primary);
}

/* Withdrawal Methods */
.withdraw-methods {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.withdraw-method {
	padding: 1rem;
	border: 2px solid var(--bs-border-color);
	border-radius: 0.75rem;
	cursor: pointer;
	transition: all 0.3s ease;
	background: rgba(var(--bs-dark-rgb), 0.4);
}

.withdraw-method:hover {
	border-color: rgba(var(--bs-primary-rgb), 0.5);
	background: rgba(var(--bs-dark-rgb), 0.6);
}

.withdraw-method.active {
	border-color: var(--bs-primary);
	background: rgba(var(--bs-primary-rgb), 0.1);
}

.withdraw-method-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.withdraw-method-info {
	display: flex;
	align-items: center;
}

.withdraw-method-info svg {
	width: 1.25rem;
	height: 1.25rem;
	margin-right: 0.75rem;
	color: var(--bs-primary);
}

.withdraw-method-title {
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.25rem;
}

.withdraw-method-desc {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

/* Bank Details */
.bank-details {
	margin-bottom: 1.5rem;
}

.bank-details-title {
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 1rem;
}

.bank-details-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 1rem;
}

@media (min-width: 768px) {
	.bank-details-grid {
		grid-template-columns: 1fr 1fr;
	}
}

/* Gold Options */
.gold-options {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.gold-option {
	padding: 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.75rem;
	transition: all 0.3s ease;
	background: rgba(var(--bs-dark-rgb), 0.4);
}

.gold-option:hover {
	border-color: rgba(var(--gold-primary), 0.5);
	background: rgba(var(--bs-dark-rgb), 0.6);
}

.gold-option-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.gold-option-title {
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.25rem;
}

.gold-option-desc {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.gold-radio {
	width: 1rem;
	height: 1rem;
	accent-color: var(--gold-primary);
}

.gold-equivalent {
	margin-top: 1rem;
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.gold-amount {
	font-weight: 600;
	color: var(--gold-primary);
}

/* Main Withdraw Button */
.withdraw-submit-btn {
	width: 100%;
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	padding: 0.75rem 1.5rem;
	border-radius: 0.75rem;
	font-size: 1rem;
	font-weight: 600;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: inherit;
}

.withdraw-submit-btn:hover:not(:disabled) {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.4);
}

.withdraw-submit-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

.withdraw-submit-btn svg {
	width: 1.25rem;
	height: 1.25rem;
	margin-right: 0.5rem;
}

/* Sidebar */
.withdraw-sidebar {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

/* Balance Card */
.balance-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.75rem;
}

.balance-item:last-child {
	margin-bottom: 0;
}

.balance-label {
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.balance-value {
	font-weight: 600;
	color: var(--bs-heading-color);
}

/* Processing Times */
.processing-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.75rem;
	font-size: 0.875rem;
}

.processing-item:last-child {
	margin-bottom: 0;
}

.processing-label {
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.processing-value {
	color: var(--bs-body-color);
}

/* Notice Section */
.notice-list {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.notice-item {
	display: flex;
	align-items: flex-start;
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.8);
}

.notice-item svg {
	width: 1rem;
	height: 1rem;
	color: var(--success-color);
	margin-right: 0.5rem;
	margin-top: 0.125rem;
	flex-shrink: 0;
}

.warning-header svg {
	color: var(--warning-color);
}

/* Loading Animation */
.loading-spinner {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Responsive Design */
@media (max-width: 768px) {
	.withdraw-container {
		padding: 1rem;
	}

	.withdraw-title {
		font-size: 1.5rem;
	}

	.withdraw-card {
		padding: 1rem;
	}

	.withdraw-type-grid {
		grid-template-columns: 1fr;
	}

	.withdraw-methods {
		gap: 0.5rem;
	}
}

@media (max-width: 480px) {
	.withdraw-method-content {
		flex-direction: column;
		align-items: stretch;
		gap: 0.5rem;
	}

	.amount-footer {
		flex-direction: column;
		align-items: stretch;
		gap: 0.5rem;
	}

	.max-button {
		align-self: flex-end;
		width: fit-content;
	}
}

/* Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.withdraw-card {
	animation: fadeIn 0.5s ease-out;
}

.withdraw-card:nth-child(1) {
	animation-delay: 0.1s;
}

.withdraw-card:nth-child(2) {
	animation-delay: 0.2s;
}

.withdraw-card:nth-child(3) {
	animation-delay: 0.3s;
}

/* Focus styles for accessibility */
.form-select:focus,
.form-input:focus,
.withdraw-submit-btn:focus,
.max-button:focus,
.withdraw-type-option:focus,
.withdraw-method:focus {
	outline: 2px solid var(--bs-primary);
	outline-offset: 2px;
}

/* Hover effects for better UX */
.withdraw-type-option:focus-within,
.withdraw-method:focus-within {
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.1);
}

/* Special styling for gold elements */
.gold-themed .section-header svg {
	color: var(--gold-primary);
}

.gold-themed .withdraw-type-content svg {
	color: var(--gold-primary);
}

.gold-themed .withdraw-submit-btn {
	background: linear-gradient(
		135deg,
		var(--gold-primary) 0%,
		var(--gold-secondary) 100%
	);
}

.gold-themed .withdraw-submit-btn:hover:not(:disabled) {
	box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
}

/* Transaction History Page Styles */

:root {
	--bs-body-font-family: "DM Sans", serif;
	--bs-heading-font-family: "Roobert PRO Bold";
	--bs-body-bg: #070710;
	--bs-body-bg-rgb: 7, 7, 16;
	--bs-body-color: #d4d5f1;
	--bs-body-color-rgb: 212, 213, 241;
	--bs-heading-color: #ccceef;
	--bs-light: #0f1021;
	--bs-light-rgb: 15, 16, 33;
	--bs-primary: #ff3bd4;
	--bs-primary-rgb: 255, 59, 212;
	--bs-secondary: #7130c3;
	--bs-secondary-rgb: 113, 48, 195;
	--bs-border-color: #2f336d;
	--bs-border-color-translucent: 47, 51, 109;
	--bs-dark: #070710;
	--bs-dark-rgb: 7, 7, 16;

	/* Additional theme colors */
	--gold-primary: #d4af37;
	--gold-secondary: #b8941f;
	--gold-light: #e6c547;
	--success-color: #10b981;
	--warning-color: #f59e0b;
	--error-color: #ef4444;
}

/* Base styles */
.transaction-container {
	min-height: 100vh;
	background: linear-gradient(
		135deg,
		var(--bs-body-bg) 0%,
		var(--bs-light) 50%,
		var(--bs-dark) 100%
	);
	padding: 1.5rem;
	color: var(--bs-body-color);
	font-family: var(--bs-body-font-family);
}

.transaction-wrapper {
	max-width: 80rem;
	margin: 0 auto;
}

/* Header */
.transaction-header {
	margin-bottom: 2rem;
}

.transaction-header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 1rem;
}

.transaction-title {
	font-size: 1.875rem;
	font-weight: 700;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
	font-family: var(--bs-heading-font-family);
}

.transaction-subtitle {
	color: var(--bs-body-color);
	opacity: 0.8;
}

/* Export Button */
.export-btn {
	display: flex;
	align-items: center;
	padding: 0.5rem 1rem;
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	border: none;
	border-radius: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
	font-family: inherit;
}

.export-btn:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.4);
}

.export-btn svg {
	width: 1rem;
	height: 1rem;
	margin-right: 0.5rem;
}

/* Summary Cards */
.summary-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 1.5rem;
	margin-bottom: 2rem;
}

.summary-card {
	background: rgba(15, 16, 33, 0.8);
	border-radius: 1rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
		0 10px 10px -5px rgba(0, 0, 0, 0.2);
	padding: 1.5rem;
	border: 1px solid rgba(var(--bs-border-color-translucent), 0.3);
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
}

.summary-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 25px 35px -5px rgba(0, 0, 0, 0.4),
		0 15px 15px -5px rgba(0, 0, 0, 0.3);
	border-color: rgba(var(--bs-primary-rgb), 0.3);
}

.summary-label {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
	margin-bottom: 0.5rem;
}

.summary-value {
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--bs-heading-color);
}

.summary-value.success {
	color: var(--success-color);
}

.summary-value.warning {
	color: var(--warning-color);
}

/* Filters Section */
.filters-card {
	background: rgba(15, 16, 33, 0.8);
	border-radius: 1rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
		0 10px 10px -5px rgba(0, 0, 0, 0.2);
	padding: 1.5rem;
	border: 1px solid rgba(var(--bs-border-color-translucent), 0.3);
	backdrop-filter: blur(10px);
	margin-bottom: 2rem;
}

.filters-container {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

@media (min-width: 1024px) {
	.filters-container {
		flex-direction: row;
	}
}

.search-container {
	flex: 1;
	position: relative;
}

.search-icon {
	position: absolute;
	left: 0.75rem;
	top: 50%;
	transform: translateY(-50%);
	width: 1.25rem;
	height: 1.25rem;
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

.search-input {
	width: 100%;
	padding: 0.75rem 1rem 0.75rem 2.5rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background-color: rgba(var(--bs-light-rgb), 0.6);
	color: var(--bs-body-color);
	font-size: 0.875rem;
	transition: all 0.3s ease;
	font-family: inherit;
}

.search-input:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.2);
	background-color: rgba(var(--bs-light-rgb), 0.8);
}

.search-input::placeholder {
	color: rgba(var(--bs-body-color-rgb), 0.5);
}

.filter-select {
	padding: 0.75rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background-color: rgba(var(--bs-light-rgb), 0.6);
	color: var(--bs-body-color);
	font-size: 0.875rem;
	transition: all 0.3s ease;
	font-family: inherit;
	min-width: 150px;
}

.filter-select:focus {
	outline: none;
	border-color: var(--bs-primary);
	box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.2);
	background-color: rgba(var(--bs-light-rgb), 0.8);
}

.filter-select option {
	background: var(--bs-light);
	color: var(--bs-body-color);
	padding: 0.5rem;
}

.more-filters-btn {
	display: flex;
	align-items: center;
	padding: 0.75rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background: transparent;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: inherit;
}

.more-filters-btn:hover {
	background: rgba(var(--bs-light-rgb), 0.8);
	border-color: var(--bs-primary);
	color: var(--bs-heading-color);
}

.more-filters-btn svg {
	width: 1.25rem;
	height: 1.25rem;
	margin-right: 0.5rem;
}

/* Transactions Table */
.transactions-table-card {
	background: rgba(15, 16, 33, 0.8);
	border-radius: 1rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
		0 10px 10px -5px rgba(0, 0, 0, 0.2);
	border: 1px solid rgba(var(--bs-border-color-translucent), 0.3);
	backdrop-filter: blur(10px);
	overflow: hidden;
}

.table-container {
	overflow-x: auto;
}

.transactions-table {
	width: 100%;
	border-collapse: collapse;
}

.table-header {
	background: rgba(var(--bs-dark-rgb), 0.5);
}

.table-header th {
	padding: 1rem 1.5rem;
	text-align: left;
	font-size: 0.875rem;
	font-weight: 500;
	color: rgba(var(--bs-body-color-rgb), 0.7);
	text-transform: uppercase;
	letter-spacing: 0.05em;
	border-bottom: 1px solid var(--bs-border-color);
}

.table-body {
	background: transparent;
}

.table-row {
	border-bottom: 1px solid rgba(var(--bs-border-color-translucent), 0.2);
	transition: all 0.3s ease;
}

.table-row:hover {
	background: rgba(var(--bs-light-rgb), 0.3);
}

.table-cell {
	padding: 1rem 1.5rem;
	white-space: nowrap;
}

/* Transaction Type Cell */
.type-cell {
	display: flex;
	align-items: center;
}

.type-icon {
	width: 1rem;
	height: 1rem;
	margin-right: 0.75rem;
}

.type-icon.buy,
.type-icon.transfer {
	color: var(--success-color);
}

.type-icon.sell,
.type-icon.withdraw {
	color: var(--error-color);
}

.type-icon.swap,
.type-icon.stake,
.type-icon.unstake {
	color: var(--bs-primary);
}

.type-info {
	display: flex;
	flex-direction: column;
}

.type-name {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
	text-transform: capitalize;
	margin-bottom: 0.25rem;
}

.type-hash {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.6);
	font-family: "Courier New", monospace;
}

/* Amount and Value Cells */
.amount-value {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
}

.value-amount {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
}

/* From/To Cell */
.from-to-cell {
	font-size: 0.875rem;
	color: var(--bs-body-color);
}

.from-to-cell div {
	margin-bottom: 0.25rem;
}

.from-to-cell div:last-child {
	margin-bottom: 0;
}

/* Date Cell */
.date-value {
	font-size: 0.875rem;
	color: var(--bs-body-color);
}

/* Status Cell */
.status-badge {
	display: inline-flex;
	padding: 0.25rem 0.5rem;
	font-size: 0.75rem;
	font-weight: 600;
	border-radius: 9999px;
	text-transform: capitalize;
}

.status-completed {
	background: rgba(16, 185, 129, 0.1);
	color: var(--success-color);
}

.status-pending {
	background: rgba(245, 158, 11, 0.1);
	color: var(--warning-color);
}

.status-failed {
	background: rgba(239, 68, 68, 0.1);
	color: var(--error-color);
}

/* Fee Cell */
.fee-value {
	font-size: 0.875rem;
	color: var(--bs-body-color);
}

/* Actions Cell */
.action-btn {
	color: rgba(var(--bs-body-color-rgb), 0.6);
	background: transparent;
	border: none;
	cursor: pointer;
	padding: 0.5rem;
	border-radius: 0.25rem;
	transition: all 0.3s ease;
}

.action-btn:hover {
	color: var(--bs-primary);
	background: rgba(var(--bs-primary-rgb), 0.1);
}

.action-btn svg {
	width: 1rem;
	height: 1rem;
}

/* Pagination */
.pagination-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 2rem;
	flex-wrap: wrap;
	gap: 1rem;
}

.pagination-info {
	font-size: 0.875rem;
	color: rgba(var(--bs-body-color-rgb), 0.7);
}

.pagination-buttons {
	display: flex;
	gap: 0.5rem;
}

.pagination-btn {
	padding: 0.5rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background: transparent;
	color: var(--bs-body-color);
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: inherit;
}

.pagination-btn:hover {
	background: rgba(var(--bs-light-rgb), 0.8);
	border-color: var(--bs-primary);
	color: var(--bs-heading-color);
}

.pagination-btn.active {
	background: linear-gradient(
		135deg,
		var(--bs-primary) 0%,
		var(--bs-secondary) 100%
	);
	color: white;
	border-color: var(--bs-primary);
}

.pagination-btn.active:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
	.transaction-container {
		padding: 1rem;
	}

	.transaction-title {
		font-size: 1.5rem;
	}

	.transaction-header-content {
		flex-direction: column;
		align-items: stretch;
	}

	.summary-grid {
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
		gap: 1rem;
	}

	.summary-card {
		padding: 1rem;
	}

	.filters-card {
		padding: 1rem;
	}

	.table-header th,
	.table-cell {
		padding: 0.75rem 1rem;
	}

	.pagination-container {
		flex-direction: column;
		align-items: stretch;
	}

	.pagination-buttons {
		justify-content: center;
	}
}

@media (max-width: 480px) {
	.type-cell {
		flex-direction: column;
		align-items: flex-start;
		gap: 0.5rem;
	}

	.type-icon {
		margin-right: 0;
	}

	.table-header th,
	.table-cell {
		padding: 0.5rem 0.75rem;
	}

	.from-to-cell {
		font-size: 0.75rem;
	}

	.type-hash {
		font-size: 0.75rem;
	}
}

/* Animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.summary-card,
.filters-card,
.transactions-table-card {
	animation: fadeIn 0.5s ease-out;
}

.summary-card:nth-child(1) {
	animation-delay: 0.1s;
}

.summary-card:nth-child(2) {
	animation-delay: 0.2s;
}

.summary-card:nth-child(3) {
	animation-delay: 0.3s;
}

.summary-card:nth-child(4) {
	animation-delay: 0.4s;
}

/* Focus styles for accessibility */
.search-input:focus,
.filter-select:focus,
.more-filters-btn:focus,
.export-btn:focus,
.action-btn:focus,
.pagination-btn:focus {
	outline: 2px solid var(--bs-primary);
	outline-offset: 2px;
}

/* Loading state for buttons */
.export-btn:disabled,
.more-filters-btn:disabled,
.pagination-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.export-btn:disabled:hover,
.more-filters-btn:disabled:hover,
.pagination-btn:disabled:hover {
	transform: none;
	box-shadow: none;
}

/* Empty state */
.empty-state {
	text-align: center;
	padding: 3rem 1rem;
	color: rgba(var(--bs-body-color-rgb), 0.6);
}

.empty-state-title {
	font-size: 1.125rem;
	font-weight: 500;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
}

.empty-state-description {
	font-size: 0.875rem;
}

/* Table scroll indicator */
.table-container::-webkit-scrollbar {
	height: 8px;
}

.table-container::-webkit-scrollbar-track {
	background: rgba(var(--bs-border-color-translucent), 0.1);
	border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
	background: rgba(var(--bs-primary-rgb), 0.3);
	border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
	background: rgba(var(--bs-primary-rgb), 0.5);
}
/* TrendingTokens.css */

.trending-tokens-container {
	min-height: 100vh;
	background: linear-gradient(
		135deg,
		var(--bs-body-bg) 0%,
		var(--bs-light) 100%
	);
	padding: 1.5rem;
	font-family: var(--bs-body-font-family);
	color: var(--bs-body-color);
	max-width: 64rem;
	margin: 0 auto;
}

/* Header Section */
.trending-tokens-header {
	margin-bottom: 2rem;
}

.header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 1rem;
}

.header-text h1 {
	font-size: 2rem;
	font-weight: bold;
	color: var(--bs-heading-color);
	margin-bottom: 0.5rem;
	font-family: var(--bs-heading-font-family);
}

.header-text p {
	color: var(--bs-body-color);
	opacity: 0.8;
}

.refresh-button {
	display: flex;
	align-items: center;
	padding: 0.75rem 1rem;
	background: var(--gold-primary);
	color: var(--bs-dark);
	border: none;
	border-radius: 0.5rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.refresh-button:hover {
	background: var(--gold-secondary);
	transform: translateY(-2px);
}

.refresh-button svg {
	width: 1rem;
	height: 1rem;
	margin-right: 0.5rem;
}

/* Filters Section */
.filters-container {
	background: var(--bs-light);
	border-radius: 1rem;
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
	padding: 1.5rem;
	margin-bottom: 2rem;
	border: 1px solid var(--bs-border-color);
}

.filters-content {
	display: flex;
	flex-direction: column;
	gap: 1rem;
	align-items: center;
	justify-content: space-between;
}

@media (min-width: 1024px) {
	.filters-content {
		flex-direction: row;
	}
}

.filters-left {
	display: flex;
	align-items: center;
	gap: 1rem;
	flex-wrap: wrap;
}

.filter-label {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.filter-label svg {
	width: 1.25rem;
	height: 1.25rem;
	color: var(--bs-body-color);
}

.filter-label span {
	font-weight: 500;
	color: var(--bs-heading-color);
}

.timeframe-buttons {
	display: flex;
	gap: 0.25rem;
	background: var(--bs-body-bg);
	border-radius: 0.5rem;
	padding: 0.25rem;
}

.timeframe-button {
	padding: 0.5rem 0.75rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	font-weight: 500;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	background: transparent;
	color: var(--bs-body-color);
}

.timeframe-button.active {
	background: var(--bs-light);
	color: var(--gold-primary);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.timeframe-button:hover:not(.active) {
	color: var(--bs-heading-color);
}

.sort-select {
	padding: 0.75rem 1rem;
	border: 1px solid var(--bs-border-color);
	border-radius: 0.5rem;
	background: var(--bs-light);
	color: var(--bs-body-color);
	font-size: 0.875rem;
	outline: none;
	transition: border-color 0.3s ease;
}

.sort-select:focus {
	border-color: var(--gold-primary);
	box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.last-updated {
	font-size: 0.875rem;
	color: var(--bs-body-color);
	opacity: 0.7;
}

/* Main Content Grid */
/* .main-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
} */

@media (min-width: 1024px) {
	/* .main-grid {
    grid-template-columns: 3fr 1fr;
  } */
}

/* Top Trending Section */
.top-trending-section {
	margin-bottom: 2rem;
}

.section-title {
	font-size: 1.5rem;
	font-weight: bold;
	color: var(--bs-heading-color);
	margin-bottom: 1.5rem;
	display: flex;
	align-items: center;
	font-family: var(--bs-heading-font-family);
}

.section-title svg {
	margin-right: 0.5rem;
	color: var(--gold-primary);
}

.trending-cards-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 1rem;
}

@media (min-width: 768px) {
	.trending-cards-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (min-width: 1024px) {
	.trending-cards-grid {
		grid-template-columns: repeat(1, 1fr);
	}
}

.token-card {
	background: var(--bs-light);
	border-radius: 1rem;
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
	padding: 1.5rem;
	transition: all 0.3s ease;
	border: 1px solid var(--bs-border-color);
}

.token-card:hover {
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
	transform: translateY(-4px);
}

.token-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 1rem;
}

.token-info {
	display: flex;
	align-items: center;
}

.token-icon {
	font-size: 1.5rem;
	margin-right: 0.75rem;
}

.token-symbol {
	font-weight: bold;
	color: var(--bs-heading-color);
	font-size: 1rem;
}

.token-name {
	font-size: 0.875rem;
	color: var(--bs-body-color);
	opacity: 0.8;
}

.hot-badge {
	background: rgba(255, 59, 212, 0.2);
	color: var(--bs-primary);
	padding: 0.25rem 0.5rem;
	border-radius: 1rem;
	font-size: 0.75rem;
	font-weight: 500;
}

.token-price {
	margin-bottom: 1rem;
}

.price-value {
	font-size: 1.5rem;
	font-weight: bold;
	color: var(--bs-heading-color);
}

.price-change {
	display: flex;
	align-items: center;
	margin-top: 0.25rem;
	font-weight: 500;
}

.price-change.positive {
	color: var(--success-color);
}

.price-change.negative {
	color: var(--error-color);
}

.price-change svg {
	width: 1rem;
	height: 1rem;
	margin-right: 0.25rem;
}

.token-stats {
	margin-bottom: 1rem;
}

.stat-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.5rem;
	font-size: 0.875rem;
}

.stat-label {
	color: var(--bs-body-color);
	opacity: 0.8;
}

.stat-value {
	font-weight: 500;
	color: var(--bs-heading-color);
}

.view-details-button {
	width: 100%;
	margin-top: 1rem;
	background: var(--gold-primary);
	color: var(--bs-dark);
	padding: 0.75rem 1rem;
	border: none;
	border-radius: 0.5rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.view-details-button:hover {
	background: var(--gold-secondary);
}

/* Table Section */
.table-section h2 {
	font-size: 1.5rem;
	font-weight: bold;
	color: var(--bs-heading-color);
	margin-bottom: 1.5rem;
	font-family: var(--bs-heading-font-family);
}

.table-container {
	background: var(--bs-light);
	border-radius: 1rem;
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
	overflow: hidden;
	border: 1px solid var(--bs-border-color);
}

.table-scroll {
	overflow-x: auto;
}

.tokens-table {
	width: 100%;
	border-collapse: collapse;
}

.table-header {
	background: var(--bs-body-bg);
}

.table-header th {
	padding: 1rem 1.5rem;
	text-align: left;
	font-size: 0.75rem;
	font-weight: 500;
	color: var(--bs-body-color);
	text-transform: uppercase;
	letter-spacing: 0.05em;
	border-bottom: 1px solid var(--bs-border-color);
}

.table-body tr {
	border-bottom: 1px solid var(--bs-border-color);
	transition: background-color 0.2s ease;
}

.table-body tr:hover {
	background: var(--bs-body-bg);
}

.table-body td {
	padding: 1rem 1.5rem;
	white-space: nowrap;
}

.rank-cell {
	display: flex;
	align-items: center;
}

.rank-number {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
}

.trending-icon {
	width: 1rem;
	height: 1rem;
	color: var(--bs-primary);
	margin-left: 0.5rem;
}

.token-cell {
	display: flex;
	align-items: center;
}

.token-cell-icon {
	font-size: 1.25rem;
	margin-right: 0.75rem;
}

.token-cell-symbol {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
}

.token-cell-name {
	font-size: 0.875rem;
	color: var(--bs-body-color);
	opacity: 0.8;
}

.price-cell {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--bs-heading-color);
}

.change-cell {
	display: flex;
	align-items: center;
	font-size: 0.875rem;
	font-weight: 500;
}

.change-cell.positive {
	color: var(--success-color);
}

.change-cell.negative {
	color: var(--error-color);
}

.change-cell svg {
	width: 1rem;
	height: 1rem;
	margin-right: 0.25rem;
}

.volume-cell,
.market-cap-cell {
	font-size: 0.875rem;
	color: var(--bs-heading-color);
}

.actions-cell {
	display: flex;
	gap: 0.5rem;
}

.action-button {
	padding: 0.25rem;
	background: none;
	border: none;
	color: var(--bs-body-color);
	opacity: 0.6;
	cursor: pointer;
	transition: all 0.3s ease;
}

.action-button:hover {
	opacity: 1;
	color: var(--gold-primary);
}

.action-button svg {
	width: 1rem;
	height: 1rem;
}

/* Sidebar */
.sidebar {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

.sidebar-card {
	background: var(--bs-light);
	border-radius: 1rem;
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
	padding: 1.5rem;
	border: 1px solid var(--bs-border-color);
}

.sidebar-title {
	font-size: 1.125rem;
	font-weight: 600;
	margin-bottom: 1rem;
	display: flex;
	align-items: center;
	color: var(--bs-heading-color);
}

.sidebar-title svg {
	margin-right: 0.5rem;
}

.sidebar-title svg.green {
	color: var(--success-color);
}

.sidebar-title svg.red {
	color: var(--error-color);
}

.overview-stats {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.overview-stat {
	display: flex;
	justify-content: space-between;
}

.overview-stat-label {
	color: var(--bs-body-color);
	opacity: 0.8;
}

.overview-stat-value {
	font-weight: 600;
	color: var(--bs-heading-color);
}

.gainers-losers-list {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.gainer-loser-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.gainer-loser-token {
	display: flex;
	align-items: center;
}

.gainer-loser-icon {
	font-size: 1.125rem;
	margin-right: 0.5rem;
}

.gainer-loser-symbol {
	font-weight: 500;
	color: var(--bs-heading-color);
}

.gainer-change {
	color: var(--success-color);
	font-weight: 500;
}

.loser-change {
	color: var(--error-color);
	font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
	.trending-tokens-container {
		padding: 1rem;
	}

	.header-content {
		flex-direction: column;
		align-items: flex-start;
	}

	.filters-content {
		flex-direction: column;
		align-items: flex-start;
	}

	.filters-left {
		flex-direction: column;
		align-items: flex-start;
	}

	.timeframe-buttons {
		width: 100%;
	}

	.sort-select {
		width: 100%;
	}

	.trending-cards-grid {
		grid-template-columns: 1fr;
	}

	.table-scroll {
		font-size: 0.75rem;
	}

	.table-header th,
	.table-body td {
		padding: 0.75rem 0.5rem;
	}
}

@media (max-width: 480px) {
	.header-text h1 {
		font-size: 1.5rem;
	}

	.section-title {
		font-size: 1.25rem;
	}

	.token-card {
		padding: 1rem;
	}

	.price-value {
		font-size: 1.25rem;
	}
}

/* Animation for refresh button */
@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.refresh-button:active svg {
	animation: spin 0.5s linear;
}

/* Focus styles for accessibility */
.timeframe-button:focus,
.sort-select:focus,
.view-details-button:focus,
.action-button:focus,
.refresh-button:focus {
	outline: 2px solid var(--gold-primary);
	outline-offset: 2px;
}

/* Custom scrollbar */
.table-scroll::-webkit-scrollbar {
	height: 8px;
}

.table-scroll::-webkit-scrollbar-track {
	background: var(--bs-body-bg);
	border-radius: 4px;
}

.table-scroll::-webkit-scrollbar-thumb {
	background: var(--bs-border-color);
	border-radius: 4px;
}

.table-scroll::-webkit-scrollbar-thumb:hover {
	background: var(--gold-primary);
}

/* AINT Token Information Card Styles */
.aint-info-card {
	background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
	border: 2px solid #ff3bd4;
	border-radius: 16px;
	max-width: 1600px;
	padding: 2rem;
	margin: 2rem 30px;
	box-shadow: 0 8px 32px rgba(255, 59, 212, 0.15);
	position: relative;
	overflow: hidden;
}

.aint-info-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg, #ff3bd4, #ff6b9d, #ff3bd4);
	animation: shimmer 2s infinite;
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

.aint-info-header {
	display: flex;
	align-items: center;
	gap: 1rem;
	margin-bottom: 2rem;
}

.aint-badge {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	background: linear-gradient(135deg, #ff3bd4, #ff6b9d);
	color: white;
	padding: 0.5rem 1rem;
	border-radius: 20px;
	font-size: 0.875rem;
	font-weight: 600;
}

.aint-badge-icon {
	width: 16px;
	height: 16px;
}

.aint-info-header h2 {
	color: #d4d5f1;
	font-size: 1.75rem;
	font-weight: 700;
	margin: 0;
}

.aint-info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 2rem;
}

.aint-price-section,
.aint-contract-section,
.aint-actions-section {
	background: rgba(255, 59, 212, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 12px;
	padding: 1.5rem;
}

.aint-price-section h3,
.aint-contract-section h3,
.aint-actions-section h3 {
	color: #d4d5f1;
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 1rem;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.price-display-grid {
	display: grid;
	gap: 1rem;
}

.price-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.75rem;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.price-label {
	color: #a0a0b8;
	font-size: 0.875rem;
	font-weight: 500;
}

.price-value {
	color: #d4d5f1;
	font-size: 1.125rem;
	font-weight: 600;
}

.contract-info {
	display: grid;
	gap: 1rem;
}

.contract-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.75rem;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 59, 212, 0.1);
}

.contract-item.full-width {
	flex-direction: column;
	align-items: flex-start;
	gap: 0.5rem;
}

.contract-label {
	color: #a0a0b8;
	font-size: 0.875rem;
	font-weight: 500;
}

.contract-value {
	color: #d4d5f1;
	font-size: 0.875rem;
	font-weight: 500;
}

.contract-address-container {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	width: 100%;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 6px;
	padding: 0.5rem;
}

.contract-address {
	color: #d4d5f1;
	font-size: 0.75rem;
	font-family: "Courier New", monospace;
	word-break: break-all;
	flex: 1;
}

.copy-btn {
	background: linear-gradient(135deg, #ff3bd4, #ff6b9d);
	border: none;
	border-radius: 4px;
	padding: 0.25rem;
	color: white;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.copy-btn:hover {
	transform: scale(1.1);
	box-shadow: 0 2px 8px rgba(255, 59, 212, 0.4);
}

.action-buttons {
	display: grid;
	gap: 0.75rem;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	background: linear-gradient(135deg, #1a1a2e, #16213e);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 8px;
	padding: 0.75rem 1rem;
	color: #d4d5f1;
	text-decoration: none;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.action-btn:hover {
	background: linear-gradient(135deg, #ff3bd4, #ff6b9d);
	border-color: #ff3bd4;
	color: white;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(255, 59, 212, 0.3);
}

/* Enhanced Chart Styles */
.enhanced-chart-placeholder {
	position: relative;
	height: 400px;
	background: linear-gradient(
		135deg,
		rgba(255, 59, 212, 0.05),
		rgba(255, 107, 157, 0.05)
	);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 12px;
	overflow: hidden;
}

.chart-line {
	width: 2px !important;
	background: linear-gradient(to top, #ff3bd4, #ff6b9d) !important;
	border-radius: 1px;
}

.chart-timeframe {
	color: #a0a0b8;
	font-size: 0.75rem;
	margin-top: 0.5rem;
}

/* Price IDR Display */
.price-idr {
	color: #a0a0b8;
	font-size: 0.875rem;
	margin-top: 0.25rem;
	font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
	.aint-info-card {
		padding: 1.5rem;
		margin: 1rem 0;
	}

	.aint-info-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 0.75rem;
	}

	.aint-info-grid {
		grid-template-columns: 1fr;
		gap: 1.5rem;
	}

	.aint-price-section,
	.aint-contract-section,
	.aint-actions-section {
		padding: 1rem;
	}

	.price-item,
	.contract-item {
		padding: 0.5rem;
	}

	.contract-address {
		font-size: 0.7rem;
	}

	.action-btn {
		padding: 0.5rem 0.75rem;
		font-size: 0.8rem;
	}
}

@media (max-width: 480px) {
	.aint-info-card {
		padding: 1rem;
	}

	.aint-info-header h2 {
		font-size: 1.5rem;
	}

	.price-value {
		font-size: 1rem;
	}

	.contract-address {
		font-size: 0.65rem;
	}
}

/* Refresh Button Styles */
.refresh-btn {
	background: linear-gradient(135deg, #1a1a2e, #16213e);
	border: 1px solid rgba(255, 59, 212, 0.3);
	border-radius: 8px;
	padding: 0.5rem;
	color: #d4d5f1;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.refresh-btn:hover:not(:disabled) {
	background: linear-gradient(135deg, #ff3bd4, #ff6b9d);
	border-color: #ff3bd4;
	color: white;
	transform: scale(1.05);
}

.refresh-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* Loading States */
.loading-spinner {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* Error Message Styles */
.error-message {
	background: linear-gradient(
		135deg,
		rgba(239, 68, 68, 0.1),
		rgba(220, 38, 38, 0.1)
	);
	border: 1px solid rgba(239, 68, 68, 0.3);
	border-radius: 12px;
	padding: 1rem;
	margin: 1rem 0;
}

.error-message .flex {
	display: flex;
	align-items: center;
}

.error-message .text-red-700 {
	color: #b91c1c;
	font-weight: 500;
}

/* Price Trend Indicator */
.price-trend-indicator {
	margin-top: 0.5rem;
}

.trend-up {
	color: #00ff88;
	font-weight: 600;
}

.trend-down {
	color: #ff4757;
	font-weight: 600;
}

.trend-stable {
	color: #ffa502;
	font-weight: 600;
}

.trend-label {
	font-size: 0.875rem;
	display: flex;
	align-items: center;
	gap: 0.25rem;
}

/* Explorer Styles */
.explorer-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #070710 0%, #0f1021 100%);
	padding: 2rem 1rem;
}

.explorer-wrapper {
	max-width: 1400px;
	margin: 0 auto;
}

.explorer-header {
	text-align: center;
	margin-bottom: 2rem;
}

.explorer-title {
	font-size: 2.5rem;
	font-weight: 700;
	color: #ff3bd4;
	margin-bottom: 0.5rem;
	text-shadow: 0 2px 4px rgba(255, 59, 212, 0.3);
}

.explorer-subtitle {
	color: #d4d5f1;
	font-size: 1.1rem;
	opacity: 0.8;
}

.explorer-grid {
	display: grid;
	gap: 2rem;
	grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
	.explorer-grid {
		grid-template-columns: 2fr 1fr;
	}
}

.explorer-card {
	background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 16px;
	padding: 1.5rem;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	transition: all 0.3s ease;
}

.explorer-card:hover {
	border-color: rgba(255, 59, 212, 0.4);
	box-shadow: 0 12px 40px rgba(255, 59, 212, 0.1);
}

.section-header {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	font-size: 1.25rem;
	font-weight: 600;
	color: #d4d5f1;
	margin-bottom: 1.5rem;
}

.section-header svg {
	color: #ff3bd4;
	width: 20px;
	height: 20px;
}

.form-group {
	margin-bottom: 1.5rem;
}

.form-label {
	display: block;
	font-size: 0.875rem;
	font-weight: 500;
	color: #d4d5f1;
	margin-bottom: 0.5rem;
}

.form-select,
.form-input {
	width: 100%;
	padding: 0.75rem 1rem;
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 8px;
	color: #d4d5f1;
	font-size: 0.875rem;
	transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
	outline: none;
	border-color: #ff3bd4;
	box-shadow: 0 0 0 3px rgba(255, 59, 212, 0.1);
}

.search-input-container {
	position: relative;
}

.search-icon {
	position: absolute;
	right: 1rem;
	top: 50%;
	transform: translateY(-50%);
	color: #ff3bd4;
	width: 18px;
	height: 18px;
}

.btn {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.75rem 1.5rem;
	border: none;
	border-radius: 8px;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
}

.btn-primary {
	background: linear-gradient(135deg, #ff3bd4 0%, #e91e63 100%);
	color: white;
}

.btn-primary:hover {
	background: linear-gradient(135deg, #e91e63 0%, #ff3bd4 100%);
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(255, 59, 212, 0.3);
}

.full-width {
	width: 100%;
}

.search-results {
	margin-top: 2rem;
	padding-top: 2rem;
	border-top: 1px solid rgba(255, 59, 212, 0.2);
}

.results-title {
	font-size: 1.125rem;
	font-weight: 600;
	color: #d4d5f1;
	margin-bottom: 1rem;
}

.transaction-details {
	background: rgba(255, 255, 255, 0.02);
	border: 1px solid rgba(255, 59, 212, 0.1);
	border-radius: 12px;
	padding: 1.5rem;
}

.detail-grid {
	display: grid;
	gap: 1rem;
	grid-template-columns: 1fr;
}

@media (min-width: 768px) {
	.detail-grid {
		grid-template-columns: 1fr 1fr;
	}
}

.detail-item {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.detail-label {
	font-size: 0.75rem;
	font-weight: 500;
	color: #d4d5f1;
	opacity: 0.7;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.detail-value {
	font-size: 0.875rem;
	color: #d4d5f1;
}

.hash-container {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.hash-code {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 6px;
	padding: 0.5rem;
	font-family: "Courier New", monospace;
	font-size: 0.75rem;
	color: #ff3bd4;
	flex: 1;
	word-break: break-all;
}

.copy-btn {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 6px;
	padding: 0.5rem;
	color: #ff3bd4;
	cursor: pointer;
	transition: all 0.3s ease;
}

.copy-btn:hover {
	background: rgba(255, 59, 212, 0.2);
	border-color: #ff3bd4;
}

.copy-success {
	color: #10b981;
}

.status-badge {
	padding: 0.25rem 0.75rem;
	border-radius: 20px;
	font-size: 0.75rem;
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.status-badge.confirmed {
	background: rgba(16, 185, 129, 0.1);
	color: #10b981;
	border: 1px solid rgba(16, 185, 129, 0.2);
}

.address-code {
	background: rgba(255, 59, 212, 0.1);
	border: 1px solid rgba(255, 59, 212, 0.2);
	border-radius: 6px;
	padding: 0.5rem;
	font-family: "Courier New", monospace;
	font-size: 0.75rem;
	color: #ff3bd4;
	word-break: break-all;
}

.amount-value {
	font-size: 1rem;
	font-weight: 600;
	color: #d4d5f1;
}

.fee-value {
	font-size: 0.875rem;
	color: #d4d5f1;
	opacity: 0.8;
}

.block-height {
	font-size: 0.875rem;
	color: #d4d5f1;
	font-weight: 500;
}

.confirmations {
	font-size: 0.875rem;
	color: #10b981;
	font-weight: 600;
}

.stats-list {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.stat-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.75rem;
	background: rgba(255, 255, 255, 0.02);
	border: 1px solid rgba(255, 59, 212, 0.1);
	border-radius: 8px;
}

.stat-label {
	font-size: 0.875rem;
	color: #d4d5f1;
	opacity: 0.8;
}

.stat-value {
	font-size: 0.875rem;
	font-weight: 600;
	color: #d4d5f1;
}

.links-list {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.quick-link {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	padding: 0.75rem;
	background: rgba(255, 255, 255, 0.02);
	border: 1px solid rgba(255, 59, 212, 0.1);
	border-radius: 8px;
	color: #d4d5f1;
	text-decoration: none;
	transition: all 0.3s ease;
}

.quick-link:hover {
	background: rgba(255, 59, 212, 0.1);
	border-color: #ff3bd4;
	transform: translateX(4px);
}

.quick-link svg {
	color: #ff3bd4;
	width: 18px;
	height: 18px;
}

.table-container {
	overflow-x: auto;
}

.blocks-table {
	width: 100%;
	border-collapse: collapse;
}

.table-header {
	border-bottom: 1px solid rgba(255, 59, 212, 0.2);
}

.table-th {
	padding: 0.75rem;
	text-align: left;
	font-size: 0.75rem;
	font-weight: 600;
	color: #d4d5f1;
	opacity: 0.7;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.table-body tr {
	border-bottom: 1px solid rgba(255, 59, 212, 0.1);
}

.table-body tr:hover {
	background: rgba(255, 59, 212, 0.05);
}

.table-td {
	padding: 0.75rem;
	font-size: 0.875rem;
	color: #d4d5f1;
}

.block-link {
	color: #ff3bd4;
	text-decoration: none;
	font-weight: 500;
	transition: all 0.3s ease;
}

.block-link:hover {
	color: #e91e63;
	text-decoration: underline;
}

.hash-short {
	font-family: "Courier New", monospace;
	font-size: 0.75rem;
	color: #d4d5f1;
	opacity: 0.8;
}

.time-cell {
	font-size: 0.75rem;
	color: #d4d5f1;
	opacity: 0.7;
}

.transactions-list {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.transaction-card {
	background: rgba(255, 255, 255, 0.02);
	border: 1px solid rgba(255, 59, 212, 0.1);
	border-radius: 12px;
	padding: 1rem;
	transition: all 0.3s ease;
}

.transaction-card:hover {
	background: rgba(255, 59, 212, 0.05);
	border-color: rgba(255, 59, 212, 0.2);
}

.transaction-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.75rem;
}

.transaction-link {
	color: #ff3bd4;
	text-decoration: none;
	font-weight: 500;
	transition: all 0.3s ease;
}

.transaction-link:hover {
	color: #e91e63;
	text-decoration: underline;
}

.transaction-hash {
	font-family: "Courier New", monospace;
	font-size: 0.75rem;
	color: #ff3bd4;
}

.transaction-time {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-size: 0.75rem;
	color: #d4d5f1;
	opacity: 0.7;
}

.transaction-time svg {
	width: 14px;
	height: 14px;
}

.transaction-details-grid {
	display: grid;
	gap: 0.5rem;
	grid-template-columns: 1fr;
}

@media (min-width: 480px) {
	.transaction-details-grid {
		grid-template-columns: 1fr 1fr;
	}
}

.transaction-detail {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.detail-label-small {
	font-size: 0.75rem;
	color: #d4d5f1;
	opacity: 0.7;
}

.address-small {
	font-family: "Courier New", monospace;
	font-size: 0.75rem;
	color: #d4d5f1;
}

.amount-small {
	font-size: 0.75rem;
	color: #d4d5f1;
	font-weight: 500;
}

.address-link {
	color: #ff3bd4;
	text-decoration: none;
	transition: all 0.3s ease;
}

.address-link:hover {
	color: #e91e63;
	text-decoration: underline;
}

.right-column {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}
