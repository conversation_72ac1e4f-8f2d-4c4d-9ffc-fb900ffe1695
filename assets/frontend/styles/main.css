/*-----------------------------------------------------------------------------------

    Project Name: Innomax - Technology & IT Startup HTML Template
    Author: XpressBuddy -->> (https://themeforest.net/user/xpressbuddy)
    Support: <EMAIL>
    Description: Innomax - Technology & IT Startup HTML Template
    Developer: <PERSON> Mia -> <EMAIL>
    Version: 1.0

-----------------------------------------------------------------------------------

    CSS INDEX
    ==================
    
	01. Theme default CSS
    02. header
    03. global
    04. hero
    05. feature
    06. about
    07. service
    08. team
    09. testimonial
    10. contact
    11. blog
    12. bramd
    13. pricing
    14. project
    15. cta
    16. fanfact
    17. marquee
    18. process
    19. banner
    20. portfolio
    21. accordion
    22. mission
    23. career
    24. shop
    25. breadcrumb
    26. sidebar
    27. backtotop
    28. sec-title
    29. search
    30. mobile-menu
    31. 404
    32. preloader
    

-----------------------------------------------------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Red+Hat+Display:ital,wght@0,300..900;1,300..900&family=Righteous&display=swap");

:root {
  --font-body: "Inter", sans-serif;
  --font-heading: 'Aeonik';
  --font-heading-two: 'TransducerTest';
  --font-heading-three: 'GT-Super-Text';
  --font-heading-four: 'Agrandir';
  --font-heading-five: "Red Hat Display", serif;
  --color-primary: #d44a00;
  --color-primary-rgb: 212, 74, 0;
  --color-primary-two: #0f55dc;
  --color-primary-two-rgb: 15, 83, 220;
  --color-primary-three: #9a4497;
  --color-primary-three-rgb: 154, 68, 151;
  --color-primary-four: #ff6a00;
  --color-primary-four-rgb: 255, 106, 0;
  --color-primary-five: #1438bc;
  --color-primary-five-rgb: 20, 56, 188;
  --color-heading: #111112;
  --color-heading-two: #0c111d;
  --color-heading-three: #16140c;
  --color-heading-four: #212877;
  --color-default: #49515b;
  --color-default-two: #494d57;
  --color-yellow: #ffd600;
  --color-white: #fff;
  --color-black: #000;
  --color-body: #eaeef0;
  --easing: cubic-bezier(0.67, 0.04, 0.3, 0.91);
}

/* reset css start */
:root {
  scroll-behavior: auto;
}

html {
  scroll-behavior: smooth;
}

.App {
  position: relative;
}

body {
  padding: 0;
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  position: relative;
  color: var(--color-default);
  font-family: var(--font-body);
  background-color: var(--color-body);
  scrollbar-width: thin;
}

body::-webkit-scrollbar {
  width: 7px;
}

body::-webkit-scrollbar-track {
  border-radius: 10px;
}

body::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 10px;
}

img {
  max-width: 100%;
  height: auto;
}

ul {
  margin: 0px;
  padding: 0px;
}

button {
  cursor: pointer;
}

*:focus {
  outline: none;
}

button {
  border: none;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

button:focus {
  outline: none;
}

a {
  text-decoration: none;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

a:hover {
  color: inherit;
}

select {
  height: 55px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  /* background-image: url(../images/icon/select-arrow.png); */
  background-position: calc(100% - 10px) 50%;
  background-repeat: no-repeat;
  padding-right: 20px;
  background-color: transparent;
  border: 0;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
form select,
textarea {
  width: 100%;
  height: 75px;
  border-radius: 0;
  background-color: #F3F4F5;
  padding: 10px 20px;
  border: 0;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  color: var(--color-black);
  font-weight: 500;
}

input[type="text"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
form select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #888686;
  opacity: 1;
}

input[type="text"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="email"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
form select::-moz-placeholder,
textarea::-moz-placeholder {
  color: #888686;
  opacity: 1;
}

input[type="text"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
form select:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #888686;
  opacity: 1;
}

input[type="text"]:-moz-placeholder,
input[type="password"]:-moz-placeholder,
input[type="email"]:-moz-placeholder,
input[type="tel"]:-moz-placeholder,
form select:-moz-placeholder,
textarea:-moz-placeholder {
  color: #888686;
  opacity: 1;
}

textarea {
  height: 100px;
}

button {
  border: 0;
}

table {
  width: 100%;
}

p,
li,
span {
  margin-bottom: 0;
}

/* reset css end */
/* global css start */
.nice-select {
  background-color: transparent;
  height: 60px;
  line-height: 59px;
  min-height: 60px;
  padding: 0 20px;
  width: 100%;
  padding: 0 46px;
  margin-bottom: 20px;
  background-color: #F3F4F5;
  border: 0;
  font-size: 16px;
}

.nice-select::after {
  border-bottom: 1.9px solid #4C6A6D;
  border-right: 1.9px solid #4C6A6D;
  height: 6px;
  right: 20px;
  width: 6px;
}

.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus {
  background-color: #F3F4F5;
}

.nice-select .list {
  width: 100%;
}

.nice-select .list li {
  margin-right: 0 !important;
}

.nice-select .list .option {
  color: var(--color-dark);
  font-weight: 500;
}

.nice-select .list .option.selected {
  font-weight: 600;
}

.nice-select .list .option.selected,
.nice-select .list .option:hover {
  border: none !important;
}

/* global css end */
.body_wrap {
  position: relative;
  overflow: clip;
}

.bg_img {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
}

.footer-bg {
  background-color: #04060A;
}

.black-bg {
  background: var(--color-black);
}

.gray-bg {
  background-color: var(--color-gray);
}

.gray-bg-2 {
  background-color: var(--color-gray-2);
}

.dark-bg {
  background-color: var(--color-dark);
}

.white {
  color: var(--color-white);
}

.pos-rel {
  position: relative;
}

.pos-absolute {
  position: absolute;
}

.f-right {
  float: right;
}

.radius-20 {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.border-effect a,
.border-effect-2 a {
  display: inline !important;
  width: 100%;
  background-repeat: no-repeat;
  background-position-y: -2px;
  background-image: linear-gradient(transparent calc(100% - 2px), currentColor 1px);
  -webkit-transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  -o-transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.border-effect a:hover,
.border-effect-2 a:hover {
  background-size: 100% 100%;
  color: inherit;
}

.border-effect-2 a {
  background-image: linear-gradient(transparent calc(100% - 1px), currentColor 1px);
}

.btn-video {
  width: 73px;
  height: 73px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: #1140C0;
  position: relative;
  background-color: var(--color-white);
}

@media (max-width: 767px) {
  .btn-video {
    width: 60px;
    height: 60px;
    font-size: 18px;
  }
}

.btn-video:hover {
  color: var(--color-primary);
}

.btn-video::before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 0;
  top: 0;
  display: block;
  width: 100%;
  height: 100%;
  background-color: var(--color-white);
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
  -webkit-animation: pulse-border 1500ms ease-out infinite;
  z-index: -2;
}

.btn-video-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.border_effect a {
  display: inline !important;
  width: 100%;
  background-repeat: no-repeat;
  background-position-y: -2px;
  background-image: linear-gradient(transparent calc(100% - 2px), currentColor 1px);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.border_effect a:hover {
  background-size: 100% 100%;
  color: inherit;
}

@media (max-width: 991px) {
  .tx-col-md-6 {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .tx-col-md-6 {
    width: 100%;
  }
}

.xb-close {
  background: rgba(0, 0, 0, 0.04);
  border: 9px solid transparent;
  color: #777;
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.xb-close::before,
.xb-close::after {
  content: '';
  position: absolute;
  height: 2px;
  width: 100%;
  top: 50%;
  left: 0;
  margin-top: -1px;
  transform-origin: 50% 50%;
  -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #1b1b1b;
}

.xb-close::before {
  -webkit-transform: rotate(45deg);
  -khtml-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.xb-close::after {
  -webkit-transform: rotate(-45deg);
  -khtml-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.xb-close:hover::before,
.xb-close:hover::after {
  -webkit-transform: rotate(0);
  -khtml-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}

/* order & unorder list reset - start */
.ul_li,
.ul_li_right,
.ul_li_center,
.ul_li_between {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.ul_li>li,
.ul_li_right>li,
.ul_li_center>li,
.ul_li_between>li {
  float: left;
  list-style: none;
  display: inline-block;
}

.ul_li {
  justify-content: flex-start;
}

.ul_li_center {
  justify-content: center;
}

.ul_li_right {
  justify-content: flex-end;
}

.ul_li_between {
  justify-content: space-between;
}

.ul_li_block {
  margin: 0px;
  padding: 0px;
  display: block;
}

.ul_li_block>li {
  display: block;
  list-style: none;
}

.flex-1 {
  flex: 1;
}

.color-black {
  color: var(--color-black);
}

.pagination_wrap ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: -5px;
  list-style: none;
}

.pagination_wrap ul li {
  padding: 5px;
}

.pagination_wrap ul li a {
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-primary);
  border: 1px solid #EEE5E5;
  background-color: var(--color-white);
  -webkit-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  z-index: 1;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  overflow: hidden;
}

@media (max-width: 767px) {
  .pagination_wrap ul li a {
    height: 40px;
    width: 40px;
    font-size: 15px;
  }
}

.pagination_wrap ul li a.current_page,
.pagination_wrap ul li a:hover {
  background-color: var(--color-yellow);
  border-color: var(--color-yellow);
}

.mr-none-60 {
  margin-right: -60px;
}

.ml-none-60 {
  margin-left: -60px;
}

.pb-8 {
  padding-bottom: 8px;
}

.bg-leniar {
  background: linear-gradient(180deg, #f7f5f2 15.68%, #fff 100%);
}

.xb-odm {
  overflow: hidden;
}

.xb-odm .xbo.xbo-auto-theme .xbo-digit .xbo-digit-inner,
.xb-odm .xbo.xbo-theme-default .xbo-digit .xbo-digit-inner {
  overflow: initial;
  overscroll-behavior-y: hidden;
}

/*--
    - Margin & Padding
-----------------------------------------*/
/*-- Margin Top --*/
.mt-none-5 {
  margin-top: -5px;
}

.mt-none-10 {
  margin-top: -10px;
}

.mt-none-15 {
  margin-top: -15px;
}

.mt-none-20 {
  margin-top: -20px;
}

.mt-none-25 {
  margin-top: -25px;
}

.mt-none-30 {
  margin-top: -30px;
}

.mt-none-35 {
  margin-top: -35px;
}

.mt-none-40 {
  margin-top: -40px;
}

.mt-none-45 {
  margin-top: -45px;
}

.mt-none-50 {
  margin-top: -50px;
}

.mt-none-55 {
  margin-top: -55px;
}

.mt-none-60 {
  margin-top: -60px;
}

.mt-none-65 {
  margin-top: -65px;
}

.mt-none-70 {
  margin-top: -70px;
}

.mt-none-75 {
  margin-top: -75px;
}

.mt-none-80 {
  margin-top: -80px;
}

.mt-none-85 {
  margin-top: -85px;
}

.mt-none-90 {
  margin-top: -90px;
}

.mt-none-95 {
  margin-top: -95px;
}

.mt-none-100 {
  margin-top: -100px;
}

/*-- Margin Top --*/
.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-55 {
  margin-top: 55px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-65 {
  margin-top: 65px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-75 {
  margin-top: 75px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-85 {
  margin-top: 85px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-95 {
  margin-top: 95px;
}

.mt-100 {
  margin-top: 100px;
}

.mt-105 {
  margin-top: 105px;
}

.mt-110 {
  margin-top: 110px;
}

.mt-115 {
  margin-top: 115px;
}

.mt-120 {
  margin-top: 120px;
}

.mt-125 {
  margin-top: 125px;
}

.mt-130 {
  margin-top: 130px;
}

.mt-135 {
  margin-top: 135px;
}

.mt-140 {
  margin-top: 140px;
}

.mt-145 {
  margin-top: 145px;
}

.mt-150 {
  margin-top: 150px;
}

.mt-155 {
  margin-top: 155px;
}

.mt-160 {
  margin-top: 160px;
}

.mt-165 {
  margin-top: 165px;
}

.mt-170 {
  margin-top: 170px;
}

.mt-175 {
  margin-top: 175px;
}

.mt-180 {
  margin-top: 180px;
}

.mt-185 {
  margin-top: 185px;
}

.mt-190 {
  margin-top: 190px;
}

.mt-195 {
  margin-top: 195px;
}

.mt-200 {
  margin-top: 200px;
}

/*-- Margin Bottom --*/
.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-55 {
  margin-bottom: 55px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-65 {
  margin-bottom: 65px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-75 {
  margin-bottom: 75px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-85 {
  margin-bottom: 85px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-95 {
  margin-bottom: 95px;
}

.mb-100 {
  margin-bottom: 100px;
}

.mb-105 {
  margin-bottom: 105px;
}

.mb-110 {
  margin-bottom: 110px;
}

.mb-115 {
  margin-bottom: 115px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb-125 {
  margin-bottom: 125px;
}

.mb-130 {
  margin-bottom: 130px;
}

.mb-135 {
  margin-bottom: 135px;
}

.mb-140 {
  margin-bottom: 140px;
}

.mb-145 {
  margin-bottom: 145px;
}

.mb-150 {
  margin-bottom: 150px;
}

.mb-155 {
  margin-bottom: 155px;
}

.mb-160 {
  margin-bottom: 160px;
}

.mb-165 {
  margin-bottom: 165px;
}

.mb-170 {
  margin-bottom: 170px;
}

.mb-175 {
  margin-bottom: 175px;
}

.mb-180 {
  margin-bottom: 180px;
}

.mb-185 {
  margin-bottom: 185px;
}

.mb-190 {
  margin-bottom: 190px;
}

.mb-195 {
  margin-bottom: 195px;
}

.mb-200 {
  margin-bottom: 200px;
}

/*-- Margin Left --*/
.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-35 {
  margin-left: 35px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-45 {
  margin-left: 45px;
}

.ml-50 {
  margin-left: 50px;
}

.ml-55 {
  margin-left: 55px;
}

.ml-60 {
  margin-left: 60px;
}

.ml-65 {
  margin-left: 65px;
}

.ml-70 {
  margin-left: 70px;
}

.ml-75 {
  margin-left: 75px;
}

.ml-80 {
  margin-left: 80px;
}

.ml-85 {
  margin-left: 85px;
}

.ml-90 {
  margin-left: 90px;
}

.ml-95 {
  margin-left: 95px;
}

.ml-100 {
  margin-left: 100px;
}

.ml-105 {
  margin-left: 105px;
}

.ml-110 {
  margin-left: 110px;
}

.ml-115 {
  margin-left: 115px;
}

.ml-120 {
  margin-left: 120px;
}

.ml-125 {
  margin-left: 125px;
}

.ml-130 {
  margin-left: 130px;
}

.ml-135 {
  margin-left: 135px;
}

.ml-140 {
  margin-left: 140px;
}

.ml-145 {
  margin-left: 145px;
}

.ml-150 {
  margin-left: 150px;
}

.ml-155 {
  margin-left: 155px;
}

.ml-160 {
  margin-left: 160px;
}

.ml-165 {
  margin-left: 165px;
}

.ml-170 {
  margin-left: 170px;
}

.ml-175 {
  margin-left: 175px;
}

.ml-180 {
  margin-left: 180px;
}

.ml-185 {
  margin-left: 185px;
}

.ml-190 {
  margin-left: 190px;
}

.ml-195 {
  margin-left: 195px;
}

.ml-200 {
  margin-left: 200px;
}

/*-- Margin Right --*/
.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-35 {
  margin-right: 35px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-45 {
  margin-right: 45px;
}

.mr-50 {
  margin-right: 50px;
}

.mr-55 {
  margin-right: 55px;
}

.mr-60 {
  margin-right: 60px;
}

.mr-65 {
  margin-right: 65px;
}

.mr-70 {
  margin-right: 70px;
}

.mr-75 {
  margin-right: 75px;
}

.mr-80 {
  margin-right: 80px;
}

.mr-85 {
  margin-right: 85px;
}

.mr-90 {
  margin-right: 90px;
}

.mr-95 {
  margin-right: 95px;
}

.mr-100 {
  margin-right: 100px;
}

.mr-105 {
  margin-right: 105px;
}

.mr-110 {
  margin-right: 110px;
}

.mr-115 {
  margin-right: 115px;
}

.mr-120 {
  margin-right: 120px;
}

.mr-125 {
  margin-right: 125px;
}

.mr-130 {
  margin-right: 130px;
}

.mr-135 {
  margin-right: 135px;
}

.mr-140 {
  margin-right: 140px;
}

.mr-145 {
  margin-right: 145px;
}

.mr-150 {
  margin-right: 150px;
}

.mr-155 {
  margin-right: 155px;
}

.mr-160 {
  margin-right: 160px;
}

.mr-165 {
  margin-right: 165px;
}

.mr-170 {
  margin-right: 170px;
}

.mr-175 {
  margin-right: 175px;
}

.mr-180 {
  margin-right: 180px;
}

.mr-185 {
  margin-right: 185px;
}

.mr-190 {
  margin-right: 190px;
}

.mr-195 {
  margin-right: 195px;
}

.mr-200 {
  margin-right: 200px;
}

/*-- Padding Top --*/
.pt-5 {
  padding-top: 5px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-55 {
  padding-top: 55px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-65 {
  padding-top: 65px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-75 {
  padding-top: 75px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-85 {
  padding-top: 85px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-95 {
  padding-top: 95px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-105 {
  padding-top: 105px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-115 {
  padding-top: 115px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-125 {
  padding-top: 125px;
}

.pt-130 {
  padding-top: 130px;
}

.pt-135 {
  padding-top: 135px;
}

.pt-140 {
  padding-top: 140px;
}

.pt-145 {
  padding-top: 145px;
}

.pt-150 {
  padding-top: 150px;
}

.pt-155 {
  padding-top: 155px;
}

.pt-160 {
  padding-top: 160px;
}

.pt-165 {
  padding-top: 165px;
}

.pt-170 {
  padding-top: 170px;
}

.pt-175 {
  padding-top: 175px;
}

.pt-180 {
  padding-top: 180px;
}

.pt-185 {
  padding-top: 185px;
}

.pt-190 {
  padding-top: 190px;
}

.pt-195 {
  padding-top: 195px;
}

.pt-200 {
  padding-top: 200px;
}

/*-- Padding Bottom --*/
.pb-5 {
  padding-bottom: 5px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pb-200 {
  padding-bottom: 200px;
}

/*-- Padding Left --*/
.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-25 {
  padding-left: 25px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-35 {
  padding-left: 35px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-45 {
  padding-left: 45px;
}

.pl-50 {
  padding-left: 50px;
}

.pl-55 {
  padding-left: 55px;
}

.pl-60 {
  padding-left: 60px;
}

.pl-65 {
  padding-left: 65px;
}

.pl-70 {
  padding-left: 70px;
}

.pl-75 {
  padding-left: 75px;
}

.pl-80 {
  padding-left: 80px;
}

.pl-85 {
  padding-left: 85px;
}

.pl-90 {
  padding-left: 90px;
}

.pl-95 {
  padding-left: 95px;
}

.pl-100 {
  padding-left: 100px;
}

.pl-105 {
  padding-left: 105px;
}

.pl-110 {
  padding-left: 110px;
}

.pl-115 {
  padding-left: 115px;
}

.pl-120 {
  padding-left: 120px;
}

.pl-125 {
  padding-left: 125px;
}

.pl-130 {
  padding-left: 130px;
}

.pl-135 {
  padding-left: 135px;
}

.pl-140 {
  padding-left: 140px;
}

.pl-145 {
  padding-left: 145px;
}

.pl-150 {
  padding-left: 150px;
}

.pl-155 {
  padding-left: 155px;
}

.pl-160 {
  padding-left: 160px;
}

.pl-165 {
  padding-left: 165px;
}

.pl-170 {
  padding-left: 170px;
}

.pl-175 {
  padding-left: 175px;
}

.pl-180 {
  padding-left: 180px;
}

.pl-185 {
  padding-left: 185px;
}

.pl-190 {
  padding-left: 190px;
}

.pl-195 {
  padding-left: 195px;
}

.pl-200 {
  padding-left: 200px;
}

/*-- Padding Right --*/
.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-25 {
  padding-right: 25px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-35 {
  padding-right: 35px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-45 {
  padding-right: 45px;
}

.pr-50 {
  padding-right: 50px;
}

.pr-55 {
  padding-right: 55px;
}

.pr-60 {
  padding-right: 60px;
}

.pr-65 {
  padding-right: 65px;
}

.pr-70 {
  padding-right: 70px;
}

.pr-75 {
  padding-right: 75px;
}

.pr-80 {
  padding-right: 80px;
}

.pr-85 {
  padding-right: 85px;
}

.pr-90 {
  padding-right: 90px;
}

.pr-95 {
  padding-right: 95px;
}

.pr-100 {
  padding-right: 100px;
}

.pr-105 {
  padding-right: 105px;
}

.pr-110 {
  padding-right: 110px;
}

.pr-115 {
  padding-right: 115px;
}

.pr-120 {
  padding-right: 120px;
}

.pr-125 {
  padding-right: 125px;
}

.pr-130 {
  padding-right: 130px;
}

.pr-135 {
  padding-right: 135px;
}

.pr-140 {
  padding-right: 140px;
}

.pr-145 {
  padding-right: 145px;
}

.pr-150 {
  padding-right: 150px;
}

.pr-155 {
  padding-right: 155px;
}

.pr-160 {
  padding-right: 160px;
}

.pr-165 {
  padding-right: 165px;
}

.pr-170 {
  padding-right: 170px;
}

.pr-175 {
  padding-right: 175px;
}

.pr-180 {
  padding-right: 180px;
}

.pr-185 {
  padding-right: 185px;
}

.pr-190 {
  padding-right: 190px;
}

.pr-195 {
  padding-right: 195px;
}

.pr-200 {
  padding-right: 200px;
}

/* typography css start */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  line-height: -0.02em;
  font-weight: 500;
  line-height: 1.2;
  color: var(--color-heading);
  font-family: var(--font-heading);
}

h2 {
  font-size: 32px;
}

h3 {
  font-size: 22px;
}

h4 {
  font-size: 20px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

/* typography css end */
@-webkit-keyframes jump {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  40% {
    -webkit-transform: translate3d(0, 50%, 0);
    transform: translate3d(0, 50%, 0);
  }

  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes jump {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  40% {
    -webkit-transform: translate3d(0, 50%, 0);
    transform: translate3d(0, 50%, 0);
  }

  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes rotated {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotated {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotatedHalf {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  50% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
}

@keyframes rotatedHalf {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  50% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
}

@-webkit-keyframes rotatedHalfTwo {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@keyframes rotatedHalfTwo {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@-webkit-keyframes scale-upOne {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  100% {
    -webkit-transform: scale(0.2);
    transform: scale(0.2);
  }
}

@keyframes scale-upOne {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  100% {
    -webkit-transform: scale(0.2);
    transform: scale(0.2);
  }
}

.scale-upOne {
  animation: scale-upOne 5s linear infinite;
}

@-webkit-keyframes scale-right {
  0% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }

  50% {
    -webkit-transform: translateX(50%);
    transform: translateX(50%);
  }

  100% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

@keyframes scale-right {
  0% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }

  50% {
    -webkit-transform: translateX(50%);
    transform: translateX(50%);
  }

  100% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

@-webkit-keyframes fade-in {
  0% {
    opacity: 0.7;
  }

  40% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0.7;
  }

  40% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

@keyframes hvr-ripple-out {
  0% {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  100% {
    top: -6px;
    right: -6px;
    bottom: -6px;
    left: -6px;
  }
}

@keyframes hvr-ripple-out-two {
  0% {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  100% {
    top: -18px;
    right: -18px;
    bottom: -18px;
    left: -18px;
    opacity: 0;
  }
}

@-webkit-keyframes scale-up-one {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  40% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes scale-up-one {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  40% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@-webkit-keyframes scale-up-two {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  40% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
  }

  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
}

@keyframes scale-up-two {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  40% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
  }

  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }
}

@-webkit-keyframes scale-up-three {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }

  40% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
  }

  100% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }
}

@keyframes scale-up-three {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }

  40% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
  }

  100% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }
}

@keyframes animationFramesOne {
  0% {
    transform: translate(0px, 0px) rotate(0deg);
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    -moz-transform: translate(0px, 0px) rotate(0deg);
    -ms-transform: translate(0px, 0px) rotate(0deg);
    -o-transform: translate(0px, 0px) rotate(0deg);
  }

  20% {
    transform: translate(73px, -1px) rotate(36deg);
    -webkit-transform: translate(73px, -1px) rotate(36deg);
    -moz-transform: translate(73px, -1px) rotate(36deg);
    -ms-transform: translate(73px, -1px) rotate(36deg);
    -o-transform: translate(73px, -1px) rotate(36deg);
  }

  40% {
    transform: translate(141px, -20px) rotate(72deg);
    -webkit-transform: translate(141px, -20px) rotate(72deg);
    -moz-transform: translate(141px, -20px) rotate(72deg);
    -ms-transform: translate(141px, -20px) rotate(72deg);
    -o-transform: translate(141px, -20px) rotate(72deg);
  }

  60% {
    transform: translate(83px, -60px) rotate(108deg);
    -webkit-transform: translate(83px, -60px) rotate(108deg);
    -moz-transform: translate(83px, -60px) rotate(108deg);
    -ms-transform: translate(83px, -60px) rotate(108deg);
    -o-transform: translate(83px, -60px) rotate(108deg);
  }

  80% {
    transform: translate(-40px, 72px) rotate(144deg);
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
    -moz-transform: translate(-40px, 72px) rotate(144deg);
    -ms-transform: translate(-40px, 72px) rotate(144deg);
    -o-transform: translate(-40px, 72px) rotate(144deg);
  }

  100% {
    transform: translate(0px, 0px) rotate(0deg);
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    -moz-transform: translate(0px, 0px) rotate(0deg);
    -ms-transform: translate(0px, 0px) rotate(0deg);
    -o-transform: translate(0px, 0px) rotate(0deg);
  }
}

@-webkit-keyframes animationFramesOne {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
  }

  20% {
    -webkit-transform: translate(73px, -1px) rotate(36deg);
  }

  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg);
  }

  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg);
  }

  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
  }

  100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
  }
}

@keyframes animationFramesTwo {
  0% {
    transform: translate(0px, 0px) rotate(0deg) scale(1);
  }

  20% {
    transform: translate(73px, -1px) rotate(36deg) scale(0.9);
  }

  40% {
    transform: translate(141px, 72px) rotate(72deg) scale(1);
  }

  60% {
    transform: translate(83px, 122px) rotate(108deg) scale(1.2);
  }

  80% {
    transform: translate(-40px, 72px) rotate(144deg) scale(1.1);
  }

  100% {
    transform: translate(0px, 0px) rotate(0deg) scale(1);
  }
}

@-webkit-keyframes animationFramesTwo {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg) scale(1);
  }

  20% {
    -webkit-transform: translate(73px, -1px) rotate(36deg) scale(0.9);
  }

  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg) scale(1);
  }

  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg) scale(1.2);
  }

  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg) scale(1.1);
  }

  100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg) scale(1);
  }
}

@keyframes animationFramesThree {
  0% {
    transform: translate(165px, -30px);
    -webkit-transform: translate(165px, -30px);
    -moz-transform: translate(165px, -30px);
    -ms-transform: translate(165px, -30px);
    -o-transform: translate(165px, -30px);
  }

  100% {
    transform: translate(-60px, 80px);
    -webkit-transform: translate(-60px, 80px);
    -moz-transform: translate(-60px, 80px);
    -ms-transform: translate(-60px, 80px);
    -o-transform: translate(-60px, 80px);
  }
}

@-webkit-keyframes animationFramesThree {
  0% {
    transform: translate(165px, -30px);
    -webkit-transform: translate(165px, -30px);
    -moz-transform: translate(165px, -30px);
    -ms-transform: translate(165px, -30px);
    -o-transform: translate(165px, -30px);
  }

  100% {
    transform: translate(-60px, 80px);
    -webkit-transform: translate(-60px, 80px);
    -moz-transform: translate(-60px, 80px);
    -ms-transform: translate(-60px, 80px);
    -o-transform: translate(-60px, 80px);
  }
}

@keyframes animationFramesFour {
  0% {
    transform: translate(0px, 60px) rotate(0deg);
    -webkit-transform: translate(0px, 60px) rotate(0deg);
    -moz-transform: translate(0px, 60px) rotate(0deg);
    -ms-transform: translate(0px, 60px) rotate(0deg);
    -o-transform: translate(0px, 60px) rotate(0deg);
  }

  100% {
    transform: translate(-100px, -100px) rotate(180deg);
    -webkit-transform: translate(-100px, -100px) rotate(180deg);
    -moz-transform: translate(-100px, -100px) rotate(180deg);
    -ms-transform: translate(-100px, -100px) rotate(180deg);
    -o-transform: translate(-100px, -100px) rotate(180deg);
  }
}

@-webkit-keyframes animationFramesFour {
  0% {
    transform: translate(0px, 60px) rotate(0deg);
    -webkit-transform: translate(0px, 60px) rotate(0deg);
    -moz-transform: translate(0px, 60px) rotate(0deg);
    -ms-transform: translate(0px, 60px) rotate(0deg);
    -o-transform: translate(0px, 60px) rotate(0deg);
  }

  100% {
    transform: translate(-100px, -100px) rotate(180deg);
    -webkit-transform: translate(-100px, -100px) rotate(180deg);
    -moz-transform: translate(-100px, -100px) rotate(180deg);
    -ms-transform: translate(-100px, -100px) rotate(180deg);
    -o-transform: translate(-100px, -100px) rotate(180deg);
  }
}

@keyframes animationFramesFive {
  0% {
    transform: translate(0, 0) rotate(0deg);
    -webkit-transform: translate(0, 0) rotate(0deg);
    -moz-transform: translate(0, 0) rotate(0deg);
    -ms-transform: translate(0, 0) rotate(0deg);
    -o-transform: translate(0, 0) rotate(0deg);
  }

  21% {
    transform: translate(4px, -20px) rotate(38deg);
    -webkit-transform: translate(4px, -20px) rotate(38deg);
    -moz-transform: translate(4px, -20px) rotate(38deg);
    -ms-transform: translate(4px, -20px) rotate(38deg);
    -o-transform: translate(4px, -20px) rotate(38deg);
  }

  41% {
    transform: translate(-50px, -60px) rotate(74deg);
    -webkit-transform: translate(-50px, -60px) rotate(74deg);
    -moz-transform: translate(-50px, -60px) rotate(74deg);
    -ms-transform: translate(-50px, -60px) rotate(74deg);
    -o-transform: translate(-50px, -60px) rotate(74deg);
  }

  60% {
    transform: translate(-20px, -30px) rotate(108deg);
    -webkit-transform: translate(-20px, -30px) rotate(108deg);
    -moz-transform: translate(-20px, -30px) rotate(108deg);
    -ms-transform: translate(-20px, -30px) rotate(108deg);
    -o-transform: translate(-20px, -30px) rotate(108deg);
  }

  80% {
    transform: translate(-195px, -49px) rotate(144deg);
    -webkit-transform: translate(-195px, -49px) rotate(144deg);
    -moz-transform: translate(-195px, -49px) rotate(144deg);
    -ms-transform: translate(-195px, -49px) rotate(144deg);
    -o-transform: translate(-195px, -49px) rotate(144deg);
  }

  100% {
    transform: translate(-1px, 0px) rotate(180deg);
    -webkit-transform: translate(-1px, 0px) rotate(180deg);
    -moz-transform: translate(-1px, 0px) rotate(180deg);
    -ms-transform: translate(-1px, 0px) rotate(180deg);
    -o-transform: translate(-1px, 0px) rotate(180deg);
  }
}

@-webkit-keyframes animationFramesFive {
  0% {
    transform: translate(0, 0) rotate(0deg);
    -webkit-transform: translate(0, 0) rotate(0deg);
    -moz-transform: translate(0, 0) rotate(0deg);
    -ms-transform: translate(0, 0) rotate(0deg);
    -o-transform: translate(0, 0) rotate(0deg);
  }

  21% {
    transform: translate(4px, -20px) rotate(38deg);
    -webkit-transform: translate(4px, -20px) rotate(38deg);
    -moz-transform: translate(4px, -20px) rotate(38deg);
    -ms-transform: translate(4px, -20px) rotate(38deg);
    -o-transform: translate(4px, -20px) rotate(38deg);
  }

  41% {
    transform: translate(-50px, -60px) rotate(74deg);
    -webkit-transform: translate(-50px, -60px) rotate(74deg);
    -moz-transform: translate(-50px, -60px) rotate(74deg);
    -ms-transform: translate(-50px, -60px) rotate(74deg);
    -o-transform: translate(-50px, -60px) rotate(74deg);
  }

  60% {
    transform: translate(-20px, -30px) rotate(108deg);
    -webkit-transform: translate(-20px, -30px) rotate(108deg);
    -moz-transform: translate(-20px, -30px) rotate(108deg);
    -ms-transform: translate(-20px, -30px) rotate(108deg);
    -o-transform: translate(-20px, -30px) rotate(108deg);
  }

  80% {
    transform: translate(-195px, -49px) rotate(144deg);
    -webkit-transform: translate(-195px, -49px) rotate(144deg);
    -moz-transform: translate(-195px, -49px) rotate(144deg);
    -ms-transform: translate(-195px, -49px) rotate(144deg);
    -o-transform: translate(-195px, -49px) rotate(144deg);
  }

  100% {
    transform: translate(-1px, 0px) rotate(180deg);
    -webkit-transform: translate(-1px, 0px) rotate(180deg);
    -moz-transform: translate(-1px, 0px) rotate(180deg);
    -ms-transform: translate(-1px, 0px) rotate(180deg);
    -o-transform: translate(-1px, 0px) rotate(180deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes zoominup {
  0% {
    transform: scale(0.7);
  }

  50% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.7);
  }
}

@-webkit-keyframes zoominup {
  0% {
    transform: scale(0.8);
  }

  50% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.8);
  }
}

.zoominup {
  animation: zoominup 5s linear infinite;
}

@-webkit-keyframes updown {
  0% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }

  50% {
    transform: translateY(-5px);
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px);
  }

  100% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }
}

@keyframes updown {
  0% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }

  50% {
    transform: translateY(-5px);
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px);
  }

  100% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }
}

.updown {
  animation: updown 3s linear infinite;
}

@-webkit-keyframes updown-2 {
  0% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }

  50% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }

  100% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }
}

@keyframes updown-2 {
  0% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }

  50% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }

  100% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }
}

.updown-2 {
  animation: updown-2 4s linear infinite;
}

@keyframes updown-3 {
  0% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }

  50% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }

  100% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }
}

@-webkit-keyframes updown-3 {
  0% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }

  50% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
  }

  100% {
    transform: translateY(-20px);
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
  }
}

.updown-3 {
  animation: updown-3 5s linear infinite;
}

@keyframes ltr {
  0% {
    width: 0;
  }

  15% {
    width: 95%;
  }

  85% {
    opacity: 1;
  }

  90% {
    width: 95%;
    opacity: 0;
  }

  to {
    width: 0;
    opacity: 0;
  }
}

/*circleAnimation*/
@-webkit-keyframes circleAnimation {

  0%,
  100% {
    border-radius: 42% 58% 70% 30% / 45% 45% 55% 55%;
    -webkit-transform: translate3d(0, 0, 0) rotateZ(0.01deg);
    transform: translate3d(0, 0, 0) rotateZ(0.01deg);
  }

  34% {
    border-radius: 70% 30% 46% 54% / 30% 29% 71% 70%;
    -webkit-transform: translate3d(0, 5px, 0) rotateZ(0.01deg);
    transform: translate3d(0, 5px, 0) rotateZ(0.01deg);
  }

  50% {
    -webkit-transform: translate3d(0, 0, 0) rotateZ(0.01deg);
    transform: translate3d(0, 0, 0) rotateZ(0.01deg);
  }

  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
    -webkit-transform: translate3d(0, -3px, 0) rotateZ(0.01deg);
    transform: translate3d(0, -3px, 0) rotateZ(0.01deg);
  }
}

@-webkit-keyframes icon-bounce {

  0%,
  100%,
  20%,
  50%,
  80% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  40% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }

  60% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
  }
}

@keyframes icon-bounce {

  0%,
  100%,
  20%,
  50%,
  80% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  40% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }

  60% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
  }
}

@keyframes lr-animation {
  0% {
    -webkit-transform: translateX(40px);
    -ms-transform: translateX(40px);
    transform: translateX(40px);
  }

  100% {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes tb-animation {
  0% {
    -webkit-transform: translateY(30px);
    -ms-transform: translateY(30px);
    transform: translateY(30px);
  }

  100% {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes xb_up_down {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-20px);
  }
}

.slide-up-down {
  animation: xb_up_down 1s ease infinite alternate;
}

@keyframes xb_ltr {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-20px);
  }
}

.slide-ltr {
  animation: xb_ltr 5s ease infinite alternate;
}

@-webkit-keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes zoom {
  0% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }

  50% {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
  }

  100% {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes zoom {
  0% {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  50% {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
  }

  100% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@-webkit-keyframes shake {
  0% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg);
  }

  50% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg);
  }
}

@keyframes shake {
  0% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg);
  }

  50% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg);
  }
}

@-webkit-keyframes down {
  0% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px);
  }

  50% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px);
  }

  100% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px);
  }
}

@keyframes down {
  0% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px);
  }

  50% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px);
  }

  100% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px);
  }
}

@keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    -webkit-filter: alpha(opacity=50);
  }

  80% {
    transform: scale(1.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    -o-transform: scale(1.5);
  }

  100% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
  }
}

@-webkit-keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
  }

  80% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
  }

  100% {
    transform: scale(3.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(3.5);
    -moz-transform: scale(3.5);
    -ms-transform: scale(3.5);
    -o-transform: scale(3.5);
  }
}

@-moz-keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
  }

  80% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
  }

  100% {
    transform: scale(3.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(3.5);
    -moz-transform: scale(3.5);
    -ms-transform: scale(3.5);
    -o-transform: scale(3.5);
  }
}

@keyframes blink {

  from,
  to {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@-moz-keyframes blink {

  from,
  to {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@-webkit-keyframes blink {

  from,
  to {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@-ms-keyframes blink {

  from,
  to {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@-o-keyframes blink {

  from,
  to {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@keyframes pulse-border {
  0% {
    transform: scale(1);
    opacity: 0.67;
  }

  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@-webkit-keyframes pulse-border {
  0% {
    transform: scale(1);
    opacity: 0.67;
  }

  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@keyframes pulse-border-big {
  0% {
    transform: scale(1);
    opacity: 0.67;
  }

  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@-webkit-keyframes pulse-border-big {
  0% {
    transform: scale(1);
    opacity: 0.67;
  }

  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes push-scale-one {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.8);
  }
}

@-webkit-keyframes push-scale-one {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.8);
  }
}

@keyframes push-scale-two {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(2.4);
  }
}

@-webkit-keyframes push-scale-two {
  0% {
    transform: scale(1);
    opacity: 0.67;
  }

  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@-webkit-keyframes tada {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-5deg);
    transform: scale(0.9) rotate(-5deg);
  }

  50%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.1) rotate(5deg);
    transform: scale(1.1) rotate(5deg);
  }

  40%,
  60%,
  80% {
    -webkit-transform: scale(1.1) rotate(-5deg);
    transform: scale(1.1) rotate(-5deg);
  }

  100% {
    -webkit-transform: scale(1) rotate(0);
    transform: scale(1) rotate(0);
  }
}

@keyframes tada {
  0% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }

  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-5deg);
    -ms-transform: scale(0.9) rotate(-5deg);
    transform: scale(0.9) rotate(-5deg);
  }

  50%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.1) rotate(5deg);
    -ms-transform: scale(1.1) rotate(5deg);
    transform: scale(1.1) rotate(5deg);
  }

  40%,
  60%,
  80% {
    -webkit-transform: scale(1.1) rotate(-5deg);
    -ms-transform: scale(1.1) rotate(-5deg);
    transform: scale(1.1) rotate(-5deg);
  }

  100% {
    -webkit-transform: scale(1) rotate(0);
    -ms-transform: scale(1) rotate(0);
    transform: scale(1) rotate(0);
  }
}

.wow.skewIn.animated {
  -webkit-animation-name: xbSkewIn;
  animation-name: xbSkewIn;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-duration: .7s;
  animation-duration: .7s;
  -webkit-animation-timing-function: cubic-bezier(0.67, 0.04, 0.3, 0.91);
  animation-timing-function: cubic-bezier(0.67, 0.04, 0.3, 0.91);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

@-webkit-keyframes xbSkewIn {
  0% {
    -webkit-clip-path: inset(0 100% 0 0);
    clip-path: inset(0 100% 0 0);
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    -webkit-clip-path: inset(0 0 0 0);
    clip-path: inset(0 0 0 0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes xbSkewIn {
  0% {
    -webkit-clip-path: inset(0 100% 0 0);
    clip-path: inset(0 100% 0 0);
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    -webkit-clip-path: inset(0 0 0 0);
    clip-path: inset(0 0 0 0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

.skewInImg {
  clip-path: polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%);
  display: inline-block;
  position: relative;
  overflow: hidden;
  transition: 1.3s cubic-bezier(0.5, 0.5, 0, 1);
}

.skewInImg img {
  transform-origin: 50% 50%;
  transition: 1.3s cubic-bezier(0.5, 0.5, 0, 1);
  max-width: 100%;
  height: auto;
  transform: scale(1.5) translate(100px, 0px);
}

.skewInImg.animated {
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
}

.skewInImg.animated img {
  transform: scale(1) translate(0px, 0px);
}

[data-aos=fade-up] {
  transform: translateY(50px);
}

[data-aos=fade-down] {
  transform: translateY(-50px);
}

[data-aos=fade-right] {
  transform: translate(-50px);
}

[data-aos=fade-left] {
  transform: translate(50px);
}

[data-aos=fade-up-right] {
  transform: translate(-50px, 50px);
}

[data-aos=fade-up-left] {
  transform: translate(50px, 50px);
}

[data-aos=fade-down-right] {
  transform: translate(-50px, -50px);
}

[data-aos=fade-down-left] {
  transform: translate(50px, -50px);
}

[data-aos][data-aos][data-aos-easing=ease],
body[data-aos-easing=ease] [data-aos] {
  transition-timing-function: cubic-bezier(0.18, 0.57, 0.25, 0.97);
}

@-webkit-keyframes xb-danceTop {
  16% {
    -webkit-transform: skew(-14deg);
    transform: skew(-14deg);
  }

  33% {
    -webkit-transform: skew(12deg);
    transform: skew(12deg);
  }

  49% {
    -webkit-transform: skew(-8deg);
    transform: skew(-8deg);
  }

  66% {
    -webkit-transform: skew(6deg);
    transform: skew(6deg);
  }

  83% {
    -webkit-transform: skew(-4deg);
    transform: skew(-4deg);
  }
}

@keyframes xb-danceTop {
  16% {
    -webkit-transform: skew(-14deg);
    transform: skew(-14deg);
  }

  33% {
    -webkit-transform: skew(12deg);
    transform: skew(12deg);
  }

  49% {
    -webkit-transform: skew(-8deg);
    transform: skew(-8deg);
  }

  66% {
    -webkit-transform: skew(6deg);
    transform: skew(6deg);
  }

  83% {
    -webkit-transform: skew(-4deg);
    transform: skew(-4deg);
  }
}

@keyframes xbzoominzoomup {
  0% {
    transform: scale(0.8);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(0.8);
  }
}

.xbzoominzoomup {
  animation: xbzoominzoomup 5s linear infinite;
}

@keyframes rotateme {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }

  50% {
    transform: rotate(-180deg);
  }

  100% {
    transform: rotate(-360deg);
    opacity: 1;
  }
}

@keyframes rotateme2 {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
    opacity: 1;
  }
}

.rotateme {
  animation-name: rotateme;
  animation-duration: 12s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.rotateme2 {
  animation-name: rotateme2;
  animation-duration: 12s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.marquee-first {
  -webkit-animation: marquee 30s linear infinite;
  animation: marquee 30s linear infinite;
}

.marquee-2 {
  -webkit-animation: marquee2 30s linear infinite;
  animation: marquee2 30s linear infinite;
}

@-webkit-keyframes marquee {
  from {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }

  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes marquee {
  from {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }

  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@-webkit-keyframes marquee2 {
  from {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  to {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

@keyframes marquee2 {
  from {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  to {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

.path {
  stroke-dasharray: 6;
  stroke-dashoffset: 6;
  animation: dash 4s linear infinite;
}

@keyframes dash {
  0% {
    stroke-dashoffset: 100;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

.path_2 {
  stroke-dasharray: 6;
  stroke-dashoffset: 6;
  animation: dashTwo 4s linear infinite;
}

@keyframes dashTwo {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 100;
  }
}

@keyframes phoneRinging {
  from {
    transform: rotate3d(0, 0, 1, 0deg);
  }

  20%,
  32%,
  44%,
  56%,
  68% {
    transform: rotate3d(0, 0, 1, 0deg);
  }

  23%,
  35%,
  47%,
  59%,
  71% {
    transform: rotate3d(0, 0, 1, 15deg);
  }

  26%,
  38%,
  50%,
  62%,
  74% {
    transform: rotate3d(0, 0, 1, 0deg);
  }

  29%,
  41%,
  53%,
  65%,
  77% {
    transform: rotate3d(0, 0, 1, -15deg);
  }

  80% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

.phoneRinging {
  animation: phoneRinging 1.5s infinite linear;
}

@keyframes ring {
  0% {
    transform: rotate(0) scale(1) skew(1deg);
  }

  10% {
    transform: rotate(-15deg) scale(1) skew(1deg);
  }

  20% {
    transform: rotate(30deg) scale(1) skew(1deg);
  }

  30% {
    transform: rotate(-15deg) scale(1) skew(1deg);
  }

  40% {
    transform: rotate(30deg) scale(1) skew(1deg);
  }

  100%,
  50% {
    transform: rotate(0) scale(1) skew(1deg);
  }
}

/*--
    - Overlay
------------------------------------------*/
[data-overlay] {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

[data-overlay]::before {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  content: "";
  width: 100%;
  height: 100%;
}

/*-- Overlay Color --*/
[data-overlay="light"]::before {
  background-color: var(--color-white);
}

[data-overlay="dark"]::before {
  background-color: var(--color-black);
}

/*-- Overlay Opacity --*/
[data-opacity="1"]::before {
  opacity: 0.1;
}

[data-opacity="2"]::before {
  opacity: 0.2;
}

[data-opacity="3"]::before {
  opacity: 0.3;
}

[data-opacity="4"]::before {
  opacity: 0.4;
}

[data-opacity="5"]::before {
  opacity: 0.5;
}

[data-opacity="6"]::before {
  opacity: 0.6;
}

[data-opacity="7"]::before {
  opacity: 0.7;
}

[data-opacity="8"]::before {
  opacity: 0.8;
}

[data-opacity="9"]::before {
  opacity: 0.9;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .header-style-one .header__wrap {
    padding: 0 25px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .header-style-one .header__wrap {
    padding: 0 10px;
  }
}

@media (max-width: 1199px) {
  .header-style-one .header__wrap {
    padding: 10px 0;
  }
}

.header-style-one .main-menu__wrap {
  padding: 0 20px;
  border-radius: 40px;
  position: relative;
  border-radius: 40px;
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -ms-border-radius: 40px;
  -o-border-radius: 40px;
  box-shadow: 0 4px 24px -1px rgba(28, 9, 61, 0.2);
  background: linear-gradient(126deg, rgba(255, 255, 255, 0.2) 0%, rgba(54, 44, 44, 0.2) 100%);
}

@media (max-width: 1199px) {
  .header-style-one .main-menu__wrap {
    padding: 0 10px;
  }
}

@media (max-width: 991px) {
  .header-style-one .main-menu__wrap {
    display: none;
  }
}

.header-style-one .main-menu__wrap::before {
  content: "";
  position: absolute;
  z-index: -1;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: inherit;
  padding: 1px;
  -webkit-mask: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff)) content-box, -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: add, add;
  mask-composite: add, add;
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  background: linear-gradient(136deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 19.3%, rgba(255, 255, 255, 0.2) 69.47%, rgba(255, 255, 255, 0.8) 100%);
}

.header-style-one .main-menu__wrap::after {
  position: absolute;
  content: '';
  top: 0px;
  left: 0px;
  z-index: -1;
  height: 100%;
  width: 100%;
  border-radius: 40px;
  background-image: url(../images/bg/noise.png) !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  background-position: center center !important;
}

.header-style-one .stricked-menu .xb-nav-mobile {
  color: var(--color-black);
  background: none;
}

.header-style-one .stricked-menu .xb-nav-mobile:hover {
  color: var(--color-black);
}

.header-style-one .xb-header-mobile-search .search-field:focus {
  border-color: var(--color-primary);
}

.header-style-one .xb-header-mobile-search .search-submit {
  color: var(--color-primary);
}

.header-style-one .xb-menu-primary li>a:hover,
.header-style-one .xb-menu-primary li>a.current,
.header-style-one .xb-menu-primary li.current_page_item>a,
.header-style-one .xb-menu-primary li.current-menu-item>a,
.header-style-one .xb-menu-primary li.current_page_ancestor>a,
.header-style-one .xb-menu-primary li.current-menu-ancestor>a,
.header-style-one .xb-menu-primary .xb-menu-toggle:hover {
  color: var(--color-primary);
}

.side-menu a {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.side-menu a span {
  position: absolute;
  left: 47px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  text-transform: uppercase;
  color: var(--color-white);
  font-size: 14px;
  font-weight: 600;
  padding-left: 8px;
}

.side-menu a svg {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
}

.side-menu a svg path {
  fill: var(--color-primary);
}

.header-transparent {
  position: absolute;
  top: 35px;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 3;
}

@media (max-width: 1199px) {
  .header-transparent {
    top: 10px;
  }
}

@media (max-width: 991px) {
  .header-transparent {
    top: 0;
  }
}

.header-style-two.header-transparent {
  top: 0;
}

.header-style-two .main-menu__wrap {
  margin-left: 24px;
}

@media (max-width: 1199px) {
  .header-style-two .main-menu__wrap {
    margin-left: 10px;
  }
}

.header-style-two .header-top {
  background: #0f55dc;
  text-align: center;
  position: relative;
  left: 0;
  right: 0;
  z-index: 3;
  padding: 5px 0;
  margin-bottom: 22px;
}

.header-style-two .header-top span {
  font-weight: 500;
  font-size: 12px;
  color: var(--color-white);
}

.header-style-two .header-top span a {
  font-weight: 700;
  color: currentColor;
  margin-left: 40px;
  text-decoration: underline;
}

@media (max-width: 991px) {
  .header-style-two .header-top span a {
    margin-left: 10px;
  }
}

.header-style-two .header-top span i {
  font-weight: 700;
  margin-left: 5px;
  transform: translateY(0px);
}

.header-style-two .header-top .header-shape .shape {
  position: absolute;
  z-index: -1;
}

.header-style-two .header-top .header-shape .shape--one {
  left: 30px;
  top: 0;
}

.header-style-two .header-top .header-shape .shape--two {
  right: 30px;
  top: 0;
}

.header-style-two .main-menu ul li a {
  color: var(--color-heading-two);
}

.header-style-two .main-menu ul li {
  padding: 18px 0;
}

.header-style-two .stricked-menu .main-menu ul li {
  padding: 0 6px;
}

@media (max-width: 1199px) {
  .header-style-two .stricked-menu .header__wrap {
    padding: 12px 0;
  }
}

.header-style-three .xb-header {
  border-bottom: 1px solid #23263c;
  background: rgba(11, 18, 59, 0.33);
  backdrop-filter: blur(34.7999992371px);
}

.header-style-three.header-transparent {
  top: 0;
}

.header-style-three .main-menu__wrap .main-menu>ul>li>a {
  color: var(--color-white);
  background: transparent;
}

.header-style-three .main-menu__wrap .main-menu>ul>li>a::before {
  position: absolute;
  content: '';
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  color: var(--color-white);
  border: 1px solid #23263c;
  background: rgba(11, 18, 59, 0.33);
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  z-index: -1;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

.header-style-three .main-menu__wrap .main-menu>ul>li.menu-item-has-children>a span::after {
  content: "+";
  display: inline-block;
  padding-left: 6px;
}

.header-style-three .main-menu__wrap .main-menu>ul>li.active>a::before,
.header-style-three .main-menu__wrap .main-menu>ul>li:hover>a::before {
  opacity: 1;
}

@media (max-width: 1199px) {
  .header-style-three .header__wrap {
    padding: 10px 0;
  }
}

@media (max-width: 767px) {
  .header-style-three .header__wrap {
    padding: 15px 0;
  }
}

.header-style-three .main-menu>ul>li>.submenu {
  background: #1b1d2d;
}

.header-style-three .main-menu>ul>li>.submenu>li>a {
  color: var(--color-white);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.header-style-three .main-menu>ul>li>.submenu>li>a::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
  content: "";
  z-index: -1;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
}

.header-style-three .main-menu>ul>li>.submenu>li:hover>a,
.header-style-three .main-menu>ul>li>.submenu>li.active>a {
  color: #fff;
}

.header-style-three .main-menu>ul>li>.submenu>li:hover>a::before,
.header-style-three .main-menu>ul>li>.submenu>li.active>a::before {
  opacity: 1;
}

.header-style-three .main-menu ul li .submenu li:hover>a a,
.header-style-three .main-menu ul li .submenu li.active>a .header-contact a {
  padding: 18px 30px;
}

.header-style-three .main-menu>ul>li {
  padding: 25px 0;
}

.header-style-three h5,
.header-style-three p {
  color: var(--color-black) !important;
}

.header-style-three .xb-nav-mobile {
  color: var(--color-white);
}

.header-style-three .xb-nav-mobile:hover {
  color: var(--color-white);
}

.header-style-three .header-bar-mobile {
  margin-left: auto;
  margin-right: 30px;
}

@media (max-width: 767px) {
  .header-style-three .header-bar-mobile {
    margin-right: 0;
  }
}

.header-style-three .xb-header-mobile-search .search-field:focus {
  border-color: #ae6dfe;
}

.header-style-three .xb-header-mobile-search .search-submit {
  color: #ae6dfe;
}

.header-style-three .xb-menu-primary li>a:hover,
.header-style-three .xb-menu-primary li>a.current,
.header-style-three .xb-menu-primary li.current_page_item>a,
.header-style-three .xb-menu-primary li.current-menu-item>a,
.header-style-three .xb-menu-primary li.current_page_ancestor>a,
.header-style-three .xb-menu-primary li.current-menu-ancestor>a,
.header-style-three .xb-menu-primary .xb-menu-toggle:hover {
  color: #ae6dfe;
}

.header-style-three .mega_menu_wrapper .site_author p {
  color: var(--color-white) !important;
}

.header-style-four .header-top {
  top: 0;
  background: #d4f479;
  margin-bottom: 0;
}

.header-style-four .header-top span {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.01em;
  color: var(--color-heading-three);
}

.header-style-four.header-transparent {
  top: 0;
}

.header-style-four .xb-header {
  background: #fefaf2;
  border: 1px solid #e3dcd0;
  box-shadow: 0 0 9px 0 #f6f2ea;
  z-index: 999;
}

@media (max-width: 991px) {
  .header-style-four .xb-header {
    padding: 15px 0;
  }
}

.header-style-four .main-menu__wrap .main-menu>ul>li>a {
  border: 1px solid transparent;
  color: var(--color-heading-three);
  cursor: pointer;
}

.header-style-four .main-menu__wrap .main-menu>ul>li.active>a,
.header-style-four .main-menu__wrap .main-menu>ul>li:hover>a {
  border: 1px solid #e3dcd0;
}

.header-style-four .main-menu__wrap .main-menu>ul>li.active>a::before,
.header-style-four .main-menu__wrap .main-menu>ul>li:hover>a::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  background: #fff;
  border-radius: inherit;
}

.header-style-four .main-menu ul li {
  padding: 26px 0;
}

.header-style-four .stricked-menu {
  border: 0;
  box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
}

.header-style-four .stricked-menu .main-menu>ul>li {
  padding: 26px 0;
}

@media (max-width: 991px) {
  .header-style-four .stricked-menu .header__wrap {
    padding: 0;
  }
}

.header-style-four .xb-header-mobile-search .search-field:focus {
  border-color: var(--color-primary-three);
}

.header-style-four .xb-header-mobile-search .search-submit {
  color: var(--color-primary-three);
}

.header-style-four .xb-menu-primary li>a:hover,
.header-style-four .xb-menu-primary li>a.current,
.header-style-four .xb-menu-primary li.current_page_item>a,
.header-style-four .xb-menu-primary li.current-menu-item>a,
.header-style-four .xb-menu-primary li.current_page_ancestor>a,
.header-style-four .xb-menu-primary li.current-menu-ancestor>a,
.header-style-four .xb-menu-primary .xb-menu-toggle:hover {
  color: var(--color-primary-three);
}

.header-style-five .header-top {
  background: var(--color-heading-four);
}

.header-style-five .header-top span a {
  font-size: 12px;
  font-weight: 700;
  color: #ffd00e;
  margin-left: 25px;
}

.header-style-five .main-menu__wrap {
  margin-right: 15px;
}

.header-style-five .main-menu__wrap .main-menu>ul>li>a {
  font-weight: 700;
  color: var(--color-heading-four);
}

.header-style-five .header-contact a {
  padding: 16.5px 24px;
  color: var(--color-white);
  background: var(--color-primary-four);
}

.header-style-five .header-contact a::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 0;
  left: 0;
  top: 50%;
  z-index: -1;
  transform: translateY(-50%);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-heading-four);
}

.header-style-five .header-contact a:hover::before {
  height: 100%;
}

.header-style-five .main-menu ul li .submenu li:hover>a,
.header-style-five .main-menu ul li .submenu li.active>a {
  color: var(--color-primary-four);
  background-color: rgba(var(--color-primary-four-rgb), 0.1);
}

.header-style-five .megamenu_case h3,
.header-style-five .megamenu_case h4 {
  color: var(--color-white);
}

.header-style-five .megamenu_case h3 {
  font-family: var(--font-body);
}

.header-style-five .megamenu_case h4 {
  text-transform: capitalize;
  font-family: var(--font-heading);
}

.header-style-five .author_box_content h3 {
  font-family: var(--font-heading);
  text-transform: capitalize;
}

.header-style-six.header-transparent {
  top: 35px;
}

.header-style-six .main-menu__wrap {
  margin-left: 0;
  margin-right: 109px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .header-style-six .main-menu__wrap {
    margin-right: 30px;
  }
}

@media (max-width: 1199px) {
  .header-style-six .main-menu__wrap {
    margin-right: 10px;
  }
}

.header-style-six .main-menu__wrap .main-menu>ul>li:not(:last-child) {
  margin-right: 6px;
}

.header-style-six .main-menu__wrap .main-menu>ul>li>a {
  padding: 20px 19px;
  color: var(--color-white);
  font-family: var(--font-heading-five);
  background: none;
}

@media (max-width: 1199px) {
  .header-style-six .main-menu__wrap .main-menu>ul>li>a {
    padding: 20px 10px;
  }
}

.header-style-six .xb-nav-mobile {
  color: #fff;
}

.xb-nav-mobile {
  background: none;
}

.header-style-six .xb-nav-mobile:hover {
  color: var(--color-white);
}

.header-style-six .stricked-menu .thm-btn--header {
  color: #fff;
  background-color: #1438bc;
}

.header-style-six .stricked-menu .thm-btn--header:hover {
  color: #2042BF;
  background-color: #fff;
}

.header-style-six .stricked-menu .header-logo img:nth-child(1) {
  display: none;
}

.header-style-six .stricked-menu .header-logo img:nth-child(2) {
  display: block;
}

.header-style-six .stricked-menu .xb-nav-mobile {
  color: #1438bc;
}

.header-style-six .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
  padding: 32px 13px !important;
}

.header-style-six .thm-btn--header {
  color: #2042BF;
  padding: 15px 36px;
  font-family: var(--font-heading-five);
}

.header-style-six .thm-btn--header span {
  height: 26px;
  width: 26px;
  margin-left: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2042BF;
  color: #fff;
  border-radius: 50%;
}

.header-style-six .header-logo img:nth-child(2) {
  display: none;
}

.header-style-six .main-menu ul li .submenu li:hover>a,
.header-style-six .main-menu ul li .submenu li.active>a {
  color: var(--color-primary-five);
  background-color: rgba(var(--color-primary-five-rgb), 0.1);
}

.header-style-six .header-contact {
  margin-left: 0 !important;
}

.header-style-six .main-menu ul li {
  padding: 0;
}

.header-style-six .megamenu_case h3,
.header-style-six .megamenu_case h4 {
  color: var(--color-white) !important;
}

.header-style-six .xb-header-mobile-search .search-field:focus {
  border-color: #2042BF;
}

.header-style-six .xb-header-mobile-search .search-submit {
  color: #2042BF;
}

.header-style-six .xb-menu-primary li>a:hover,
.header-style-six .xb-menu-primary li>a.current,
.header-style-six .xb-menu-primary li.current_page_item>a,
.header-style-six .xb-menu-primary li.current-menu-item>a,
.header-style-six .xb-menu-primary li.current_page_ancestor>a,
.header-style-six .xb-menu-primary li.current-menu-ancestor>a,
.header-style-six .xb-menu-primary .xb-menu-toggle:hover {
  color: #2042BF;
}

.header-area .logo02 {
  display: none;
}

.stricked-menu .header-logo .logo01 {
  display: none;
}

.stricked-menu .header-logo .logo02 {
  display: block;
}

.stricked-menu {
  top: 0;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  position: fixed;
  transition: 0.5s;
  visibility: hidden;
  background-color: #fff;
  transform: translateY(-100%);
  box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
}

.stricked-menu.stricky-fixed {
  visibility: visible;
  transform: translateY(0%);
  -webkit-animation: smoothScroll 1s forwards;
  animation: smoothScroll 1s forwards;
  padding: 10px 0;
}
.header-style-six .stricked-menu.stricky-fixed {
  padding: 0px 0;
}
.header-style-three .stricked-menu.stricky-fixed {
  padding: 0px 0;
}
.header-style-four .stricked-menu.stricky-fixed {
  padding: 0px 0;
}

@keyframes smoothScroll {
	0% {
		transform: translateY(-142px);
	}

	100% {
		transform: translateY(0px);
	}
}

.header-style-one .stricked-menu .header__wrap .thm-btn--header {
  color: var(--color-white);
  background: var(--color-primary);
}

.header-style-one .stricked-menu .header__wrap .thm-btn--header span svg path {
  fill: var(--color-white);
}

.header-style-one .stricked-menu .header__wrap .btn-effect_1::before {
  background: #111112;
}

.header-style-one .main-menu ul li .submenu li:hover>a,
.header-style-one .main-menu ul li .submenu li.active>a {
  color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.header-style-one .header-bar-mobile {
  margin-left: auto;
}

@media (max-width: 1199px) {
  .header-style-one .main-menu__wrap {
    display: none;
  }
}

.header-style-one .xb-nav-mobile {
  color: var(--color-white);
}

.header-style-one .xb-nav-mobile:hover {
  color: var(--color-white);
}

.header-style-one .stricked-menu .main-menu__wrap,
.header-style-two .stricked-menu .main-menu__wrap {
  padding: 0;
  background: none;
  box-shadow: none;
}

.header-style-one .stricked-menu .main-menu__wrap::after,
.header-style-one .stricked-menu .main-menu__wrap::before,
.header-style-two .stricked-menu .main-menu__wrap::after,
.header-style-two .stricked-menu .main-menu__wrap::before {
  display: none;
}

.header-style-one .stricked-menu .main-menu__wrap .main-menu>ul>li>a,
.header-style-two .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
  padding: 6px 19px;
  color: var(--color-black);
  background: transparent;
}

@media (max-width: 1199px) {

  .header-style-one .stricked-menu .main-menu__wrap .main-menu>ul>li>a,
  .header-style-two .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
    padding: 6px 19px;
  }
}

.header-style-one .stricked-menu .main-menu__wrap .main-menu>ul>li .submenu,
.header-style-two .stricked-menu .main-menu__wrap .main-menu>ul>li .submenu {
  transform: translateY(-3px);
}

.header-style-one .header-bar-mobile,
.header-style-two .header-bar-mobile {
  margin-left: auto;
  margin-right: 30px;
}

@media (max-width: 767px) {

  .header-style-one .header-bar-mobile,
  .header-style-two .header-bar-mobile {
    margin-right: 0;
  }
}

.header-style-two .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
  color: var(--color-black);
  background: transparent;
}

.header-style-two .stricked-menu .main-menu__wrap .main-menu>ul>li .submenu {
  transform: translateY(-3px);
}

.header-style-two .header-contact {
  margin-left: 30px;
}

.header-style-four .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
  padding: 6px 19px;
}

.header-style-four .main-menu ul li .submenu li:hover>a,
.header-style-four .main-menu ul li .submenu li.active>a {
  color: var(--color-primary-three);
  background-color: rgba(var(--color-primary-three-rgb), 0.1);
}

.header-style-four .xb-nav-mobile {
  color: var(--color-black);
}

.header-style-four .xb-nav-mobile:hover {
  color: var(--color-black);
}

.header-style-four .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
  padding: 6px 19px;
}

@media (max-width: 1199px) {
  .header-style-four .stricked-menu .main-menu__wrap .main-menu>ul>li>a {
    padding: 6px 10px;
  }
}

.header-style-four .main-menu ul li .submenu li:hover>a,
.header-style-four .main-menu ul li .submenu li.active>a {
  color: var(--color-primary-three);
  background-color: rgba(var(--color-primary-three-rgb), 0.1);
}

.main-menu {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.main-menu ul {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
}

.main-menu ul li {
  position: relative;
}

.main-menu ul li:not(:last-child) {
  margin-right: 2px;
}

.main-menu ul li .submenu li {
  margin-right: 0;
  padding: 0 6px;
}

.main-menu ul li a {
  z-index: 3;
  font-size: 16px;
  font-weight: 500;
  padding: 6px 19px;
  position: relative;
  text-decoration: none;
  display: inline-block;
  letter-spacing: 0.01em;
  color: var(--color-white);
  text-transform: capitalize;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  font-family: var(--font-heading);
}

@media (max-width: 1199px) {
  .main-menu ul li a {
    padding: 6px 10px;
  }
}

.main-menu ul li.active>a,
.main-menu ul li:hover>a {
  background: var(--color-white);
  color: var(--color-black);
}

.main-menu ul li.menu-item-has-children>a span::after {
  content: "+";
  display: inline-block;
  padding-left: 6px;
}

.main-menu ul li.menu-item-has-children:hover>.submenu {
  opacity: 1;
  visibility: visible;
  top: calc(100% + 0px);
}

.main-menu ul li .submenu {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  position: absolute;
  min-width: 240px;
  top: 100%;
  opacity: 0;
  visibility: hidden;
  background: #fff;
  left: 0;
  padding: 6px 0;
  -webkit-transition: 200ms;
  -o-transition: 200ms;
  transition: 200ms;
  z-index: 3;
  top: calc(100% + 10px);
  text-align: left;
  box-shadow: 0 0.5rem 1.875rem rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}

.main-menu ul li .submenu li:not(:last-child) {
  margin-bottom: 1px;
}

.main-menu ul li .submenu li a {
  padding: 8px 22px;
  display: block;
  margin: 0;
  font-size: 16px;
  text-transform: capitalize;
  letter-spacing: 0;
  color: var(--color-black);
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}

.main-menu ul li .submenu li:hover>a,
.main-menu ul li .submenu li.active>a {
  color: var(--color-primary-two);
  background-color: rgba(var(--color-primary-two-rgb), 0.1);
}

.main-menu ul li .submenu ul {
  left: 100%;
  top: 0px;
}

.main-menu ul li .submenu ul::before {
  display: none;
}

.main-menu>ul>li {
  padding: 10px 0;
}

.mega_menu_wrapper {
  width: 100%;
  left: 0;
  right: 0;
  position: fixed;
  max-width: 1320px;
  margin: auto;
}

.mega_menu_wrapper_inner {
  padding: 30px;
  padding-bottom: 40px;
  background: #fff;
  border: none;
  border-radius: 0px;
  z-index: 2;
  box-shadow: -2px 24px 52px 0 rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  overflow: hidden;
}

.main-menu ul li.menu-last ul.submenu {
  right: 0;
  left: auto;
}

.main-menu ul li.menu-last ul.submenu ul {
  right: auto;
  left: -100%;
}

.main-menu ul li ul.submenu .menu-item-has-children>a span::after {
  position: absolute;
  top: 9px;
  right: 15px;
  content: "\f105";
  font-size: 13px;
  font-family: 'Font Awesome 5 Pro';
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.main-menu ul li.megamenu .submenu {
  padding: 0;
  background: transparent;
  box-shadow: none !important;
  border: none;
}

.stricked-menu .main-menu ul li.megamenu .submenu {
  position: fixed;
  left: 0;
  right: 0;
}

.iconbox_block {
  padding: 50px 50px 42px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  box-shadow: 0 1px 2px 0 rgba(174, 191, 210, 0.3);
}

.iconbox_block:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3);
}

.iconbox_block .iconbox_icon {
  width: 80px;
  height: 80px;
  flex: 0 0 auto;
  margin: 0 0 32px;
  border-radius: 100%;
  align-items: center;
  display: inline-flex;
  justify-content: center;
  color: var(--color-primary-two);
  background-color: var(--bs-primary-bg-subtle);
}

.iconbox_block .iconbox_icon svg {
  max-width: 40px;
}

.iconbox_block .iconbox_title {
  font-size: 30px;
  font-weight: 600;
  line-height: 36px;
  margin-bottom: 20px;
}

.iconbox_block p {
  font-size: 16px;
}

.iconbox_block.layout_icon_left {
  padding: 30px;
  display: inline-flex;
  align-items: flex-start;
  border-radius: var(--bs-border-radius);
}

.iconbox_block.layout_icon_left .iconbox_icon {
  width: 70px;
  height: 70px;
  margin: 0 30px 0 0;
  border-radius: 10px;
}

.iconbox_block.layout_icon_left .iconbox_icon svg {
  max-width: 32px;
}

.iconbox_block.layout_icon_left .iconbox_title {
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 12px;
}

.mega_menu_wrapper .iconbox_block_2 {
  display: block;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px !important;
  -webkit-border-radius: 10px !important;
  -moz-border-radius: 10px !important;
  -ms-border-radius: 10px !important;
  -o-border-radius: 10px !important;
  padding: 15px !important;
  padding-bottom: 12px !important;
  border: 1px solid #EFF0F3;
  margin-bottom: 10px !important;
  line-height: 1.3;
}

.mega_menu_wrapper .iconbox_block_2 .icon_title_wrap {
  gap: 10px;
  display: flex !important;
  margin-bottom: 10px;
  align-items: flex-start;
}

.mega_menu_wrapper .iconbox_block_2 .iconbox_icon {
  width: 30px;
  height: 30px;
  flex: 0 0 auto;
  border-radius: 5px;
  align-items: center;
  display: inline-flex;
  justify-content: center;
  background-color: rgba(0, 68, 235, 0.1);
}

.mega_menu_wrapper .iconbox_block_2 .iconbox_icon svg {
  height: 16px;
}

.iconbox_block_2 .iconbox_icon img {
  max-width: 16px;
}

.mega_menu_wrapper .iconbox_block_2 .iconbox_title {
  line-height: 1;
  margin: 6px 0 0;
  font-size: 20px;
  font-weight: 700;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .iconbox_block_2 .iconbox_title {
    font-size: 16px;
  }
}

.mega_menu_wrapper .iconbox_block_2:hover .iconbox_title {
  color: var(--color-primary-two);
}

.mega_menu_wrapper .iconbox_block_2 .badge {
  margin-top: 5px;
}

.mega_menu_wrapper .iconbox_block_2 .description {
  font-size: 16px;
  color: #494D57;
  font-weight: 400;
  font-family: var(--font-body);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .mega_menu_wrapper .iconbox_block_2 .description {
    font-size: 15px;
  }
}

.mega_menu_wrapper .iconbox_block_2:hover {
  background-color: #E8EFFF;
}

.mega_menu_wrapper .btn {
  display: inline-flex !important;
  margin: 0;
  border-radius: 50px !important;
  padding: 0 40px !important;
  color: #fff;
}

.mega_menu_wrapper .btn:hover {
  background-color: var(--color-primary-two) !important;
  color: #fff !important;
}

.review_short_info img {
  max-width: 128px;
}

.review_short_info span {
  line-height: 1;
  font-size: 26px;
  font-weight: 700;
  margin-left: 15px;
  color: var(--color-primary-two);
}

.review_short_info .review_counter {
  margin-top: 6px;
  font-size: 18px;
}

.review_short_info .review_counter b {
  color: var(--bs-dark);
}

.review_short_info_2 {
  display: flex;
  align-items: center;
  background-color: #F6F6F8;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  border: 1px solid #E7E8EC;
}

.review_short_info_2 .review_admin_logo {
  height: 60px;
  width: 110px;
  flex: 0 0 auto;
  align-items: center;
  display: inline-flex;
  justify-content: center;
  border-right: 1px solid #fff;
}

@media (max-width: 1199px) {
  .review_short_info_2 .review_admin_logo {
    width: 85px;
  }
}

.review_admin_logo img {
  max-width: 72px;
}

.review_short_info_2 .review_info_content {
  flex: 0 0 auto;
  padding: 2px 25px;
}

.review_short_info_2 .rating_block {
  margin-bottom: 8px;
}

.review_short_info_2 .rating_block>li {
  padding: 0 !important;
  font-size: 11px;
}

.review_short_info_2 .review_counter {
  line-height: 1;
  font-size: 12px;
}

.author_box .author_image {
  width: 90px;
  height: 90px;
  flex: 0 0 auto;
  border-radius: 5px;
  overflow: hidden;
  display: inline-flex;
  align-items: flex-end;
  justify-content: center;
}

.mega_menu_wrapper .autpr_box {
  height: calc(100% + 70px);
  background-color: #0C111D;
  margin-right: -30px;
  margin-top: -30px;
  padding: 50px 30px 50px;
  position: relative;
  z-index: 1;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .autpr_box {
    margin-top: 30px;
    margin-right: 0;
    padding: 40px 20px 20px;
  }
}

.author_box_quote {
  position: absolute;
  top: 50px;
  right: 53px;
  z-index: -1;
}

.mega_menu_wrapper .author_box {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 50px;
}

.author_box_content h3 {
  font-size: 16px;
  margin-bottom: 5px;
}

.mega_menu_wrapper .site_author p {
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  padding-right: 25px;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .site_author p {
    padding-right: 0;
    font-size: 16px;
  }
}

.megamenu-btn {
  padding: 20px 30px !important;
  color: var(--color-white) !important;
  border-radius: 7px !important;
  -webkit-border-radius: 7px !important;
  -moz-border-radius: 7px !important;
  -ms-border-radius: 7px !important;
  -o-border-radius: 7px !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
}

.megamenu-btn:hover {
  color: var(--color-heading-two) !important;
}

@media (max-width: 1199px) {
  .megamenu_pages_wrapper {
    margin-bottom: 30px !important;
  }
}

.rating_block li {
  color: #FFB600;
  line-height: 1.2;
  font-size: 11px;
}

.rating_block li:not(:last-child) {
  margin-right: 4px !important;
}

.mega_menu_wrapper_inner .btns_group {
  display: flex;
  align-items: center;
  gap: 20px 45px;
  list-style: none;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper_inner .btns_group {
    flex-wrap: wrap;
  }
}

.mega_menu_wrapper_inner .btns_group li {
  padding: 0 !important;
}

.mega_menu_wrapper .social_area {
  display: flex;
  padding: 36px 0;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #E7E8EC;
  padding: 10px 10px 20px 10px;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .social_area {
    border: 0;
    flex-wrap: wrap;
  }
}

.mega_menu_wrapper .social_area a {
  padding: 0 !important;
}

.mega_menu_wrapper .social_icons_block {
  gap: 9px;
  display: flex;
  list-style: none;
}

.social_inner {
  gap: 20px;
}

.mega_menu_wrapper .social_icons_block li a:hover svg {
  filter: brightness(0%);
}

.career_link {
  display: flex;
  gap: 7px;
  flex-wrap: wrap;
}

.career_link a {
  color: #FF3400 !important;
  text-decoration: underline !important;
  line-height: 1.3 !important;
}

.mega_menu_wrapper .social_icons_block a {
  width: auto;
  height: auto;
  border: none;
  background: none;
  font-size: 18px !important;
  background: none !important;
}

.mega_menu_wrapper .social_icons_block a:hover {
  color: var(--color-primary-two);
}

.mega_menu_wrapper .social_icons_block a [class*="fa-facebook"] {
  color: #3D6AD6 !important;
}

.mega_menu_wrapper .social_icons_block a [class*="fa-twitter"] {
  color: #000000;
}

.mega_menu_wrapper .social_icons_block a [class*="fa-linkedin"] {
  color: #0073B1;
}

.mega_menu_wrapper .social_icons_block a [class*="fa-dribbble"] {
  color: #D31F61;
}

.megamenu_widget_inner>.row {
  margin: 0 -50px;
}

@media (max-width: 1199px) {
  .megamenu_widget_inner>.row {
    margin: 0;
  }
}

.mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget)>[class*="col-"] {
  padding: 0 50px 85px;
  margin-top: 67px;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget)>[class*="col-"] {
    padding: 0 10px;
    margin-top: 10px;
  }
}

.mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget)>[class*="col-"]:not(:last-child) {
  border-style: solid;
  border-width: 0 1px 0 0;
  border-color: #E7E8EC;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget)>[class*="col-"]:not(:last-child) {
    border: 0;
  }
}

.mega_menu_wrapper .megamenu_widget ul {
  gap: 29px 22px;
  flex-direction: column;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .megamenu_widget ul {
    gap: 14px 22px;
  }
}

.megamenu_widget_wrapper {
  padding: 0;
  padding-left: 70px;
}

.mega_menu_wrapper .megamenu_widget ul li {
  padding: 0 !important;
}

.mega_menu_wrapper .megamenu_widget ul li a {
  line-height: 1;
  padding: 0 !important;
  font-size: 22px !important;
  font-weight: 700;
  display: inline-block !important;
}

@media (max-width: 1199px) {
  .mega_menu_wrapper .megamenu_widget ul li a {
    font-size: 18px !important;
  }
}

.mega_menu_wrapper .megamenu_widget ul li a:hover {
  color: var(--color-primary-two);
}

.mega_menu_wrapper .megamenu_widget ul li a:hover,
.mega_menu_wrapper .megamenu_widget ul li:hover a {
  background-color: transparent !important;
}

.mega_menu_wrapper .megamenu_widget ul li:hover a {
  color: var(--bs-dark) !important;
}

.mega_menu_wrapper .megamenu_widget ul li a:hover {
  color: var(--color-primary-two) !important;
}

.stricked-menu .main-menu .megamenu_widget ul li a {
  padding: 0 !important;
}

.mega_menu_wrapper .social_icons_block li a:hover {
  background: transparent !important;
  border: transparent;
}

.mega_menu_wrapper .social_icons_block li a svg {
  height: 16px;
}

@media screen and (min-width: 992px) {
  .dropdown:hover>.mega_menu_wrapper {
    transform: translate(0%, 0px);
  }
}

.megamenu_case {
  padding: 30px;
  height: 100%;
  background-color: #0C111D;
  margin-left: -43px;
}

@media (max-width: 1199px) {
  .megamenu_case {
    margin-left: 0;
  }
}

.megamenu_case h3 {
  line-height: 1;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
  color: var(--color-white);
  text-transform: uppercase;
}

.megamenu_case h4 {
  line-height: 1;
  font-size: 24px;
  font-weight: 700;
  margin: 10px 0 0;
  color: var(--color-white);
}

.megamenu_case img {
  margin: 50px 0 35px;
  display: block;
}

.megamenu_case .btn {
  box-shadow: none;
  background-color: var(--color-white);
}

.megamenu_case .btn:hover {
  color: var(--color-black) !important;
  background: var(--color-white) !important;
}

.megamenu_btn {
  color: var(--color-white) !important;
  text-decoration: underline !important;
  font-size: 18px !important;
  display: flex !important;
  gap: 15px;
  padding: 0 !important;
}

.sec-title .title {
  font-size: 48px;
  letter-spacing: -0.02em;
  color: var(--color-heading);
}

@media (max-width: 991px) {
  .sec-title .title {
    font-size: 40px;
    margin-bottom: 15px;
  }
}

@media (max-width: 767px) {
  .sec-title .title {
    font-size: 32px;
  }
}

.sec-title .content {
  max-width: 429px;
  display: inline-block;
}

.sec-title--two .sub-title {
  font-family: var(--font-family);
  font-weight: 500;
  font-size: 14px;
  background: #fff;
  padding: 2px 10px;
  margin-bottom: 22px;
  display: inline-flex;
  align-items: center;
  border-radius: 14px;
  -webkit-border-radius: 14px;
  -moz-border-radius: 14px;
  -ms-border-radius: 14px;
  -o-border-radius: 14px;
  color: var(--color-heading-two);
  box-shadow: 0 2px 4px 0 rgba(44, 64, 94, 0.08), 0 1px 1px 0 rgba(44, 64, 94, 0.04), 0 0 0 1px rgba(44, 64, 94, 0.06);
}

.sec-title--two .sub-title img {
  margin-right: 6px;
}

.sec-title--two .sub-title--strock {
  color: var(--color-white);
  background: #0f55dc;
  box-shadow: 0 2px 4px 0 rgba(255, 255, 255, 0.1), 0 1px 1px 0 rgba(255, 255, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.sec-title--two .title {
  font-weight: 700;
  font-size: 48px;
  line-height: 58px;
  color: var(--color-heading-two);
}

@media (max-width: 1199px) {
  .sec-title--two .title {
    font-size: 38px;
    line-height: 46px;
  }
}

@media (max-width: 767px) {
  .sec-title--two .title {
    font-size: 32px;
    line-height: 40px;
  }
}

.sec-title--two .content {
  font-size: 20px;
  line-height: 32px;
  margin-top: 10px;
  display: inline-block;
  letter-spacing: -0.02em;
}

.sec-title--three .sub-title {
  border: 1px solid rgba(255, 255, 255, 0.22);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 100%);
  box-shadow: 0 2px 4px 0 rgba(44, 64, 94, 0.08), 0 1px 1px 0 rgba(44, 64, 94, 0.04), 0 0 0 1px rgba(44, 64, 94, 0.06);
}

.sec-title--three .sub-title img {
  margin-right: 6px;
}

.sec-title--three .sub-title span {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.9) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sec-title--three .title {
  color: var(--color-white);
}

.sec-title--three .content {
  font-size: 16px;
  line-height: 26px;
  margin-top: 15px;
  letter-spacing: 0;
}

.sec-title--four .sub-title {
  margin-bottom: 27px;
  background: #f6f0e6;
}

.sec-title--four .title {
  font-size: 64px;
  color: var(--color-heading-three);
}

@media (max-width: 1199px) {
  .sec-title--four .title {
    font-size: 44px;
    line-height: 52px;
  }
}

@media (max-width: 767px) {
  .sec-title--four .title {
    font-size: 38px;
    line-height: 46px;
  }
}

.sec-title--five .title {
  font-size: 45px;
  line-height: 65px;
  margin-bottom: 10px;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  color: var(--color-heading-four);
}

@media (max-width: 767px) {
  .sec-title--five .title {
    font-size: 28px;
    line-height: 45px;
  }
}

.sec-title--five .content {
  font-size: 22px;
  line-height: 38px;
  text-align: center;
  color: #212877;
  max-width: 885px;
  display: inline-block;
  text-transform: capitalize;
}

.white-sec-title .title {
  color: var(--color-white);
}

.white-sec-title .content {
  color: #c2c4e4;
}

.da-sec-titlte .sub_title {
  font-family: var(--font-heading-five);
  font-weight: 700;
  font-size: 11px;
  margin-bottom: 20px;
  letter-spacing: 0.18em;
  text-transform: uppercase;
  color: #1337bb;
  display: inline-flex;
  align-items: center;
  border-bottom: 1px solid rgba(19, 55, 187, 0.25);
}

.da-sec-titlte .sub_title span {
  margin-right: 8px;
}

.da-sec-titlte .title {
  font-size: 44px;
  font-weight: 800;
  line-height: 56px;
  letter-spacing: -0.02em;
  color: var(--color-heading-four);
}

@media (max-width: 991px) {
  .da-sec-titlte .title {
    font-size: 36px;
    line-height: 49px;
  }
}

@media (max-width: 767px) {
  .da-sec-titlte .title {
    font-size: 26px;
    line-height: 39px;
  }
}

.da-sec-titlte .content {
  font-weight: 600;
  font-size: 24px;
  line-height: 40px;
  margin-top: 12px;
  max-width: 685px;
  display: inline-block;
}

.fanfact-item {
  padding: 45px 30px;
  position: relative;
  cursor: pointer;
  max-height: 288px;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-white);
}

.fanfact-item::before {
  top: 0;
  left: 0;
  opacity: 0;
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  transform: scale(0.9);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-heading);
}

.fanfact-item:hover::before {
  opacity: 1;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  transform: scale(1);
}

.fanfact-item:hover .xb-item--title,
.fanfact-item:hover .xb-item--content,
.fanfact-item:hover .xb-item--number,
.fanfact-item:hover .xb-item--text {
  color: var(--color-white);
}

.fanfact-item:hover .fanfact-icon .icon {
  opacity: 1;
  transform: scale(1);
}

.fanfact-item .xb-item--title {
  font-size: 20px;
  margin-bottom: 10px;
  letter-spacing: 0em;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.fanfact-item .xb-item--content {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 1199px) {
  .fanfact-item .xb-item--content {
    max-width: 235px;
  }
}

.fanfact-item .xb-item--number {
  font-weight: 500;
  font-size: 48px;
  display: block;
  margin: 45px 0 20px;
  letter-spacing: -0.02em;
  color: var(--color-heading);
  font-family: var(--font-heading);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 1199px) {
  .fanfact-item .xb-item--number {
    font-size: 35px;
  }
}

@media only screen and (max-width: 992px) {
  .fanfact-item .xb-item--number {
    font-size: 30px;
  }
}

.fanfact-item .xb-item--text {
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0em;
  color: var(--color-heading);
  font-family: var(--font-heading);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media only screen and (max-width: 992px) {
  .fanfact-item .xb-item--text {
    font-size: 18px;
  }
}

.fanfact-item .fanfact-icon .icon {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  transform: scale(0.6);
  border: 3px solid #fff;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.fanfact-item .fanfact-icon .icon--one {
  top: -130px;
  right: 132px;
  height: 108px;
  width: 108px;
  transition-delay: .1s;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon .icon--one {
    top: -100px;
    right: 125px;
    height: 90px;
    width: 90px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon .icon--one {
    top: -85px;
    height: 80px;
    width: 80px;
  }
}

.fanfact-item .fanfact-icon .icon--two {
  height: 100px;
  width: 100px;
  left: 30px;
  bottom: -74px;
  transition-delay: .2s;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon .icon--two {
    height: 80px;
    width: 80px;
    left: 50px;
    bottom: -50px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon .icon--two {
    height: 60px;
    width: 60px;
    left: 58px;
    bottom: -33px;
  }
}

.fanfact-item .fanfact-icon .icon--three {
  bottom: -60px;
  right: 28px;
  height: 133px;
  width: 133px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition-delay: .3s;
  background: var(--color-primary);
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon .icon--three {
    bottom: -40px;
    right: 11px;
    height: 100px;
    width: 100px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon .icon--three {
    width: 85px;
    height: 85px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon .icon--three {
    right: 35px;
    bottom: -30px;
  }
}

.fanfact-item .fanfact-icon--two .icon--one {
  top: -120px;
  right: 25px;
  height: 72px;
  width: 72px;
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon--two .icon--one {
    top: 65px;
    right: -29px;
    width: 65px;
    height: 65px;
  }
}

@media (max-width: 767px) {
  .fanfact-item .fanfact-icon--two .icon--one {
    top: 86px;
    right: -12px;
    width: 55px;
    height: 55px;
  }
}

.fanfact-item .fanfact-icon--two .icon--two {
  height: 100px;
  width: 100px;
  right: 47px;
  bottom: -74px;
  left: auto;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon--two .icon--two {
    height: 80px;
    width: 80px;
    right: 70px;
    bottom: -53px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon--two .icon--two {
    height: 75px;
    width: 75px;
    bottom: -30px;
  }
}

.fanfact-item .fanfact-icon--two .icon--three {
  top: -93px;
  right: 112px;
  transition-delay: .2s;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon--two .icon--three {
    top: -70px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon--two .icon--three {
    top: -35px;
  }
}

.fanfact-item .fanfact-icon--three .icon--one {
  top: -61px;
  left: 30px;
  height: 86px;
  width: 86px;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon--three .icon--one {
    top: -50px;
    left: 40px;
    height: 76px;
    width: 76px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon--three .icon--one {
    top: -32px;
    left: 50px;
    height: 66px;
    width: 66px;
  }
}

.fanfact-item .fanfact-icon--three .icon--two {
  height: 92px;
  width: 92px;
  right: -54px;
  bottom: 88px;
  left: auto;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon--three .icon--two {
    height: 72px;
    width: 72px;
    right: -13px;
    bottom: 105px;
  }
}

@media (max-width: 991px) {
  .fanfact-item .fanfact-icon--three .icon--two {
    height: 62px;
    width: 62px;
    right: -30px;
    bottom: 128px;
  }
}

@media (max-width: 767px) {
  .fanfact-item .fanfact-icon--three .icon--two {
    height: 55px;
    width: 55px;
    right: -13px;
  }
}

.fanfact-item .fanfact-icon--three .icon--three {
  bottom: -67px;
}

@media (max-width: 1199px) {
  .fanfact-item .fanfact-icon--three .icon--three {
    bottom: -55px;
  }
}

.xb-fanfact-item {
  width: 31.5%;
  padding: 47px 40px 45px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: rgba(229, 160, 227, 0.16);
  margin-top: 30px;
}

@media (max-width: 991px) {
  .xb-fanfact-item {
    padding: 30px 20px;
  }
}

@media (max-width: 767px) {
  .xb-fanfact-item {
    width: 100%;
  }
}

.xb-fanfact-item .xb-item--number {
  font-weight: 600;
  font-size: 50px;
  letter-spacing: -0.05em;
  color: var(--color-heading-three);
  font-family: var(--font-body);
}

@media (max-width: 991px) {
  .xb-fanfact-item .xb-item--number {
    font-size: 30px;
  }
}

@media (max-width: 1199px) {
  .xb-fanfact-item .xb-item--number {
    font-size: 42px;
  }
}

.xb-fanfact-item--two {
  background: rgba(217, 244, 137, 0.36);
}

.xb-fanfact-item--four {
  background: rgba(100, 64, 224, 0.09);
}

.cd-fanfact-inner {
  margin-left: -60px;
  margin-right: -60px;
  padding: 106px 60px;
  background: #161c66;
  border-radius: 26px;
  -webkit-border-radius: 26px;
  -moz-border-radius: 26px;
  -ms-border-radius: 26px;
  -o-border-radius: 26px;
}

.cd-fanfact-inner .xb-item--item_box {
  min-height: 233px;
  max-width: 305px;
}

.cd-fanfact-inner .xb-item--item_box:nth-child(2) .xb-item--number {
  color: var(--color-primary-four);
}

.cd-fanfact-inner .xb-item--item_box:nth-child(3) .xb-item--number {
  color: #bb33df;
}

.cd-fanfact-inner .xb-item--number {
  font-size: 80px;
  font-weight: 800;
  color: #00b59f;
  display: block;
  margin-bottom: 48px;
  letter-spacing: -0.02em;
  font-family: var(--font-heading-four);
}

@media (max-width: 767px) {
  .cd-fanfact-inner .xb-item--number {
    font-size: 50px;
    margin-bottom: 20px;
  }
}

.cd-fanfact-inner .xb-item--number .time {
  font-size: 40px;
  font-weight: 500;
  margin-left: -5px;
  display: inline-block;
  transform: translateY(-10px);
}

.cd-fanfact-inner .xb-item--number .suffix {
  margin-left: 10px;
}

.cd-fanfact-inner .xb-item--content {
  font-weight: 500;
  font-size: 24px;
  line-height: 37px;
  letter-spacing: -0.01em;
  text-transform: capitalize;
  color: #fff;
}

.ap-fanfact-item .xb-item--number {
  font-weight: 700;
  font-size: 130px;
  margin-bottom: 7px;
  letter-spacing: -0.01em;
  color: var(--color-primary-two);
}

@media (max-width: 1199px) {
  .ap-fanfact-item .xb-item--number {
    font-size: 80px;
  }
}

@media (max-width: 991px) {
  .ap-fanfact-item .xb-item--number {
    font-size: 50px;
  }
}

.ap-fanfact-item .xb-item--text {
  font-family: var(--font-heading);
  font-weight: 700;
  font-size: 20px;
  text-transform: uppercase;
  color: var(--color-heading-two);
}

@media (max-width: 991px) {
  .ap-fanfact-item .xb-item--text {
    font-size: 18px;
  }
}

.ap-fanfact-item--last {
  float: right;
}

@media (max-width: 767px) {
  .ap-fanfact-item--last {
    float: none;
  }
}

.ap-fanfact-item--middle {
  padding-left: 60px;
}

@media (max-width: 991px) {
  .ap-fanfact-item--middle {
    padding-left: 0;
  }
}

.pg-fanfact-item {
  position: relative;
}

.pg-fanfact-item:not(:last-child)::before {
  position: absolute;
  height: 166px;
  width: 2px;
  content: '';
  top: 22px;
  right: -100px;
  background: rgba(12, 17, 29, 0.1);
}

@media (max-width: 1199px) {
  .pg-fanfact-item:not(:last-child)::before {
    display: none;
  }
}

.pg-fanfact-item .xb-item--number {
  font-size: 80px;
}

@media (max-width: 767px) {
  .pg-fanfact-item .xb-item--number {
    font-size: 54px;
  }
}

.pg-fanfact-item .xb-item--content {
  max-width: 294px;
  display: block;
  margin-top: 13px;
}

.pg-fan-bg {
  background: linear-gradient(360deg, #f6f6f8 0%, #fff 100%);
}

.about-item {
  padding: 19px 40px;
  width: 23.3%;
  min-height: 356px;
  position: relative;
  overflow: hidden;
  background: #29292a;
  transform: translateX(0);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}

@media (max-width: 767px) {
  .about-item {
    width: 100%;
  }
}

.about-item .xb-item--img {
  position: absolute;
  top: 0;
  right: 0;
}

@media (max-width: 1199px) {
  .about-item .xb-item--img {
    max-width: 200px;
  }
}

@media (max-width: 991px) {
  .about-item .xb-item--img {
    max-width: 130px;
  }
}

@media (max-width: 767px) {
  .about-item .xb-item--img {
    max-width: 150px;
  }
}

.about-item .xb-item--heading {
  font-size: 26px;
  letter-spacing: 0em;
  color: var(--color-white);
  position: absolute;
  bottom: 55px;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1199px) {
  .about-item .xb-item--heading {
    font-size: 24px;
  }
}

@media (max-width: 991px) {
  .about-item .xb-item--heading {
    font-size: 18px;
  }
}

@media (max-width: 767px) {
  .about-item .xb-item--heading {
    display: none;
  }
}

.about-item .xb-item--heading span {
  margin-right: 9px;
}

.about-item .xb-item--holder {
  top: 42px;
  position: relative;
}

.about-item .xb-item--title {
  display: inline-block;
  padding-bottom: 52px;
  font-size: 26px;
  letter-spacing: 0em;
  color: var(--color-white);
  border-bottom: 1px solid #d44a00;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  display: inline-flex;
  align-items: center;
}

@media (max-width: 1199px) {
  .about-item .xb-item--title {
    font-size: 24px;
  }
}

@media (max-width: 991px) {
  .about-item .xb-item--title {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .about-item .xb-item--title {
    opacity: 1;
    visibility: visible;
    transform: translateY(0px);
  }
}

.about-item .xb-item--title span {
  margin-right: 9px;
}

.about-item .xb-item--content {
  margin-top: 61px;
  width: 472px;
  opacity: 0;
  visibility: hidden;
  display: block;
  color: var(--color-white);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  transform: translateY(20px);
}

@media (max-width: 1199px) {
  .about-item .xb-item--content {
    width: 380px;
  }
}

@media (max-width: 991px) {
  .about-item .xb-item--content {
    width: 286px;
    margin-top: 25px;
  }
}

@media (max-width: 767px) {
  .about-item .xb-item--content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0px);
  }
}

.about-item.active {
  width: 48.9%;
  transform: translateX(1);
}

@media (max-width: 767px) {
  .about-item.active {
    max-width: 100%;
    width: 100%;
  }
}

.about-item.active .xb-item--title,
.about-item.active .xb-item--content {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
  transition-delay: .2s;
}

.about-item.active .xb-item--title {
  transition-delay: .1s;
}

.about-item.active .xb-item--heading {
  opacity: 0;
  visibility: hidden;
}

.about-left {
  position: relative;
  transform: translateY(5px);
}

.about-left::before {
  position: absolute;
  top: 51%;
  right: 0;
  content: '';
  height: 96.5%;
  width: 2px;
  transform: translateY(-50%);
  background-color: rgba(12, 17, 29, 0.1);
}

@media (max-width: 1199px) {
  .about-left::before {
    display: none;
  }
}

.about-left .title {
  font-size: 34px;
  margin-bottom: 15px;
  display: inline-block;
  color: var(--color-heading-two);
}

.about-left .about-item_box {
  margin-top: 40px;
}

.about-left .about-item_box .xb-item--icon {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  background: #ffffff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.08);
}

.about-left .about-item_box .xb-item--holder {
  width: calc(100% - 90px);
}

.about-left .about-item_box .xb-item--content {
  font-size: 20px;
  line-height: 32px;
  display: inline-block;
  max-width: 410px;
}

.about-left .about-item_box .xb-item--content span {
  font-weight: 600;
  color: var(--color-heading-two);
}

.about-right {
  max-width: 517px;
  margin-left: 105px;
}

@media (max-width: 991px) {
  .about-right {
    margin-left: 0;
  }
}

.about-right .xb-item--title {
  font-size: 34px;
  margin-bottom: 14px;
  color: var(--color-heading-two);
}

.about-right .xb-item--content {
  font-size: 20px;
  line-height: 32px;
  letter-spacing: -0.02em;
}

.about-right .xb-item--holder:not(:last-child) {
  margin-bottom: 75px;
}

@media (max-width: 991px) {
  .about-right .xb-item--holder:not(:last-child) {
    margin-bottom: 30px;
  }
}

.about-wrapper {
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}

.cs-about_right {
  position: relative;
  min-height: 432px;
}

@media (max-width: 1199px) {
  .cs-about_right {
    max-width: 450px;
    margin-left: auto;
  }
}

@media (max-width: 991px) {
  .cs-about_right {
    max-width: 450px;
    margin-left: 0;
    margin-right: auto;
  }
}

.cs-about_right .xb-img {
  position: absolute;
  right: 0;
  top: 0;
}

.cs-about_right .xb-content {
  top: -31px;
  left: 75px;
  z-index: 1;
  width: 302px;
  height: auto;
  position: relative;
  text-align: center;
  background: #010315;
  border-radius: 30px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -ms-border-radius: 30px;
  -o-border-radius: 30px;
}

@media (max-width: 1199px) {
  .cs-about_right .xb-content {
    top: -46px;
    left: -35px;
  }
}

.cs-about_right .xb-content .xb-item--img {
  position: absolute;
  top: 0;
  left: 0;
}

.cs-about_right .xb-content .xb-item--inner {
  position: absolute;
  top: 75px;
  left: 60px;
  max-width: 181px;
}

.cs-about_right .xb-content .xb-item--number {
  font-family: var(--font-heading-two);
  font-weight: 700;
  font-size: 70px;
  line-height: 114%;
  letter-spacing: -0.02em;
  text-align: center;
  margin-bottom: 10px;
  background: linear-gradient(180deg, #fff 0%, #010315 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 1px #5F30BC;
}

.cs-about_right .xb-content .xb-item--content {
  font-weight: 500;
  font-size: 18px;
  letter-spacing: -0.01em;
}

.cs-about-wrap {
  z-index: 1;
}

.cs-about-wrap .xb-shape {
  position: absolute;
  right: -315px;
  top: -30%;
  z-index: -1;
}

@media (max-width: 991px) {
  .cs-about-wrap .xb-shape {
    display: none;
  }
}

.da-about-left .title {
  font-size: 50px;
  line-height: 64px;
  color: #212877;
}

@media (max-width: 767px) {
  .da-about-left .title {
    font-size: 30px;
    line-height: 45px;
  }
}

.da-about-left .img {
  margin-left: -30px;
  margin-top: 92px;
}

@media (max-width: 991px) {
  .da-about-left .img {
    display: none;
  }
}

.da-about-right {
  margin-top: 5px;
  z-index: 2;
  position: relative;
}

@media (max-width: 991px) {
  .da-about-right {
    margin-top: 30px;
    margin-bottom: 120px;
  }
}

.da-about-right .content {
  font-weight: 600;
  font-size: 24px;
  line-height: 40px;
  display: inline-flex;
  color: var(--color-heading-four);
}

.da-about-right .content:nth-child(2) {
  margin: 36px 0 50px;
}

.about-img {
  padding-top: 66px;
}

.about-img .xb-img {
  overflow: hidden;
  display: inline-block;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

@media (max-width: 991px) {
  .cs-about_left .xb-btn {
    margin-top: 40px;
  }
}

.xb-brand-title {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

@media (max-width: 767px) {
  .xb-brand-title {
    margin-bottom: 30px;
  }
}

.xb-brand-title span {
  font-weight: 500;
  color: #9c9ca7;
  display: inline-block;
  text-transform: uppercase;
}

.xb-brand-wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
  border: 1px solid #2F3037;
}

.xb-brand-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 20%;
  box-sizing: border-box;
  text-align: center;
  background-color: transparent;
  position: relative;
  z-index: 2;
}

@media (max-width: 767px) {
  .xb-brand-item {
    width: 100%;
    min-height: 150px;
    border-bottom: 1px solid #2F3037;
  }
}

.xb-brand-item img {
  opacity: .5;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 991px) {
  .xb-brand-item img {
    max-width: 100px;
    max-height: 70px;
  }
}

.xb-brand-item--big {
  border-left: 1px solid #2F3037;
  border-right: 1px solid #2F3037;
}

@media (max-width: 767px) {
  .xb-brand-item--big {
    border-left: 0;
    border-right: 0;
  }
}

.xb-brand-hover:hover img {
  opacity: 1;
  transform: translateY(-4px);
}

.xb-brand-single {
  width: 100%;
  padding: 35px 0;
  position: relative;
  z-index: 2;
}

.xb-brand-single:not(:last-child) {
  border-bottom: 1px solid #2F3037;
}

.brand-sub_title {
  margin-top: 10px;
  position: relative;
  margin-bottom: 70px;
}

.brand-sub_title::before {
  position: absolute;
  left: 0;
  top: 50%;
  content: '';
  height: 1px;
  width: 100%;
  background: rgba(12, 17, 29, 0.1);
}

@media (max-width: 767px) {
  .brand-sub_title::before {
    display: none;
  }
}

.brand-sub_title span {
  position: relative;
  z-index: 1;
  font-weight: 500;
  font-size: 18px;
  letter-spacing: -0.02em;
  padding: 8.5px 20px;
  background: #fff;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  border: 1px solid rgba(12, 17, 29, 0.1);
}

@media (max-width: 767px) {
  .brand-sub_title span {
    background: transparent;
    border: none;
    padding: 0;
  }
}

.brand-sub_title span b {
  color: var(--color-heading-two);
}

.brand-logo {
  margin-right: 85px;
  display: inline-block;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 991px) {
  .brand-logo {
    margin-right: 50px;
  }
}

@media (max-width: 450px) {
  .brand-logo img {
    max-width: 60%;
  }
}

.cs-brand-item {
  padding: 1px 1px;
  margin-top: 15px;
  margin-right: 35px;
  display: inline-block;
  border-radius: 23px;
  -webkit-border-radius: 23px;
  -moz-border-radius: 23px;
  -ms-border-radius: 23px;
  -o-border-radius: 23px;
  background: linear-gradient(180deg, #23263c 0%, #010315 100%);
}

.cs-brand-item .xb-inner {
  height: 100%;
  width: 100%;
  background-color: #010315;
  border-radius: 23px;
  -webkit-border-radius: 23px;
  -moz-border-radius: 23px;
  -ms-border-radius: 23px;
  -o-border-radius: 23px;
  padding: 19.5px 42px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.cs-brand_content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 724px;
  height: 267px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cs-brand_content::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background: #010315;
  filter: blur(59.4000015259px);
}

@media (max-width: 1199px) {
  .cs-brand_content::before {
    width: 53%;
    right: 0;
    margin: auto;
  }
}

@media (max-width: 991px) {
  .cs-brand_content::before {
    width: 32%;
  }
}

.cs-brand_content .title {
  font-size: 28px;
}

@media (max-width: 991px) {
  .cs-brand_content .title {
    font-size: 20px;
  }
}

.cs-brand_content .title span {
  text-transform: capitalize;
  text-align: center;
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 26.17%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.cs-brand_content .sub-title {
  transform: rotate(5deg);
  display: inline-block;
  position: absolute;
  top: 35px;
  left: 105px;
}

@media (max-width: 767px) {
  .cs-brand_content .sub-title {
    left: 150px;
  }
}

.cs-brand_content .sub-title>span {
  display: inline-block;
}

.cs-brand_content .sub-title svg {
  transform: rotate(-10deg) translate(-10px, 10px);
}

.cs-brand_content .sub-title--inner {
  font-weight: 500;
  font-size: 16px;
  padding: 2px 10px;
  display: inline-block;
  border-radius: 14px;
  -webkit-border-radius: 14px;
  -moz-border-radius: 14px;
  -ms-border-radius: 14px;
  -o-border-radius: 14px;
  border: 1px solid rgba(255, 255, 255, 0.22);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 100%);
  box-shadow: 0 2px 4px 0 rgba(44, 64, 94, 0.08), 0 1px 1px 0 rgba(44, 64, 94, 0.04), 0 0 0 1px rgba(44, 64, 94, 0.06);
}

.cs-brand_content .sub-title--inner span {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.9) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.cs-brand_content .sub-title .xb-holder {
  position: absolute;
  z-index: 2;
}

.cs-brand_content .sub-title--two {
  bottom: 35px;
  top: auto;
  right: 85px;
  left: auto;
  transform: rotate(5px);
}

@media (max-width: 767px) {
  .cs-brand_content .sub-title--two {
    right: 150px;
  }
}

.cs-brand_content .sub-title--two svg {
  transform: rotate(-10deg) translate(5px, -17px);
}

.xb-brand-wrap .brand-sub_title span {
  background: #fefaf2;
  border: 1px solid #e3dcd0;
}

.xb-brand-wrap .brand-sub_title::before {
  background: #e3dcd0;
}

.cd-brand {
  overflow: hidden;
  padding: 45px 0;
  margin-top: 85px;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-white);
}

.cd-brand-item {
  display: inline-flex;
  margin-right: 70px;
  align-items: center;
}

.cd-brand-item .xb-item--img {
  margin-right: 15px;
  display: inline-block;
}

.cd-brand-item .xb-item--title {
  font-size: 20px;
  letter-spacing: 0em;
  display: inline-block;
}

.cd-brand-item .xb-item--title span {
  color: var(--color-primary-four);
}

.da-brand_inner {
  position: relative;
  overflow: hidden;
  padding: 34px 0;
}

.da-brand_inner::before,
.da-brand_inner::after {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  z-index: 1;
  height: 100%;
  width: 160px;
  background-image: url(../images/shape/da-gradient01.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.da-brand_inner::after {
  right: 0;
  left: auto;
  background-image: url(../images/shape/da-gradient02.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.da-brand_inner .xb-item--item {
  padding: 4px 0;
  min-width: 208px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #d5dcf7;
}

.da-brand_wrapper {
  text-align: center;
}

.da-brand_wrapper .xb-item--title {
  font-size: 40px;
  letter-spacing: -0.03em;
  margin-bottom: 45px;
}

.da-brand_wrapper .xb-item--title span {
  color: #1438bc;
}

.da-brand_wrapper .xb-item--content {
  font-weight: 500;
  font-size: 20px;
  margin-top: 40px;
  letter-spacing: -0.02em;
}

.brand-slider-nav {
  padding-bottom: 30px;
}

@media (max-width: 767px) {
  .pro-top .sec-title {
    margin-bottom: 30px;
  }
}

.project-item {
  padding: 15px;
  position: relative;
  background: var(--color-white);
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.project-item::before,
.project-item::after {
  position: absolute;
  content: '';
  top: 0px;
  left: 30px;
  right: 30px;
  bottom: -20px;
  z-index: -1;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: rgba(255, 255, 255, 0.3);
}

.project-item::after {
  left: 15px;
  right: 15px;
  bottom: -10px;
  background: rgba(255, 255, 255, 0.3);
}

@media (max-width: 991px) {
  .project-item .xb-item--inner {
    align-items: center !important;
  }
}

@media (max-width: 767px) {
  .project-item .xb-item--inner {
    flex-direction: column-reverse;
  }
}

.project-item .xb-item--left_item {
  max-width: 440px;
  margin-right: 75px;
  padding: 45px 0 45px 45px;
}

@media (max-width: 991px) {
  .project-item .xb-item--left_item {
    margin-right: 35px;
    max-width: 411px;
    padding: 45px 0 45px 20px;
  }
}

@media (max-width: 767px) {
  .project-item .xb-item--left_item {
    margin-right: 0;
    max-width: 100%;
    padding: 45px 20px 45px 0px;
  }
}

.project-item .xb-item--icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eaeef0;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.project-item .xb-item--title {
  font-size: 30px;
  letter-spacing: 0em;
  margin: 20px 0 18px;
}

@media (max-width: 767px) {
  .project-item .xb-item--title {
    font-size: 22px;
  }
}

.project-item .xb-item--title a {
  color: currentColor;
}

.project-item .xb-item--right_img {
  overflow: hidden;
  width: calc(100% - 515px);
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.project-item .xb-item--right_img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 991px) {
  .project-item .xb-item--right_img {
    width: calc(100% - 446px);
  }
}

@media (max-width: 767px) {
  .project-item .xb-item--right_img {
    width: 100%;
  }
}

.project-item .work-btn {
  position: absolute;
  bottom: 60px;
  left: 60px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 991px) {
  .project-item .work-btn {
    position: unset;
    margin-top: 50px;
  }
}

.project-item .work-btn a .xb-item--hidden-text {
  margin-right: 6.8rem;
}

@media (max-width: 767px) {
  .project-item .work-btn a .xb-item--hidden-text {
    margin-right: 8.8rem;
  }
}

.project-item:hover .xb-item--right_img img {
  transform: scale(1.2) rotate(2deg);
}

.category {
  margin-top: 35px;
}

.category li:not(:last-child) {
  margin-right: 10px;
}

@media (max-width: 767px) {
  .category li:not(:last-child) {
    margin-bottom: 10px;
  }
}

.category li a {
  font-weight: 500;
  font-size: 12px;
  padding: 1px 9px;
  display: inline-block;
  cursor: pointer;
  text-transform: uppercase;
  border-radius: 14px;
  -webkit-border-radius: 14px;
  -moz-border-radius: 14px;
  -ms-border-radius: 14px;
  -o-border-radius: 14px;
  border: 1px solid #eaeef0;
  color: #49515B;
}

.category li a:hover {
  background: #eaeef0;
}

.project-slider {
  margin-left: -642px;
  margin-right: -658px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .project-slider {
    margin-left: 150px;
    margin-right: 150px;
  }
}

@media (max-width: 1199px) {
  .project-slider {
    margin-left: 20px;
    margin-right: 20px;
  }
}

.project-wrapper {
  z-index: 1;
  padding-bottom: 103px;
}

.project-wrapper .swiper {
  overflow: unset;
  position: unset;
}

.project-wrapper .swiper-pagination {
  bottom: 0;
  left: 50%;
  width: auto;
  padding: 3px 27px;
  display: inline-block;
  border-radius: 16px;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
  transform: translateX(-50%);
  border: 1px solid #d0dde3;
}

.project-wrapper .swiper-pagination .swiper-pagination-bullet {
  width: 9px;
  height: 9px;
  opacity: .2;
  background: var(--color-heading);
}

.project-wrapper .swiper-pagination .swiper-pagination-bullet:not(:last-child) {
  margin-right: 8px;
}

.project-wrapper .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
}

.project-slider .swiper-slide-active .work-btn .thm-btn--strock {
  background: var(--color-primary);
}

.project-slider .swiper-slide-active .work-btn .thm-btn--strock:hover {
  background: var(--color-heading);
}

.project-slider .swiper-slide-active .work-btn .thm-btn--strock .xb-item--icon {
  background: var(--color-white);
}

.project-slider .swiper-slide-active .work-btn .thm-btn--strock .xb-item--icon i {
  color: var(--color-heading);
}

.project-slider .swiper-slide-active .work-btn .thm-btn--strock .xb-item--text {
  color: var(--color-white);
}

.sa-project-item {
  width: 100%;
  height: auto;
  overflow: hidden;
  position: relative;
  padding: 80px 113px 80px 77px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .sa-project-item {
    padding: 80px 40px 80px;
  }
}

@media (max-width: 1199px) {
  .sa-project-item {
    padding: 80px 40px 80px;
  }
}

@media (max-width: 767px) {
  .sa-project-item {
    padding: 40px 25px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .sa-project-item .xb-item--inner {
    gap: 40px;
  }
}

@media (max-width: 1199px) {
  .sa-project-item .xb-item--inner {
    gap: 40px;
  }
}

.sa-project-item::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: linear-gradient(272deg, #0c111d 0%, rgba(12, 17, 29, 0.84) 24.69%, rgba(12, 17, 29, 0.48) 55.72%, rgba(12, 17, 29, 0.9) 82.18%, #0c111d 100%);
}

.sa-project-item .xb-item--project_title {
  max-width: 429px;
}

.sa-project-item .xb-item--title {
  font-weight: 700;
  font-size: 34px;
  line-height: 44px;
  margin-bottom: 15px;
  color: var(--color-white);
}

@media (max-width: 767px) {
  .sa-project-item .xb-item--title {
    font-size: 25px;
    line-height: 36px;
  }
}

.sa-project-item .xb-item--content {
  color: var(--color-white);
  line-height: 26px;
}

.sa-project-item .xb-item--item {
  padding-left: 30px;
  margin-top: 50px;
  max-width: 272px;
  position: relative;
}

.sa-project-item .xb-item--item::before {
  position: absolute;
  content: '';
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background: #ff8139;
}

.sa-project-item .xb-item--number {
  font-size: 40px;
  font-weight: 700;
  display: block;
  margin-bottom: 15px;
  color: var(--color-white);
  font-family: var(--font-heading);
  line-height: 1.2;
}

.sa-project-item .xb-item--text {
  display: block;
  font-size: 20px;
  letter-spacing: -0.02em;
  color: var(--color-white);
}

.sa-project-item .thm-btn span {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-project-item .thm-btn span svg {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-project-item .thm-btn::before {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-project-item .thm-btn:hover span {
  -webkit-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.sa-project-item .thm-btn:hover span svg {
  -webkit-transform: rotate(32deg);
  -ms-transform: rotate(32deg);
  transform: rotate(32deg);
}

.sa-project-item .thm-btn:hover::before {
  width: 0;
}

.sa-project-item .thm-btn span {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-left: 20px;
}

.sa-project-slider {
  margin-left: -55%;
  margin-right: -54%;
}

.sa-projecr_brand {
  border-bottom: 1px solid #F1F3F4;
}

.barnd-logo {
  position: relative;
}

.barnd-logo::before {
  position: absolute;
  content: '';
  bottom: 0;
  left: 0;
  height: 6px;
  width: 100%;
  background-color: #f1f3f4;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.barnd-logo .brand-items {
  z-index: 1;
  position: relative;
  display: inline-block;
  opacity: 0.5;
  cursor: pointer;
  padding-right: 110px;
}

@media (max-width: 1199px) {
  .barnd-logo .brand-items {
    padding-right: 70px;
  }
}

.barnd-logo .brand-items.is-active,
.barnd-logo .brand-items:hover {
  opacity: 1;
}

.barnd-logo .brand-items.is-active .xb-line,
.barnd-logo .brand-items:hover .xb-line {
  opacity: 1;
}

.barnd-logo .brand-items .xb-img {
  height: 38px;
  width: 100%;
}

.barnd-logo .brand-items .xb-line {
  position: relative;
  height: 6px;
  opacity: 1;
  margin-top: 30px;
  opacity: 0;
  background: #a8afb2;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.brand-slider-nav .swiper-slide-active .barnd-logo .brand-items {
  opacity: 1;
}

.brand-slider-nav .swiper-slide-active .barnd-logo .brand-items .xb-line {
  opacity: 1;
}

.seo-project-slider-inner {
  position: relative;
}

.seo-project-slider-inner::before,
.seo-project-slider-inner:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 301px;
  z-index: 1;
  height: 100%;
  content: "";
  background: linear-gradient(271deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
  opacity: .8;
}

@media (max-width: 991px) {

  .seo-project-slider-inner::before,
  .seo-project-slider-inner:after {
    display: none;
  }
}

.seo-project-slider-inner::after {
  left: auto;
  right: 0;
  background: linear-gradient(271deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
  transform: rotate(-180deg);
}

.seo-project-slider {
  margin-left: -40px;
}

@media (max-width: 767px) {
  .seo-project-slider {
    margin-left: -20px;
  }
}

.seo-project-slider .slick-track {
  display: flex;
  gap: 60px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .seo-project-slider .slick-track {
    gap: 30px;
  }
}

@media (max-width: 1199px) {
  .seo-project-slider .slick-track {
    gap: 30px;
  }
}

@media (max-width: 767px) {
  .seo-project-slider .slick-track {
    gap: 20px;
  }
}

.sa-brand-item {
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: .3;
  transition: .3s;
}

.slick-slide.slick-active.slick-current .sa-brand-item,
.sa-brand-item:hover {
  opacity: 1;
}

.slick-slide.slick-active.slick-current .sa-brand-item .xb-img::before,
.sa-brand-item:hover .xb-img::before {
  opacity: 1;
  right: auto;
  left: 0;
  width: 100%;
}

.sa-brand-item .xb-img {
  position: relative;
  padding-bottom: 30px;
}

.sa-brand-item .xb-img::before {
  position: absolute;
  bottom: -2px;
  right: 0;
  left: auto;
  width: 0%;
  height: 6px;
  background-color: #A8AFB2;
  content: "";
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.seo-project-slider-nav {
  position: relative;
}

.seo-project-slider-nav::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background-color: #F1F3F4;
  content: "";
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.process-left {
  position: sticky;
  top: 100px;
  z-index: 2;
  margin-bottom: 100px;
}

@media (max-width: 991px) {
  .process-left .xb-btn {
    margin-top: 40px;
  }
}

.process-left .thm-btn--fill_icon:hover .xb-item--holder {
  transform: translateX(-77%);
}

@media (max-width: 767px) {
  .process-right {
    float: none;
  }
}

.process-right .process-item {
  top: 140px;
  z-index: 2;
  position: sticky;
  max-width: 470px;
  padding: 0 40px 40px;
  margin-bottom: 100px;
  background: #eaeef0;
  border-radius: 16px;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
  box-shadow: 4px 4px 14px 0 #d5dbde;
}

@media (max-width: 991px) {
  .process-right .process-item {
    position: unset;
    margin-bottom: 40px;
  }
}

@media (max-width: 767px) {
  .process-right .process-item {
    margin-bottom: 50px;
    padding: 0 20px 40px;
  }
}

.process-right .process-item .xb-item--number {
  font-weight: 500;
  font-size: 30px;
  text-align: center;
  width: 54px;
  height: 57px;
  padding-top: 12px;
  border-top: none;
  display: inline-block;
  color: var(--color-heading);
  border-radius: 0 0 27px 27px;
  font-family: var(--font-heading);
  border: 1px solid rgba(17, 17, 18, 0.1);
}

.process-right .process-item .xb-item--title {
  font-size: 24px;
  letter-spacing: 0em;
  margin-bottom: 10px;
}

.process-right .process-item .xb-item--img {
  margin: 14px 0 25px;
}

.peocess-shape .shape {
  position: absolute;
  z-index: 1;
}

.peocess-shape .shape--one {
  left: 0;
  top: 0;
}

.peocess-shape .shape--two {
  top: 0;
  right: 0;
}

.sa-process_left .process-item {
  max-width: 450px;
  min-height: 236px;
  background: #fff;
  padding: 40px 30px;
  position: sticky;
  top: 150px;
  border: 1px solid #0f55dc;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  margin-bottom: 80px;
}

@media (max-width: 991px) {
  .sa-process_left .process-item {
    position: relative;
    margin-bottom: 0;
    top: 0;
    margin: 0 auto 30px;
  }
}

@media (max-width: 991px) {
  .sa-process_left .process-item:last-child {
    margin-bottom: 0;
  }
}

.sa-process_left .process-item .xb-item--icon {
  height: 41px;
  width: 41px;
}

.sa-process_left .process-item .xb-item--title {
  font-size: 28px;
  margin: 20px 0 20px;
}

.sa-process_left .process-item .xb-item--number {
  position: absolute;
  top: 30px;
  right: 30px;
  height: 48px;
  width: 48px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-heading-two);
  background-color: rgba(15, 83, 220, 0.1);
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  font-family: var(--font-heading);
  font-weight: 500;
}

.sa-process_left .process-item--two {
  top: 235px;
}

@media (max-width: 991px) {
  .sa-process_left .process-item--two {
    top: 0;
  }
}

.sa-process_left .process-item--three {
  top: 320px;
}

@media (max-width: 991px) {
  .sa-process_left .process-item--three {
    top: 0;
  }
}

.sa-process_left .process-item--four {
  top: 500px;
}

@media (max-width: 991px) {
  .sa-process_left .process-item--four {
    top: 0;
  }
}

.sa-process_right {
  display: flex;
  justify-content: flex-end;
  position: sticky;
  top: 200px;
}

@media (max-width: 991px) {
  .sa-process_right {
    max-width: 400px;
    margin: 0 auto;
  }
}

.sa-process_right .updown {
  animation: updown 3s linear infinite;
}

.sa-process_shape .shape {
  position: absolute;
  z-index: -1;
}

.sa-process_shape .shape--one {
  top: 0;
  left: 0;
}

.sa-process_shape .shape--two {
  top: 0;
  right: 0;
}

.sa-process_shape .shape--three {
  top: 60px;
  left: 43px;
}

.process-bg {
  background: linear-gradient(360deg, #f6f6f8 0%, #fff 100%);
}

.cp-process-right .xb-img {
  float: right;
  margin-right: -145px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .cp-process-right .xb-img {
    float: none;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .cp-process-right .xb-img {
    float: none;
    margin-right: 0;
  }
}

.sa-process_left {
  z-index: 2;
  position: relative;
}

.process_shape {
  position: absolute;
  top: -268px;
  left: -920px;
  width: 973px;
  height: 920px;
  z-index: -1;
}

.service-warpper {
  opacity: 0;
  position: relative;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.service-bg_img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.outer {
  width: 100%;
  height: 100dvh;
  transition: all 1s ease;
  position: relative;
  opacity: 0;
}

.outer .bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.outer .bg-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 1s ease;
}

.outer .box {
  width: 840px;
  height: 530px;
  border-radius: 20px;
  background: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 35px;
  display: flex;
  justify-content: space-between;
}

.outer .menu-wrap {
  width: 50%;
}

.outer .menu {
  width: 100%;
  position: relative;
  padding-right: 35px;
}

.outer .title {
  font-size: 38px;
  font-weight: normal;
  margin-bottom: 30px;
}

.outer .hover-bg {
  width: 91%;
  height: 80px;
  position: absolute;
  top: 0px;
  left: 0;
  background: #f3f3f3;
  z-index: -1;
  transition: all 0.7s cubic-bezier(0.28, 0.42, 0.36, 1.15);
  border-radius: 5px;
}

.outer .item {
  position: relative;
  width: 100%;
  display: block;
}

.outer .hovered {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.outer .inner {
  width: 100%;
  display: flex;
  padding: 10px;
  align-items: center;
  gap: 20px;
  height: 80px;
  cursor: pointer;
  transition: all 1s ease;
}

.outer .icon {
  width: 60px;
  height: 60px;
  background: #F7F0EA;
  display: flex;
  justify-content: center;
  border-radius: 5px;
  align-items: center;
  transition: all 1s ease;
}

.outer .icon>i {
  color: #362323;
  transition: all 1s ease;
}

.outer .active {
  animation: fadeIn 1s ease;
}

.outer .item.active .icon {
  background: #362323;
  transition: all 1s ease;
}

.outer .item.active .icon>i {
  color: #F7F0EA;
}

.outer .image {
  width: 50%;
  border-radius: 10px;
}

.outer .image-holder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  transition: all 1s ease;
}

@keyframes fadeIn {
  0% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

.service-box {
  padding: 10px;
  min-height: 323px;
  background: #f6f6f8;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e7e8ec;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
}

.service-box:not(:last-child) {
  margin-bottom: 30px;
}

.service-box::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  z-index: 1;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: linear-gradient(122deg, rgba(255, 217, 17, 0) 0%, rgba(233, 226, 255, 0.8) 34.54%, #cae1f7 62.5%, rgba(250, 232, 138, 0.56) 100%), radial-gradient(74.51% 50% at 50% 100%, white 24.0899994969%, white 100%);
  background: url(../images/bg/service_bg.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.service-box:hover::before {
  opacity: 1;
}

.service-box:hover .service-item .xb-item--arrow img {
  transform: rotate(45deg);
}

.service-box:hover .service-item .xb-item--arrow::before {
  opacity: 1;
  transform: scale(1);
}

.service-box .service-item {
  position: relative;
  z-index: 2;
  padding: 30px;
  background: #fff;
  border: 1px solid #e6e7ec;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.service-box .service-item .xb-item--title {
  font-size: 28px;
  letter-spacing: 0em;
  margin-bottom: 10px;
}

.service-box .service-item .xb-item--contact {
  line-height: 26px;
}

.service-box .service-item .xb-item--icon {
  align-items: center;
}

.service-box .service-item .xb-item--img {
  margin-left: -10px;
}

.service-box .service-item .xb-item--img img {
  height: 82px;
}

.service-box .service-item .xb-item--arrow {
  height: 45px;
  width: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border: 1px solid #e6e7ec;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.service-box .service-item .xb-item--arrow::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  opacity: 0;
  z-index: -1;
  transform: scale(0.2);
  background: linear-gradient(122deg, rgba(255, 217, 17, 0) 0%, rgba(233, 226, 255, 0.8) 34.54%, #cae1f7 62.5%, rgba(250, 232, 138, 0.56) 100%), radial-gradient(74.51% 50% at 50% 100%, white 24.0899994969%, white 100%);
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.service-box .service-item .xb-item--arrow img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.service-box .xb-overlay {
  z-index: 2;
}

.service-info {
  position: sticky;
  top: 100px;
}

.cs-service-item {
  z-index: 2;
  position: relative;
  padding: 40px 30px;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: #010315;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.cs-service-item .border-bg1,
.cs-service-item .border-bg2 {
  position: absolute;
  content: '';
  z-index: -1;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.cs-service-item .border-bg1 svg,
.cs-service-item .border-bg2 svg {
  width: 100%;
  height: 100%;
}

.cs-service-item .border-bg2 {
  opacity: 0;
}

.cs-service-item .xb-item--icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cs-service-item .xb-item--icon img {
  -webkit-transition: 0.8s;
  -o-transition: 0.8s;
  transition: 0.8s;
}

.cs-service-item .xb-item--icon::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: linear-gradient(180deg, #3a3645 0%, #010315 100%);
}

.cs-service-item .xb-item--inner {
  z-index: 1;
  position: relative;
}

.cs-service-item .xb-item--title {
  margin: 25px 0 20px;
}

.cs-service-item .xb-item--item li {
  color: #a9a4c0;
}

.cs-service-item .xb-item--item li:not(:last-child) {
  margin-bottom: 8px;
}

.cs-service-item .xb-item--item li img {
  margin-right: 10px;
  transform: translateY(-2px);
}

.cs-service-item:hover .border-bg1 {
  opacity: 0;
}

.cs-service-item:hover .border-bg2 {
  opacity: 1;
}

.cs-service-item:hover .xb-item--icon::before {
  background: linear-gradient(179deg, #431dab 0%, #010315 100%);
}

.cs-service-item:hover .xb-item--icon img {
  transform: rotateY(360deg);
}

.cs-ser_shape {
  position: absolute;
  left: 49%;
  bottom: -74px;
  transform: translateX(-50%);
}

.cd-service-item {
  position: relative;
  min-height: 364px;
  margin-top: 24px;
  padding: 61px 50px 50px;
  border: 1px solid #e8e8e8;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 1199px) {
  .cd-service-item {
    padding: 61px 28px 50px;
  }
}

@media (max-width: 991px) {
  .cd-service-item {
    min-height: 330px;
  }
}

.cd-service-item .xb-item--icon {
  height: 60px;
  width: 60px;
}

.cd-service-item .xb-item--title {
  font-size: 24px;
  line-height: 40px;
  margin: 42px 0 44px;
  letter-spacing: -0.01em;
}

@media (max-width: 991px) {
  .cd-service-item .xb-item--title {
    font-size: 18px;
    line-height: 30px;
  }
}

.cd-service-item .xb-item--dot span {
  height: 12px;
  width: 12px;
  opacity: 0.5;
  display: inline-block;
  background: #99e1d9;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.cd-service-item .xb-item--dot span:not(:last-child) {
  margin-right: 8px;
}

.cd-service-item:hover {
  box-shadow: 0 44px 38px 0 rgba(102, 80, 65, 0.12);
}

.cd-service-item:hover .xb-item--dot span {
  opacity: 1;
}

.cd-dot_bg2 span {
  background: var(--color-primary-four) !important;
}

.cd-dot_bg3 span {
  background: #e4adf2 !important;
}

.cd-dot_bg4 span {
  background: #b2e1ff !important;
}

.da-service-wrapper {
  gap: 24px;
  display: flex;
  justify-content: space-between;
}

@media (max-width: 991px) {
  .da-service-wrapper {
    flex-wrap: wrap;
    gap: 0;
  }
}

.da-service-item {
  display: inline-block;
  position: relative;
  margin-top: 30px;
  padding: 68px 50px 61px 35px;
  border-radius: 16px;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
  backdrop-filter: blur(10px);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  display: flex;
  position: relative;
  overflow: hidden;
  width: 32%;
  background: linear-gradient(172deg, #f2d8d4 0%, #fff 100%);
}

@media (max-width: 991px) {
  .da-service-item {
    width: 48%;
  }
}

@media (max-width: 1199px) {
  .da-service-item {
    padding: 50px 20px;
  }
}

@media (max-width: 767px) {
  .da-service-item {
    width: 100%;
  }
}

.da-service-item:nth-child(2) {
  background: linear-gradient(164deg, #9fb4ff 0%, #fff 100%);
}

.da-service-item:nth-child(3) {
  background: linear-gradient(172deg, #d4f2df 0%, #fff 100%);
}

.da-service-item:nth-child(4) {
  background: linear-gradient(159deg, #aff1ff 0%, #fff 100%);
}

.da-service-item .xb-item--icon {
  min-height: 66px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.da-service-item .xb-item--title {
  font-size: 28px;
  line-height: 38px;
  margin: 48px 0 29px;
  width: 237px;
  color: var(--color-heading-four);
  max-width: 225px;
}

@media (max-width: 1199px) {
  .da-service-item .xb-item--title {
    font-size: 23px;
    line-height: 33px;
    margin: 20px 0;
  }
}

.da-service-item .xb-item--arrow {
  position: absolute;
  left: 35px;
  bottom: 61px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.da-service-item .xb-item--arrow i {
  color: #1438bc;
  font-size: 30px;
  transform: rotate(-45deg);
}

.da-service-item .xb-item--content {
  font-weight: 500;
  line-height: 30px;
  opacity: 0;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
  transform: translateY(20px);
}

.da-service-item.active {
  align-items: flex-start;
  width: 45%;
}

@media (max-width: 991px) {
  .da-service-item.active {
    width: 48%;
  }
}

@media (max-width: 767px) {
  .da-service-item.active {
    width: 100%;
  }
}

.da-service-item.active .xb-item--arrow {
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
}

.da-service-item.active .xb-item--content {
  opacity: 1;
  transition-delay: .3s;
  transform: translateY(0px);
}

.sd-ser-content {
  padding-right: 75px;
}

@media (max-width: 991px) {
  .sd-ser-content {
    padding-right: 0;
  }
}

.sd-ser-content .sd-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 30px;
}

@media (max-width: 991px) {
  .sd-ser-content .sd-title {
    font-size: 36px;
  }
}

@media (max-width: 767px) {
  .sd-ser-content .sd-title {
    font-size: 28px;
  }
}

.sd-ser-content .sd-content {
  line-height: 28px;
}

.sd-heading .sd-title {
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 35px;
}

@media (max-width: 991px) {
  .sd-heading .sd-title {
    font-size: 34px;
  }
}

@media (max-width: 767px) {
  .sd-heading .sd-title {
    font-size: 26px;
  }
}

.sd-heading .sd-content {
  padding-right: 90px;
}

@media (max-width: 991px) {
  .sd-heading .sd-content {
    padding-right: 0;
  }
}

.similar-casestudy-title {
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 35px;
}

@media (max-width: 991px) {
  .similar-casestudy-title {
    font-size: 34px;
  }
}

@media (max-width: 767px) {
  .similar-casestudy-title {
    font-size: 26px;
  }
}

.sd-process-item {
  z-index: 1;
  position: relative;
  max-width: 308px;
  padding-left: 60px;
  margin-right: 7px;
  margin-top: 30px;
}

.sd-process-item .xb-item--title {
  font-size: 22px;
  margin-top: 37px;
  margin-bottom: 20px;
}

.sd-process-item .xb-item--number {
  height: 39px;
  width: 39px;
  font-size: 18px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 7px;
  left: 0;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  color: var(--color-heading-two);
  background: var(--color-white);
  font-family: var(--font-heading);
  box-shadow: 0 2px 6px 0 #e0dde7;
}

.sd-process_inner {
  margin-top: 63px;
  padding: 10px 0 50px;
  position: relative;
}

.sd-process_inner .sd-shape {
  position: absolute;
  top: 0;
  left: 12px;
}

@media (max-width: 1199px) {
  .sd-process_inner .sd-shape {
    display: none;
  }
}

.sd-ser-list {
  margin-top: 40px;
}

.sd-ser-list:not(:last-child) {
  margin-right: 250px;
}

@media (max-width: 991px) {
  .sd-ser-list:not(:last-child) {
    margin-right: 30px;
  }
}

.sd-ser-list li {
  font-weight: 500;
}

.sd-ser-list li:not(:last-child) {
  margin-bottom: 25px;
}

.sd-ser-list li img {
  margin-right: 15px;
}

.csd-ser_inner {
  padding-top: 25px;
  border-top: 1px solid #e4eeef;
}

.csd-ser_inner .csd-item .xb-icon {
  margin-right: 8px;
}

.csd-ser_inner .csd-item .xb-text {
  font-weight: 700;
  display: inline-block;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.csd-ser_inner .csd-item .xb-text span {
  font-weight: 500;
  font-size: 18px;
  color: #494d57;
  text-transform: lowercase;
}

.csd-heading .sd-title {
  margin-bottom: 50px;
}

.csd-heading .sd-content span {
  font-weight: 600;
  color: var(--color-heading-two);
}

.csd-item {
  margin-top: 25px;
}

.csd-item .xb-img {
  margin-top: 30px;
  overflow: hidden;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.service-height {
  min-height: 920px;
}

@media (max-width: 991px) {
  .service-height {
    min-height: 700px;
  }
}

.service-image-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: .5s;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.service-image-item::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #111112;
  content: "";
  opacity: 0.1;
}

.service-image-item.active {
  opacity: 1;
  z-index: 2;
}

.service-content-box {
  max-width: 910px;
  width: 100%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  padding: 30px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  z-index: 3;
}

@media (max-width: 767px) {
  .service-content-box {
    padding: 30px 10px;
  }
}

.service-content-list,
.service-content-image {
  width: 50%;
}

@media (max-width: 767px) {

  .service-content-list,
  .service-content-image {
    width: 100%;
  }
}

.service-content-image .xb-item--img {
  position: absolute;
  top: 0;
  right: 30px;
  width: 43%;
  height: calc(100% - 60px);
  bottom: 0;
  margin: auto;
  border-radius: 10px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: .5s;
}

@media (max-width: 767px) {
  .service-content-image .xb-item--img {
    position: unset;
  }
}

.service-content-image .xb-item--img.active {
  opacity: 1;
}

.service-content-list {
  padding-top: 10px;
  padding-bottom: 10px;
}

.service-content-list .title {
  font-size: 42px;
  margin-bottom: 30px;
}

.service-list-item {
  padding: 11px 12px;
  padding-right: 20px;
  cursor: pointer;
}

.service-list-item:not(:last-child) {
  margin-bottom: 3px;
}

.service-list-item .xb-item--icon {
  width: 63px;
  height: 63px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  background: linear-gradient(180deg, #ffe6d9 0%, #fff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

@media (max-width: 991px) {
  .service-list-item .xb-item--icon {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }
}

.service-list-item .xb-item--title {
  font-weight: 500;
  font-size: 20px;
  line-height: 160%;
  letter-spacing: 0em;
  color: #111112;
  margin-right: auto;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.service-list-item .xb-item--arrow {
  width: 30px;
  height: 30px;
  background-color: #EAEEF0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  transition: .3s;
}

.service-list-item .xb-item--arrow svg path {
  transition: .3s;
}

.service-list-item.current .xb-item--title {
  color: var(--color-white);
}

.service-list-item.current .xb-item--arrow {
  background-color: var(--color-primary);
}

.service-list-item.current .xb-item--arrow svg path {
  fill: var(--color-white);
}

.service-list {
  position: relative;
}

.service-list .active-bg {
  top: -1px;
  bottom: 0px;
  left: -1px;
  right: 0px;
  position: absolute;
  background: var(--color-primary);
  transition: all 0.5s ease;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  z-index: -1;
}

.feature-item {
  margin-top: 20px;
  align-items: flex-start;
}

.feature-item .xb-item--icon {
  height: 74px;
  width: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: linear-gradient(180deg, #ffe6d9 0%, #fff 100%);
}

.feature-item .xb-item--holder {
  width: calc(100% - 104px);
}

.feature-item .xb-item--title {
  margin-bottom: 10px;
}

@media (max-width: 1199px) {
  .feature-item .xb-item--title {
    font-size: 18px;
  }
}

.feature-item .xb-item--content {
  color: #49515b;
}

.feature-table tr {
  border-bottom: 1px solid rgba(231, 232, 236, 0.4);
}

.feature-table tr:first-child td:first-child {
  border-top-left-radius: 20px;
}

.feature-table tr:first-child td:last-child {
  border-top-right-radius: 20px;
}

.feature-table tr:last-child td:first-child {
  border-bottom-left-radius: 20px;
}

.feature-table tr:last-child td:last-child {
  border-bottom-right-radius: 20px;
}

.feature-table tr th {
  font-size: 28px;
  font-weight: 500;
  padding: 40px 0;
  color: var(--color-heading-two);
  font-family: var(--font-heading);
}

@media (max-width: 1199px) {
  .feature-table tr th {
    font-size: 22px;
  }
}

@media (max-width: 767px) {
  .feature-table tr th {
    font-size: 18px;
  }
}

.feature-table tr th:nth-child(2) {
  border-radius: 20px 20px 0 0;
  background-color: var(--color-primary-two);
}

.feature-table tr td {
  background: #fff;
  font-size: 20px;
  font-weight: 500;
  padding: 25px 0;
  letter-spacing: -0.01em;
  color: var(--color-heading-two);
}

@media (max-width: 991px) {
  .feature-table tr td {
    font-size: 18px;
  }
}

.feature-table tr td:nth-child(1),
.feature-table tr th:nth-child(1) {
  width: 46%;
  padding-left: 60px;
}

@media (max-width: 991px) {

  .feature-table tr td:nth-child(1),
  .feature-table tr th:nth-child(1) {
    padding-left: 30px;
    padding-right: 30px;
  }
}

.feature-table tr td:nth-child(2),
.feature-table tr th:nth-child(2) {
  width: 28%;
  text-align: center;
  background-color: var(--color-primary-two);
}

.feature-table tr td:nth-child(3),
.feature-table tr th:nth-child(3) {
  width: 28%;
  text-align: center;
}

.feature-table .table-body {
  border: 1px solid #e7e8ec;
  box-shadow: 0 4px 13px 0 rgba(119, 152, 215, 0.19);
}

.cs-feature-item {
  display: inline-block;
  margin-top: 30px;
}

.cs-feature-item:not(:last-child) {
  margin-right: 82px;
}

.cs-feature-item .xb-item--icon {
  min-height: 70px;
}

.cs-feature-item .xb-item--title {
  font-size: 20px;
  margin: 20px 0 15px;
}

.hd-feature-item {
  height: auto;
  width: 332px;
  z-index: 1;
  padding: 30px;
  cursor: pointer;
  padding-top: 22px;
  position: relative;
  background: #000;
  display: inline-block;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.hd-feature-item::before {
  position: absolute;
  content: '';
  left: 0;
  bottom: 3px;
  height: 100%;
  width: 100%;
  z-index: -1;
  border-radius: inherit;
  background: var(--color-white);
}

.hd-feature-item .xb-item--title {
  font-size: 22px;
  margin-bottom: 10px;
}

.hd-feature-item .xb-item--content {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.hd-feature-item .xb-item--meta {
  margin-top: 15px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.hd-feature-item .xb-item--meta li span {
  display: block;
  font-weight: 700;
  margin-top: 10px;
  color: var(--color-heading-three);
}

.hd-feature-item:hover {
  transform: scale(1.2);
}

.hd-feature-button {
  padding: 6px;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
  border-radius: 64px;
  -webkit-border-radius: 64px;
  -moz-border-radius: 64px;
  -ms-border-radius: 64px;
  -o-border-radius: 64px;
  background-color: var(--color-white);
  display: inline-block;
}

.hd-feature-button span {
  padding: 26px 50px 45px 73px;
  display: inline-block;
  border-radius: inherit;
  background: #d4f479;
}

@media (max-width: 1199px) {
  .hd-feature-button span {
    padding: 20px 30px;
  }
}

.hd-feature-button:hover {
  transform: scale(1.1);
}


.help_desk {
  overflow: hidden;
}
.xb-feature-wrap {
  z-index: 1;
}

.xb-feature-wrap::before {
  position: absolute;
  bottom: -13%;
  left: 0;
  right: 0;
  width: 100%;
  height: 400px;
  content: "";
  background-image: url(../images/shape/feature_blur.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.xb-feature-wrap .xb-shape {
  position: absolute;
  top: 86%;
  left: 50%;
  width: 466px;
  height: 518px;
  z-index: -1;
  transform: translate(-50%, -50%);
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border: 1px solid rgba(216, 215, 199, 0.24);
  border-bottom: none;
  background: linear-gradient(180deg, rgba(214, 222, 191, 0.54) 0%, #f6f0e6 100%);
}

.xb-feature-wrap .xb-shape::before,
.xb-feature-wrap .xb-shape::after {
  position: absolute;
  content: '';
  height: 100%;
  width: 100%;
  border-radius: inherit;
  animation: push-scale-one 1500ms ease-out infinite;
  background: linear-gradient(180deg, rgba(223, 231, 203, 0.15) 0%, #f6f0e6 100%);
}

.xb-feature-wrap .xb-shape::after {
  animation: push-scale-two 1500ms ease-out infinite;
  background: linear-gradient(178deg, rgba(214, 222, 191, 0.13) 0%, #f6f0e6 100%);
}

.xb-feature-wrap .xb-shape span {
  position: absolute;
  height: 50%;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 1;
  background: #F6F0E6;
}

.hd-feature--top {
  margin-bottom: 60px;
  display: flex;
  justify-content: center;
}

@media (max-width: 991px) {
  .hd-feature--top {
    margin-bottom: 30px;
  }
}

.hd-feature--top .xb-item--meta li:not(:last-child) {
  margin-right: 15px;
}

.hd-feature--middle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 60px;
}

@media (max-width: 991px) {
  .hd-feature--middle {
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 30px;
  }
}

.hd-feature--middle .xb-item--title {
  margin-top: 15px;
}

.hd-feature--bottom {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hd-feature--bottom .xb-item--meta {
  display: flex;
  justify-content: space-between;
}

.hd-feature--bottom .xb-item--meta li {
  margin-right: 0;
}

.hd-button-box .xb-arrow .arrow {
  position: absolute;
}

.hd-button-box .xb-arrow .arrow--one {
  top: -60px;
  left: 50%;
  transform: rotate(90deg);
}

.hd-button-box .xb-arrow .arrow--two {
  top: 50%;
  right: -70px;
  transform: translateY(-50%);
}

.hd-button-box .xb-arrow .arrow--three {
  left: 50%;
  bottom: -60px;
  transform: rotate(-90deg);
}

.hd-button-box .xb-arrow .arrow--four {
  top: 50%;
  left: -70px;
  transform: translateY(-50%);
}

.hd-button-box .xb-arrow .arrow--five {
  top: -24px;
  right: -29px;
}

.feature-left-item .xb-img-two {
  position: absolute;
  left: -40px;
  top: 64px;
}

@media (max-width: 1199px) {
  .feature-left-item .xb-img-two {
    left: -10px;
  }
}

.feature-left-item .xb-img-two img {
  animation: updown-3 5s linear infinite;
}

.feature  {
  overflow: hidden;
}

.cd-feature .title {
  font-size: 100px;
  line-height: 150px;
  letter-spacing: -0.02em;
  text-align: center;
  text-transform: uppercase;
}

@media (max-width: 1199px) {
  .cd-feature .title {
    font-size: 60px;
    line-height: 85px;
  }
}

@media (max-width: 767px) {
  .cd-feature .title {
    font-size: 32px;
    line-height: 50px;
  }
}

.cd-feature .xb-shape .shape {
  position: absolute;
}

.cd-feature .xb-shape .shape--one {
  top: 46%;
  left: 19%;
}

.cd-feature .xb-shape .shape--two {
  top: 14.5%;
  right: 16%;
}

@media (max-width: 767px) {
  .cd-feature .xb-shape .shape--two {
    top: -15.5%;
    right: 6%;
  }
}

.xb-btn-wrapper {
  display: inline-block;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.xb-hover-btn {
  position: relative;
  overflow: hidden;
  padding: 10px 20px;
  font-size: 16px;
  color: #fff;
  background: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
}

.xb-btn-circle-dot {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  transition: top 0.2s ease, left 0.2s ease;
}


.da-feature-wrap {
  background: #D5DCF7;
  border-top: 1px solid #D5DCF7;
  border-bottom: 1px solid #D5DCF7;
}

.da-feature-item {
  background: #f4f5fc;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  padding: 68px 100px 40px 46px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .da-feature-item {
    padding: 60px 40px;
  }
}

@media (max-width: 1199px) {
  .da-feature-item {
    padding: 40px 20px;
  }
}

@media (max-width: 991px) {
  .da-feature-item {
    padding: 40px 50px;
    height: 100%;
  }
}

.da-feature-item:hover {
  background: #fff;
}

.da-feature-item .xb-item--icon {
  min-height: 70px;
  display: inline-flex;
}

.da-feature-item .xb-item--title {
  font-size: 38px;
  font-weight: 400;
  line-height: 50px;
  margin: 35px 0 28px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .da-feature-item .xb-item--title {
    font-size: 28px;
    margin: 0 0 10px;
  }
}

@media (max-width: 1199px) {
  .da-feature-item .xb-item--title {
    font-size: 22px;
    line-height: 36px;
    margin: 10px 0 0;
  }
}

.da-feature-item .xb-item--content {
  font-weight: 400;
  font-size: 16px;
  line-height: 187%;
  color: #212877;
}

.fea-col:nth-child(1) .xb-item--holder {
  padding-left: 215px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .fea-col:nth-child(1) .xb-item--holder {
    padding-left: 0;
  }
}

@media (max-width: 1199px) {
  .fea-col:nth-child(1) .xb-item--holder {
    padding-left: 0;
  }
}

.fea-col:nth-child(1) .da-feature-item {
  margin-right: 66px;
  border-radius: 0 10px 10px 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .fea-col:nth-child(1) .da-feature-item {
    padding-right: 100px;
  }
}

@media (max-width: 1199px) {
  .fea-col:nth-child(1) .da-feature-item {
    padding-right: 60px;
  }
}

@media (max-width: 991px) {
  .fea-col:nth-child(1) .da-feature-item {
    margin: 0;
    padding: 50px;
  }
}

.fea-col:nth-child(2) .da-feature-item {
  margin-right: 1px;
  margin-left: -65px;
}

@media (max-width: 991px) {
  .fea-col:nth-child(2) .da-feature-item {
    margin-left: 0;
  }
}

.fea-col:nth-child(3) .da-feature-item {
  margin-right: -65px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .fea-col:nth-child(3) .da-feature-item {
    margin-right: -90px;
  }
}

@media (max-width: 991px) {
  .fea-col:nth-child(3) .da-feature-item {
    margin-right: 0;
  }
}

.fea-col:nth-child(4) .xb-item--holder {
  padding-right: 215px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .fea-col:nth-child(4) .xb-item--holder {
    padding-right: 0;
  }
}

@media (max-width: 1199px) {
  .fea-col:nth-child(4) .xb-item--holder {
    padding-right: 0;
  }
}

.fea-col:nth-child(4) .da-feature-item {
  margin-left: 66px;
  border-radius: 10px 0 0 10px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .fea-col:nth-child(4) .da-feature-item {
    margin-left: 91px;
  }
}

@media (max-width: 1199px) {
  .fea-col:nth-child(4) .da-feature-item {
    padding-left: 50px;
  }
}

@media (max-width: 991px) {
  .fea-col:nth-child(4) .da-feature-item {
    margin-left: 0;
  }
}

.ap-feature-item {
  max-width: 244px;
}

.ap-feature-item .xb-item--icon {
  min-height: 57px;
}

.ap-feature-item .xb-item--title {
  font-weight: 700;
  margin: 28px 0 15px;
  letter-spacing: 0em;
  text-transform: capitalize;
  color: var(--color-heading-two);
}

.ap-feature-item .xb-item--content {
  line-height: 26px;
  color: #494d57;
}

.ap-fea-item {
  overflow: hidden;
  display: inline-block;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.ap-fea-item::before {
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  height: 100%;
  width: 100%;
  z-index: 1;
  border-radius: inherit;
  background: linear-gradient(180deg, rgba(12, 17, 29, 0) 0%, rgba(12, 17, 29, 0.69) 63.19%, #0c111d 100%);
}

.ap-fea-item .xb-item--img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.ap-fea-item .xb-item--content {
  position: absolute;
  z-index: 2;
  left: 5px;
  right: 20px;
  bottom: 49px;
  font-size: 28px;
  line-height: 38px;
  text-align: center;
  letter-spacing: 0em;
  color: var(--color-white);
  padding: 0 15px;
}

.ap-fea-item:hover .xb-item--img {
  transform: scale(1.1);
}

.testimonial-item {
  padding: 50px;
  background: #29292a;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  border: 1px solid #353537;
}

@media (max-width: 767px) {
  .testimonial-item {
    padding: 50px 20px;
  }
}

.testimonial-item .xb-item--holder {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

@media (max-width: 767px) {
  .testimonial-item .xb-item--holder {
    flex-wrap: wrap;
  }
}

.testimonial-item .xb-item--author {
  max-width: 200px;
  margin-right: 44px;
  position: relative;
}

.testimonial-item .xb-item--author .img {
  height: 64px;
  width: 64px;
  border: 1px solid #353537;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.testimonial-item .xb-item--author .img--one {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
  background-color: var(--color-heading);
}

.testimonial-item .xb-item--author .img--two {
  position: absolute;
  top: 0;
  left: 50px;
}

.testimonial-item .xb-item--name {
  font-size: 20px;
  margin-bottom: 7px;
  letter-spacing: 0em;
  color: var(--color-white);
  text-transform: capitalize;
}

.testimonial-item .xb-item--desig {
  color: var(--color-white);
}

.testimonial-item .xb-item--rating {
  padding: 5px 7px;
  font-weight: 600;
  color: var(--color-white);
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background-color: var(--color-heading);
}

.testimonial-item .xb-item--rating img {
  margin-right: 5px;
}

.testimonial-item .xb-item--content {
  width: calc(100% - 249px);
  display: inline-block;
  font-size: 24px;
  line-height: 34px;
  color: var(--color-white);
  transform: translateY(-8px);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .testimonial-item .xb-item--content {
    width: calc(100% - 244px);
  }
}

@media (max-width: 1199px) {
  .testimonial-item .xb-item--content {
    font-size: 18px;
    line-height: 30px;
  }
}

@media (max-width: 991px) {
  .testimonial-item .xb-item--content {
    font-size: 20px;
    line-height: 30px;
  }
}

@media (max-width: 767px) {
  .testimonial-item .xb-item--content {
    width: 100%;
    margin-top: 30px;
  }
}

.testimonial-item .xb-item--bottom {
  margin-top: 48px;
}

.testimonial-slider {
  margin-right: -365px;
  margin-left: -365px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .testimonial-slider {
    margin-right: -200px;
    margin-left: -200px;
  }
}

@media (max-width: 767px) {
  .testimonial-slider {
    margin-right: 0;
    margin-left: 0;
    padding: 0 15px;
  }
}

.testimonial-pagination {
  position: relative;
  z-index: 2;
  bottom: -50px;
  left: 50%;
  width: 870px;
  transform: translateX(-50%);
}

.testimonial-pagination .swiper-pagination {
  position: absolute;
  left: 50%;
  bottom: -50px;
  color: #fff;
  font-size: 18px;
  padding: 23px 0;
  max-width: 730px;
  text-align: center;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: #29292a;
  border: 1px solid #353537;
  transform: translateX(-50%);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .testimonial-pagination .swiper-pagination {
    max-width: 655px;
  }
}

@media (max-width: 991px) {
  .testimonial-pagination .swiper-pagination {
    max-width: 600px;
  }
}

@media (max-width: 767px) {
  .testimonial-pagination .swiper-pagination {
    max-width: 250px;
    bottom: -40px;
    padding: 18px 0;
  }
}

.testimonial-pagination .swiper-pagination .swiper-pagination-current::before,
.testimonial-pagination .swiper-pagination .swiper-pagination-total::before {
  content: '0';
}

.testimonial-pagination .swiper-buttons {
  font-size: 30px;
  font-weight: 400;
  line-height: 34px;
  color: #fff;
  padding: 34px 24px;
  background: #29292a;
  border: 1px solid #353537;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media (max-width: 767px) {
  .testimonial-pagination .swiper-buttons {
    font-size: 20px;
    padding: 30px 24px;
  }
}

.testimonial-pagination .swiper-button-prev {
  left: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .testimonial-pagination .swiper-button-prev {
    left: 42px;
  }
}

@media (max-width: 991px) {
  .testimonial-pagination .swiper-button-prev {
    left: 70px;
  }
}

@media (max-width: 991px) {
  .testimonial-pagination .swiper-button-prev {
    left: 250px;
  }
}

.testimonial-pagination .swiper-button-next {
  right: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .testimonial-pagination .swiper-button-next {
    right: 42px;
  }
}

@media (max-width: 991px) {
  .testimonial-pagination .swiper-button-next {
    right: 70px;
  }
}

@media (max-width: 991px) {
  .testimonial-pagination .swiper-button-next {
    right: 250px;
  }
}

.testimonial-pagination .swiper-button-next::after,
.testimonial-pagination .swiper-button-prev::after {
  display: none;
}

.tes-heading .title {
  color: #fff;
  font-size: 48px;
  letter-spacing: -0.02em;
  color: var(--color-white);
}

@media (max-width: 991px) {
  .tes-heading .title {
    font-size: 40px;
  }
}

@media (max-width: 767px) {
  .tes-heading .title {
    font-size: 32px;
  }
}

@media (max-width: 1199px) {
  .sa-testimonial-slider-inner {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.sa-testimonial-slider .swiper-wrapper {
  padding-top: 10px;
}

.sa-testimonial-slider {
  margin-left: -125px;
  margin-right: -125px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .sa-testimonial-slider {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 1199px) {
  .sa-testimonial-slider {
    margin-left: 0;
    margin-right: 0;
  }
}

.sa-testimonial-slider .swiper-slide-active .sa-testimonial-item::before {
  opacity: 1;
}

.sa-testimonial-item {
  padding: 10px;
  background: #f6f6f8;
  border: 1px solid #e7e8ec;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-testimonial-item:before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: linear-gradient(122deg, rgba(255, 217, 17, 0) 0%, rgba(233, 226, 255, 0.8) 34.54%, #cae1f7 62.5%, rgba(250, 232, 138, 0.56) 100%), radial-gradient(74.51% 50% at 50% 100%, white 24.0899994969%, white 100%);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: url(../images/bg/tm_bg.png) center center/cover no-repeat;
}

.sa-testimonial-item:hover::before {
  opacity: 1;
}

.sa-testimonial-item .xb-item--inner {
  padding: 30px;
  border: 1px solid #e7e8ec;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-white);
  z-index: 2;
  position: relative;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.sa-testimonial-item .xb-item--content {
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 20px;
  line-height: 36px;
  letter-spacing: -0.01em;
  color: var(--color-heading);
  margin-top: 40px;
  display: inline-block;
}

.sa-testimonial-item .xb-item--holder {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 1px solid #e7e8ec;
  gap: 10px 18px;
}

.sa-testimonial-item .xb-item--avatar {
  width: 90px;
  height: 90px;
  border: 3px solid #fff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  box-shadow: 0 4px 13px 0 rgba(119, 152, 215, 0.19);
}

.sa-testimonial-item .xb-item--name {
  font-size: 22px;
  letter-spacing: 0em;
}

.sa-testimonial-item .xb-item--desig {
  margin: 5px 0;
  display: inline-block;
}

.sa-testimonial-item:hover {
  transform: scale(1.03);
}

.sa-tes_button .sa-swiper-btn {
  position: absolute;
  right: 0;
  left: auto;
  top: 50px;
  font-size: 30px;
  width: 36px;
  height: 43px;
  z-index: 1;
  color: var(--color-black);
  border: 1px solid #e7e8ec;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background: #f6f6f8;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-tes_button .sa-swiper-btn.swiper-button-prev {
  right: 42px;
}

@media (max-width: 767px) {
  .sa-tes_button .sa-swiper-btn {
    position: relative;
    right: 0 !important;
    display: inline-flex;
    margin-top: 0;
    top: 20px;
  }
}

.sa-tes_button .sa-swiper-btn i {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-tes_button .sa-swiper-btn::after,
.sa-tes_button .sa-swiper-btn::before {
  display: none;
}

.sa-tes_button .sa-swiper-btn:hover {
  background: var(--color-primary-two);
}

.sa-tes_button .sa-swiper-btn:hover i {
  color: #fff;
}

.cs-brand-logo {
  margin: 0;
  margin-top: 70px;
  margin-right: -110px;
}

.cs-brand-logo::before {
  background-color: rgba(255, 255, 255, 0.1);
}

.cs-brand-logo .brand-items {
  opacity: 0.3;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.cs-brand-logo .brand-items .xb-line {
  margin-top: 25px;
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
}

.cs-brand-logo .brand-items .xb-img {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cs-testimonial-slider-nav .slick-slide.slick-active.slick-current .brand-items {
  opacity: 1;
}

.cs-testimonial-slider-nav .slick-slide.slick-active.slick-current .brand-items .xb-line {
  opacity: 1;
}

.cs-tes-item {
  display: inline-flex !important;
}

@media (max-width: 991px) {
  .cs-tes-item {
    flex-wrap: wrap;
  }
}

.cs-tes-item .xb-left-item {
  width: 53%;
  position: relative;
  border-radius: 15px 0 0 15px;
}

@media (max-width: 991px) {
  .cs-tes-item .xb-left-item {
    width: 100%;
  }
}

.cs-tes-item .xb-left-item::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  z-index: 1;
  height: 100%;
  width: 100%;
  background: linear-gradient(137deg, rgba(0, 0, 0, 0.2) 0%, rgba(1, 3, 21, 0.68) 26.12%, rgba(1, 3, 20, 0.66) 43.63%, rgba(1, 3, 21, 0.72) 65.07%, rgba(1, 3, 21, 0.56) 76.29%, rgba(1, 3, 21, 0) 100%);
}

.cs-tes-item .xb-left-item .xb-item--header {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  margin: 40px;
  opacity: 0;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  transition-delay: .3s;
  -webkit-transform: translateY(40px);
  -ms-transform: translateY(40px);
  transform: translateY(40px);
}

@media (max-width: 767px) {
  .cs-tes-item .xb-left-item .xb-item--header {
    margin: 20px;
  }
}

.cs-tes-item .xb-left-item .xb-item--sub-title {
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}

.cs-tes-item .xb-left-item .xb-item--logo {
  margin: 33px 0 25px;
}

.cs-tes-item .xb-left-item .xb-item--title {
  font-size: 28px;
}

.cs-tes-item .xb-right-item {
  padding: 25px 48px 25px 50px;
  width: 47%;
}

@media (max-width: 991px) {
  .cs-tes-item .xb-right-item {
    padding: 40px 20px;
    width: 100%;
  }
}

.cs-tes-item .xb-right-item .xb-item--content {
  font-size: 22px;
  font-weight: 600;
  line-height: 32px;
}

@media (max-width: 767px) {
  .cs-tes-item .xb-right-item .xb-item--content br {
    display: none;
  }
}

.cs-tes-item .xb-right-item .xb-item--name {
  font-size: 20px;
  font-weight: 500;
  display: block;
  line-height: 32px;
}

.cs-tes-item .xb-right-item .xb-item--desig {
  color: #a9a4c0;
}

.cs-tes-item .xb-right-item .xb-item--author {
  padding: 40px 0;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.cs-tes-item .xb-right-item .xb-item--number {
  font-size: 34px;
  font-weight: 700;
  display: block;
  margin-bottom: 15px;
  font-family: var(--font-heading-two);
}

.cs-tes-item .xb-right-item .xb-item--holder {
  flex-wrap: nowrap;
  gap: 30px;
}

.cs-tes-item .xb-right-item .xb-item--time {
  width: 50%;
  margin-top: 20px;
}

.cs-tes-item.slick-active .xb-left-item .xb-item--header {
  opacity: 1;
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
}

.cs-bran-nav {
  overflow: hidden;
}

.cs-tes-content {
  z-index: 2;
  padding: 1px;
  position: relative;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
}

.cs-tes-content::after,
.cs-tes-content::before {
  position: absolute;
  content: '';
  height: 100%;
  bottom: -29px;
  left: 30px;
  right: 30px;
  z-index: -1;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: rgba(255, 255, 255, 0.02);
}

.cs-tes-content::after {
  bottom: -14px;
  left: 15px;
  right: 15px;
}

.cs-tes-content .cs-testimonial-slider {
  padding: 19px;
  background: #010315;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  padding-right: 0;
}

.cs-tes-content .swiper-button-prev,
.cs-tes-content .swiper-button-next {
  position: absolute;
  top: 50%;
  height: 45px;
  width: 45px;
  color: #fff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
}

.cs-tes-content .swiper-button-prev i,
.cs-tes-content .swiper-button-next i {
  font-size: 30px;
  font-weight: 300;
}

.cs-tes-content .swiper-button-prev::after,
.cs-tes-content .swiper-button-prev::before,
.cs-tes-content .swiper-button-next::after,
.cs-tes-content .swiper-button-next::before {
  display: none;
}

.cs-tes-content .swiper-button-prev {
  left: -22.5px;
  right: auto;
}

.cs-tes-content .swiper-button-next {
  right: -22.5px;
  left: auto;
}

.cs-tes-shape .shape {
  z-index: -1;
  position: absolute;
}

.cs-tes-shape .shape--one {
  top: 6%;
  left: 14%;
}

@media (max-width: 767px) {
  .cs-tes-shape .shape--one {
    display: none;
  }
}

.cs-tes-shape .shape--two {
  top: 26.5%;
  left: 34%;
}

.cs-tes-shape .shape--two .world {
  animation: spin 80s linear infinite;
}

.cs-tes-shape .shape--three {
  bottom: 13%;
  left: 7%;
}

.hd-testimonial-item {
  z-index: 1;
  padding: 0;
  padding-bottom: 2px;
  border: none;
  cursor: pointer;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-heading-three);
}

.hd-testimonial-item .xb-item--inner {
  width: 100%;
  height: 100%;
  z-index: -1;
  padding-right: 40px;
  background: #f1f8cc;
  border-radius: inherit;
  border: 1px solid #ded7ca;
}

.hd-testimonial-item .xb-item--author {
  max-width: 100%;
  margin-right: 45px;
}

@media (max-width: 991px) {
  .hd-testimonial-item .xb-item--author {
    margin-right: 30px;
  }
}

@media (max-width: 767px) {
  .hd-testimonial-item .xb-item--author {
    padding-right: 0;
  }
}

.hd-testimonial-item .xb-item--author img {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.hd-testimonial-item .xb-item--holder {
  position: relative;
  display: block;
  width: calc(100% - 319px);
}

@media (max-width: 767px) {
  .hd-testimonial-item .xb-item--holder {
    width: 100%;
    padding: 0 30px 30px;
  }
}

.hd-testimonial-item .xb-item--content {
  width: 100%;
  transform: translateY(0);
  color: var(--color-heading-three);
}

.hd-testimonial-item .xb-item--name {
  font-family: var(--font-body);
  color: var(--color-heading-three);
}

.hd-testimonial-item .xb-item--desig {
  color: var(--color-heading-three);
}

.hd-testimonial-item .xb-item--rating {
  background: #f1f8cc;
  border: 1px solid #ded7ca;
  color: var(--color-heading-three);
}

.hd-testimonial-item .xb-item--quoat {
  position: absolute;
  top: -6px;
  left: 0;
}

@media (max-width: 1199px) {
  .hd-testimonial-item .xb-item--box {
    padding-top: 30px;
  }
}

.hd-testimonial-item .xb-item--shape {
  position: absolute;
  bottom: -45px;
  left: -20px;
  right: -20px;
  z-index: -1;
}

.hd-testimonial-item .tes-bg--one {
  background: #DEEFFF;
}

.hd-testimonial-item .tes-bg--three {
  background: #FAECF0;
}

.hd-testimonial-item:hover {
  transform: translateY(-4px);
}
.hd-testimonial-slider-inner {
  position: relative;
}
.hd-testimonial-slider-inner .swiper-pagination{
  margin: -60px;
}

@media (max-width: 767px) {
  .hd-testimonial-slider-inner {
    padding: 0 15px;
  }
}

.hd-testimonial-slider {
  margin-left: -375px;
  margin-right: -375px;
}

@media (max-width: 1199px) {
  .hd-testimonial-slider {
    margin-left: -500px;
    margin-right: -500px;
  }
}

@media (max-width: 767px) {
  .hd-testimonial-slider {
    margin-left: 0;
    margin-right: 0;
  }
}

.hd-testimonial-slider .swiper-pagination {
  bottom: -27px;
  z-index: 1;
}

.hd-testimonial-slider .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  opacity: .2;
  background: var(--color-heading-three);
}

.hd-testimonial-slider .swiper-pagination .swiper-pagination-bullet:not(:last-child) {
  margin-right: 6px;
}

.hd-testimonial-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
  transform: scale(1.4);
}

.da-testimonial {
  background: #f4f5fc;
  padding: 51px 50px 51px 80px;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
}

@media (max-width: 1199px) {
  .da-testimonial {
    padding: 40px;
  }
}

@media (max-width: 767px) {
  .da-testimonial {
    padding: 30px 25px;
  }
}

.da-tes-left {
  padding-right: 40px;
}

@media (max-width: 1199px) {
  .da-tes-left {
    padding-right: 0;
  }
}

.da-tes-left .xb-item--title {
  font-size: 26px;
  line-height: 38px;
  margin-bottom: 15px;
}

@media (max-width: 767px) {
  .da-tes-left .xb-item--title {
    font-size: 20px;
    line-height: 32px;
  }
}

.da-tes-left .xb-item--content.content--two {
  margin-bottom: 28px;
}

.da-tes-left .xb-item--number {
  font-weight: 800;
  font-size: 25px;
  line-height: 38px;
  color: #1438bc;
  margin-bottom: 4px;
}

.da-tes-left .xb-item--text {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #1438bc;
  display: inline-block;
}

.da-tes-left .xb-item--holder {
  margin: 5px 0 30px;
}

.da-tes-left .xb-item--meta {
  max-width: 230px;
  margin-top: 30px;
}

.da-tes-left .xb-item--meta:nth-child(1) {
  margin-right: 42px;
}

.da-tes-left .xb-item--meta:nth-child(1) .xb-item--text {
  max-width: 183px;
}

.da-tes-right {
  margin-left: 54px;
}

@media (max-width: 991px) {
  .da-tes-right {
    margin-left: 0;
  }
}

.da-tes-right .xb-item--author {
  margin-bottom: 20px;
  background: #f9f9fa;
  border: 8px solid #fff;
  border-radius: 16px;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
}

.da-tes-right .xb-item--author img {
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.da-tes-right .xb-item--avatar {
  text-align: center;
  background: #fff;
  padding: 12px 0px;
  border-radius: 12px;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -ms-border-radius: 12px;
  -o-border-radius: 12px;
  box-shadow: 0 4px 4px 0 rgba(224, 226, 240, 0.53);
}

.da-testimonial-slider {
  margin-left: 46px;
  margin-right: 46px;
}

.da-testimonial-wrap .da-swiper-btn {
  position: absolute;
  top: 50%;
  height: 64px;
  width: 64px;
  z-index: 2;
  background: #e7e9f9;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.da-testimonial-wrap .da-swiper-btn i {
  font-size: 20px;
  font-weight: 300;
  color: var(--color-black);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.da-testimonial-wrap .da-swiper-btn::after,
.da-testimonial-wrap .da-swiper-btn::before {
  display: none;
}

.da-testimonial-wrap .da-swiper-btn:hover {
  background: var(--color-heading-four);
}

.da-testimonial-wrap .da-swiper-btn:hover i {
  color: var(--color-white);
}

.da-testimonial-wrap .swiper-button-prev {
  left: -58px;
  right: auto;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .da-testimonial-wrap .swiper-button-prev {
    left: 0;
  }
}

@media (max-width: 1199px) {
  .da-testimonial-wrap .swiper-button-prev {
    left: 0;
  }
}

.da-testimonial-wrap .swiper-button-next {
  right: -58px;
  left: auto;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .da-testimonial-wrap .swiper-button-next {
    right: 0;
  }
}

@media (max-width: 1199px) {
  .da-testimonial-wrap .swiper-button-next {
    right: 0;
  }
}

.da-quote {
  width: 59.1%;
  background: #1438bc;
  padding: 93px 165px 100px 240px;
  min-height: 645px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .da-quote {
    padding: 50px;
  }
}

@media (max-width: 1199px) {
  .da-quote {
    padding: 40px;
  }
}

@media (max-width: 991px) {
  .da-quote {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .da-quote {
    width: 100%;
  }
}

.da-quote .xb-item--content {
  font-weight: 500;
  font-size: 45px;
  line-height: 58px;
  letter-spacing: -0.01em;
  color: #fff;
  margin-top: 40px;
}

@media (max-width: 991px) {
  .da-quote .xb-item--content {
    font-size: 26px;
    line-height: 42px;
    letter-spacing: 0;
  }
}

.da-quote .xb-item--holder {
  margin-top: 50px;
  gap: 24px;
}

.da-quote .xb-item--author {
  display: inline-block;
  border: 3px solid #53baff;
  border-radius: 100px;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -ms-border-radius: 100px;
  -o-border-radius: 100px;
}

.da-quote .xb-item--desig {
  font-size: 16px;
  color: #c4cdec;
}

.da-quote .xb-item--name {
  font-weight: 500;
  font-size: 22px;
  letter-spacing: 0em;
  color: #fff;
  margin-bottom: 5px;
}

.da-cta {
  width: 40.9%;
  min-height: 645px;
  background: #53baff;
  padding: 0 135px;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .da-cta {
    padding: 0 50px;
  }
}

@media (max-width: 1199px) {
  .da-cta {
    padding: 0 50px;
  }
}

@media (max-width: 991px) {
  .da-cta {
    padding: 0 30px;
    width: 50%;
  }
}

@media (max-width: 767px) {
  .da-cta {
    width: 100%;
    min-height: 0;
    padding: 50px 20px;
  }
}

.da-cta .xb-item--title {
  font-size: 50px;
  line-height: 64px;
  letter-spacing: -0.02em;
}

@media (max-width: 1199px) {
  .da-cta .xb-item--title {
    font-size: 40px;
    line-height: 55px;
  }
}

.cs-testimonial-slider .slick-arrow {
  position: absolute;
  top: 50%;
  left: -22px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  background-color: #ddd;
  z-index: 1;
  font-size: 30px;
  color: var(--color-white);
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

@media (max-width: 767px) {
  .cs-testimonial-slider .slick-arrow {
    left: -12px;
  }
}

.cs-testimonial-slider .slick-arrow.slick-next {
  left: auto;
  right: -22px;
}

@media (max-width: 767px) {
  .cs-testimonial-slider .slick-arrow.slick-next {
    right: -12px;
  }
}

.industrie-wrap {
  position: relative;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}

.indus-item {
  min-height: 232px;
  padding: 20px;
  text-align: center;
  position: relative;
  z-index: 1;
  margin-top: 30px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  box-shadow: 0 4px 13px 0 rgba(119, 152, 215, 0.19);
}

.indus-item::before {
  position: absolute;
  content: '';
  top: 27px;
  left: 19px;
  width: 200px;
  height: 180px;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transform: skewY(-25deg);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background-color: var(--color-yellow);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .indus-item::before {
    left: 5px;
  }
}

@media (max-width: 1199px) {
  .indus-item::before {
    left: 5px;
    top: 18px;
    width: 158px;
    height: 191px;
  }
}

.indus-item::after {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  background: #fff;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.indus-item:hover {
  transform: scale(1.1);
}

.indus-item:hover::before {
  opacity: 1;
  visibility: visible;
}

.indus-item .xb-title {
  font-size: 28px;
  margin-top: 35px;
  display: inline-block;
}

@media (max-width: 1199px) {
  .indus-item .xb-title {
    font-size: 22px;
  }
}

.cs-industries-item {
  position: relative;
  min-height: 219px;
  padding: 20px;
  z-index: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
}

.cs-industries-item .bg-shape1,
.cs-industries-item .bg-shape2 {
  position: absolute;
  content: '';
  z-index: -1;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.cs-industries-item .bg-shape1 svg,
.cs-industries-item .bg-shape2 svg {
  width: 100%;
}

.cs-industries-item .bg-shape2 {
  opacity: 0;
}

.cs-industries-item:hover .bg-shape1 {
  opacity: 0;
}

.cs-industries-item:hover .bg-shape2 {
  opacity: 1;
}

.cs-industries-item:hover .xb-item--icon .hover {
  opacity: 1;
}

.cs-industries-item:hover .xb-item--icon .default {
  opacity: 0;
}

.cs-industries-item .xb-item--icon {
  height: 66px;
  width: 66px;
  display: inline-block;
  position: relative;
  margin-bottom: 35px;
}

.cs-industries-item .xb-item--icon img {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.cs-industries-item .xb-item--icon .hover {
  opacity: 0;
}

.cs-industries-item .xb-item--title {
  font-size: 20px;
}

.industries-slider {
  margin-right: -260px;
  margin-left: -0px;
}

.hd-award-slider {
  padding-top: 3px;
}

@media (max-width: 767px) {
  .industries-slider {
    margin-right: 0;
  }
}

.team-wrap {
  gap: 30px;
  margin-top: 53px;
}

@media (max-width: 991px) {
  .team-wrap .xb-item--skill {
    width: 100%;
    flex-wrap: wrap;
    display: inline-flex;
  }
}

@media (max-width: 767px) {
  .team-wrap .xb-item--skill {
    margin-bottom: 20px;
  }
}

.team-wrap .xb-item--skill li {
  font-weight: 500;
  letter-spacing: -0.01em;
  color: #49515b;
  position: relative;
  padding-left: 34px;
}

@media (max-width: 991px) {
  .team-wrap .xb-item--skill li {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .team-wrap .xb-item--skill li {
    width: 100%;
  }
}

.team-wrap .xb-item--skill li:not(:last-child) {
  margin-bottom: 20px;
}

.team-wrap .xb-item--skill li::before {
  position: absolute;
  content: '\f00c';
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
  font-family: "Font Awesome 5 Pro";
  left: 0;
  top: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: var(--color-heading);
}

.team-wrap .xb-item--title {
  font-size: 30px;
  line-height: 34px;
  letter-spacing: 0em;
  margin-bottom: 30px;
}

.team-wrap .xb-item--holders {
  max-width: 457px;
}

@media (max-width: 1199px) {
  .team-wrap .xb-item--holders {
    max-width: 339px;
    transform: translateY(11px);
  }
}

@media (max-width: 991px) {
  .team-wrap .xb-item--holders {
    max-width: 348px;
  }
}

.team-heading .title {
  font-size: 305px;
  letter-spacing: -0.02em;
  display: inline-block;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .team-heading .title {
    font-size: 280px;
  }
}

@media (max-width: 1199px) {
  .team-heading .title {
    font-size: 238px;
  }
}

@media only screen and (max-width: 1023px) {
  .team-heading .title {
    font-size: 228px;
  }
}

@media (max-width: 991px) {
  .team-heading .title {
    font-size: 150px;
  }
}

@media (max-width: 767px) {
  .team-heading .title {
    font-size: 91px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .title {
    font-size: 125px;
  }
}

.team-heading .team-member .member {
  position: absolute;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border: 3px solid #fff;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background-color: #fff;
}

.team-heading .team-member .member img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-heading .team-member .member--one {
  left: 7%;
  bottom: 25px;
  height: 65px;
  width: 65px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .team-heading .team-member .member--one {
    left: 11%;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1300px) {
  .team-heading .team-member .member--one {
    left: 7%;
  }
}

@media (max-width: 991px) {
  .team-heading .team-member .member--one {
    left: 35px;
    bottom: -5px;
    height: 55px;
    width: 55px;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--one {
    left: 20px;
    bottom: -13px;
    height: 40px;
    width: 40px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .team-member .member--one {
    left: 32px;
    bottom: -2px;
  }
}

.team-heading .team-member .member--two {
  top: 15%;
  left: 15%;
  height: 91px;
  width: 91px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .team-heading .team-member .member--two {
    top: 12%;
    left: 17%;
  }
}

@media (max-width: 1199px) {
  .team-heading .team-member .member--two {
    top: 10%;
    left: 14%;
    height: 81px;
    width: 81px;
  }
}

@media (max-width: 991px) {
  .team-heading .team-member .member--two {
    top: 2%;
    left: 14%;
    height: 70px;
    width: 70px;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--two {
    top: -12%;
    left: 13%;
    height: 52px;
    width: 52px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .team-member .member--two {
    top: 3%;
    left: 15%;
  }
}

.team-heading .team-member .member--three {
  bottom: 57px;
  left: 39%;
  height: 60px;
  width: 60px;
}

@media (max-width: 1199px) {
  .team-heading .team-member .member--three {
    left: 36%;
    bottom: 53px;
  }
}

@media (max-width: 1199px) {
  .team-heading .team-member .member--three {
    left: 38%;
    bottom: 28px;
    height: 50px;
    width: 50px;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--three {
    left: 37%;
    bottom: 8px;
    height: 35px;
    width: 35px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .team-member .member--three {
    left: 38%;
    bottom: 20px;
  }
}

.team-heading .team-member .member--four {
  top: 19%;
  left: 49%;
  height: 49px;
  width: 49px;
}

@media (max-width: 1199px) {
  .team-heading .team-member .member--four {
    top: 15%;
    left: 48%;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--four {
    top: 2%;
    left: 45%;
    height: 32px;
    width: 32px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .team-member .member--four {
    top: 13px;
    left: 46%;
  }
}

.team-heading .team-member .member--five {
  bottom: 30px;
  right: 30%;
  height: 65px;
  width: 65px;
}

@media (max-width: 1199px) {
  .team-heading .team-member .member--five {
    bottom: 10px;
  }
}

@media (max-width: 991px) {
  .team-heading .team-member .member--five {
    bottom: -2px;
    right: 30%;
    height: 55px;
    width: 55px;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--five {
    bottom: -15px;
    right: 28%;
    height: 41px;
    width: 41px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .team-member .member--five {
    bottom: -7px;
    right: 30%;
  }
}

.team-heading .team-member .member--six {
  top: 20%;
  right: 17%;
  height: 87px;
  width: 87px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .team-heading .team-member .member--six {
    top: 16%;
    right: 18%;
  }
}

@media (max-width: 1199px) {
  .team-heading .team-member .member--six {
    top: 16%;
    right: 16%;
    height: 77px;
    width: 77px;
  }
}

@media (max-width: 991px) {
  .team-heading .team-member .member--six {
    top: 11%;
    right: 17%;
    height: 67px;
    width: 67px;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--six {
    top: -7%;
    right: 13%;
    height: 52px;
    width: 52px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .team-heading .team-member .member--six {
    top: 8%;
    right: 14%;
  }
}

.team-heading .team-member .member--seven {
  bottom: 28%;
  right: 0%;
  height: 65px;
  width: 65px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .team-heading .team-member .member--seven {
    right: 2%;
  }
}

@media (max-width: 991px) {
  .team-heading .team-member .member--seven {
    bottom: 23%;
    right: -3%;
    height: 57px;
    width: 57px;
  }
}

@media (max-width: 767px) {
  .team-heading .team-member .member--seven {
    bottom: 23%;
    right: -3%;
    height: 35px;
    width: 35px;
  }
}

.team-item {
  padding: 10px;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  border: 1px solid #e7e8ec;
  background-color: var(--color-white);
  position: relative;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}

.team-item:before {
  position: absolute;
  content: '';
  left: 30px;
  bottom: -12px;
  width: calc(100% - 60px);
  height: 12px;
  opacity: 0;
  visibility: hidden;
  border-radius: 0 0 12px 12px;
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 1px rgba(47, 1, 151, 0.04), 0 32px 24px -12px rgba(0, 39, 118, 0.06), 0 11px 4px 0 rgba(0, 39, 118, 0.01), 0 6px 4px 0 rgba(0, 39, 118, 0.02), 0 3px 3px 0 rgba(0, 39, 118, 0.03), 0 1px 1px 0 rgba(0, 39, 118, 0.04);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.team-item:hover:before {
  opacity: 0.7;
  visibility: visible;
}

.team-item .xb-item--item {
  top: 30px;
  right: 20px;
  position: absolute;
}

.team-item .xb-item--content {
  text-align: end;
}

.team-item .xb-item--content .xb-item--img img {
  width: 100%;
}

.team-item .xb-item--skill {
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0em;
  color: #0c111d;
  display: block;
  margin-bottom: 18px;
  font-family: var(--font-heading);
}

.team-item .xb-item--reating {
  padding: 3px 5px;
  color: var(--color-heading);
  border: 1px solid #CCC39A;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background-color: var(--color-white);
}

.team-item .xb-item--reating i {
  color: var(--color-yellow);
  margin-right: 5px;
}

.team-item .xb-item--inner {
  padding: 0 20px 25px;
  position: relative;
}

@media (max-width: 991px) {
  .team-item .xb-item--inner {
    padding: 18px 20px 17px;
  }
}

.team-item .xb-item--avatar {
  height: 114px;
  width: 114px;
  margin: -80px 0 23px;
  border: 3px solid #fff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  box-shadow: 0 4px 13px 0 rgba(119, 152, 215, 0.19);
}

@media (max-width: 1199px) {
  .team-item .xb-item--avatar {
    height: 100px;
    width: 100px;
  }
}

@media (max-width: 991px) {
  .team-item .xb-item--avatar {
    height: 60px;
    width: 60px;
  }
}

.team-item .xb-item--name {
  font-size: 24px;
  margin-bottom: 5px;
  letter-spacing: 0em;
}

.team-item .xb-item--social-link li:not(:last-child) {
  margin-right: 15px;
}

.team-item .xb-item--social-link li a {
  font-size: 20px;
  color: var(--color-black);
}

.team-item .xb-item--social-link li a svg {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
}

.team-slider {
  padding-bottom: 30px;
}

.team-slider .swiper-slide-active .team-item:before {
  opacity: 0.7;
  visibility: visible;
}

.sa-team .sa-swiper-btn {
  position: absolute;
  top: 38%;
  font-size: 30px;
  width: 36px;
  height: 43px;
  transform: translateY(-50%);
  color: var(--color-black);
  border: 1px solid #e7e8ec;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background: #f6f6f8;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.sa-team .sa-swiper-btn::after,
.sa-team .sa-swiper-btn::before {
  display: none;
}

.sa-team .sa-swiper-btn:hover {
  background: var(--color-primary-two);
}

.sa-team .sa-swiper-btn:hover i {
  color: #fff;
}

.sa-team .swiper-button-next {
  right: -85px;
}

.sa-team .swiper-button-prev {
  left: -85px;
}

.cd-team-member {
  margin-top: -10%;
}

.cd-team-member .xb-btn {
  position: absolute;
  bottom: 13%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

@media (max-width: 767px) {
  .cd-team-member .xb-btn {
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
    transform: translateX(0%);
  }
}

.cd-team-member .xb-shape {
  position: absolute;
  top: 33%;
  right: 38%;
  transform: translate(-50%, -50%);
}

.cp-team-item {
  padding: 50px;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
  width: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: var(--color-white);
  border: 1px solid #e7e8ec;
}

@media (max-width: 991px) {
  .cp-team-item {
    padding: 20px 25px;
  }
}

.cp-team-item::after {
  position: absolute;
  left: 0;
  top: 0;
  content: '';
  height: 100%;
  width: 7px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-primary-two);
}

.cp-team-item .xb-item--inner {
  gap: 30px;
}

.cp-team-item .xb-item--holder {
  gap: 20px 0;
}

.cp-team-item .xb-item--title {
  font-weight: 700;
  font-size: 28px;
  letter-spacing: 0em;
  margin-bottom: 23px;
  color: var(--color-heading-two);
}

@media (max-width: 767px) {
  .cp-team-item .xb-item--title {
    font-size: 24px;
  }
}

.cp-team-item .xb-item--title a {
  color: currentColor;
}

.cp-team-item .xb-item--meta {
  font-weight: 500;
  font-size: 18px;
  color: var(--color-heading-two);
}

.cp-team-item .xb-item--meta:not(:last-child) {
  margin-right: 82px;
}

.cp-team-item .xb-item--meta img {
  margin-right: 9px;
}

.cp-team-item:hover {
  background: #F6F6F8;
}

.cp-team-item:hover::after {
  background: var(--color-yellow);
}

.cp-team-item:hover .cp-team-btn a {
  color: var(--color-heading-two);
  background: var(--color-yellow);
}

.cp-team-item:hover .cp-team-btn a i {
  color: var(--color-heading-two);
}

.cp-team-wrap {
  margin-left: 80px;
  margin-right: 80px;
}

@media (max-width: 1199px) {
  .cp-team-wrap {
    margin-left: 0;
    margin-right: 0;
  }
}

.team-menu {
  padding: 7px;
  background: #f6f6f8;
  border: 1px solid #e7e8ec;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.team-menu button {
  z-index: 1;
  font-weight: 700;
  letter-spacing: 0.01em;
  background: none;
  padding: 9px 20px;
  position: relative;
  color: var(--color-heading-two);
  font-family: var(--font-heading);
}

.team-menu button:not(:last-child) {
  margin-right: 17px;
}

@media (max-width: 767px) {
  .team-menu button:not(:last-child) {
    margin-right: 5px;
  }
}

.team-menu button::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  opacity: 0;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-white);
  border: 1px solid #e7e8ec;
  box-shadow: 0 6px 13px 0 rgba(167, 197, 203, 0.39);
}

.team-menu button.active::before {
  opacity: 1;
}

.xb-blog-item .xb-item--holder {
  padding: 1px;
  position: relative;
  display: inline-flex;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: #23263c;
}

.xb-blog-item .xb-item--img {
  z-index: 1;
  overflow: hidden;
  position: relative;
  display: inline-block;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
}

.xb-blog-item .xb-item--img::before {
  position: absolute;
  content: '';
  left: 0;
  top: 0;
  opacity: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: rgba(1, 3, 21, 0.4);
}

.xb-blog-item .xb-item--img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.xb-blog-item .xb-item--line {
  position: absolute;
  top: -13px;
  left: 63px;
}

.xb-blog-item .xb-item--circle-arrow {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 100px;
  width: 100px;
  opacity: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  transform: translate(-50%, -50%) scale(0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
}

.xb-blog-item .xb-item--title {
  font-size: 22px;
  line-height: 32px;
  margin: 20px 0 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #343544;
}

.xb-blog-item .xb-item--title a {
  color: currentColor;
}

.xb-blog-item .xb-item--meta {
  gap: 10px 20px;
  justify-content: space-between;
  padding-right: 30px;
}

@media (max-width: 991px) {
  .xb-blog-item .xb-item--meta {
    padding-right: 0;
  }
}

.xb-blog-item .xb-item--meta li {
  color: #a9a4c0;
}

.xb-blog-item .xb-item--meta li span {
  margin-right: 7px;
  display: inline-block;
  transform: translateY(-1px);
}

.xb-blog-item:hover .xb-item--img img {
  transform: scale(1.1);
}

.xb-blog-item:hover .xb-item--img::before {
  opacity: 1;
}

.xb-blog-item:hover .xb-item--holder {
  background: linear-gradient(290.54deg, #6780D2 -0.71%, #2F3B8D 38.91%);
}

.xb-blog-item:hover .xb-item--circle-arrow {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.hd-blog .xb-item--img {
  overflow: hidden;
  margin-bottom: 30px;
  display: inline-block;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.hd-blog .xb-item--img img {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}

.hd-blog .xb-item--meta li {
  position: relative;
  font-size: 15px;
  font-weight: 500;
  display: inline-block;
}

.hd-blog .xb-item--meta li:not(:last-child) {
  padding-right: 10px;
  margin-right: 5px;
}

.hd-blog .xb-item--meta li:not(:last-child)::before {
  position: absolute;
  top: 50%;
  right: 0;
  content: '';
  height: 11px;
  width: 2px;
  background: #16140c;
  transform: translateY(-50%);
}

.hd-blog .xb-item--meta li span {
  margin-right: 8px;
  display: inline-block;
  transform: translateY(-2px);
}

.hd-blog .xb-item--title {
  font-size: 44px;
  margin: 5px 0 15px;
  letter-spacing: -0.03em;
}

@media (max-width: 1199px) {
  .hd-blog .xb-item--title {
    font-size: 32px;
  }
}

.hd-blog .xb-item--title a {
  color: currentColor;
}

.hd-blog:hover .xb-item--img img {
  transform: scale(1.2);
}

.hd-blog-left {
  margin-right: -30px;
}

.hd-blog-right {
  margin-top: 30px;
  margin-left: 40px;
  padding-bottom: 25px;
  border-bottom: 1px solid #d9d9d9;
}

@media (max-width: 991px) {
  .hd-blog-right {
    margin-left: 0;
  }
}

.hd-blog-right .xb-item--holder {
  width: calc(100% - 220px);
}

@media (max-width: 767px) {
  .hd-blog-right .xb-item--holder {
    width: calc(100% - 140px);
  }
}

.hd-blog-right .xb-item--img {
  margin-bottom: 0;
  width: 180px;
  margin-left: 40px;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media (max-width: 767px) {
  .hd-blog-right .xb-item--img {
    width: 120px;
    margin-left: 20px;
  }
}

.hd-blog-right .xb-item--title {
  font-size: 28px;
  font-weight: 500;
}

@media (max-width: 1199px) {
  .hd-blog-right .xb-item--title {
    font-size: 24px;
  }
}

.cd-blog-item {
  margin-top: 20px;
}

.cd-blog-item .xb-item--img {
  overflow: hidden;
  display: inline-block;
  border-radius: 18px 18px 0 0;
}

.cd-blog-item .xb-item--img img {
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
}

.cd-blog-item .xb-item--sub_title {
  font-weight: 600;
  color: var(--color-white);
}

.cd-blog-item .xb-item--title {
  font-size: 22px;
  line-height: 36px;
  margin-top: 20px;
  letter-spacing: -0.03em;
  color: var(--color-white);
}

@media (max-width: 1199px) {
  .cd-blog-item .xb-item--title {
    font-size: 16px;
    line-height: 30px;
  }
}

.cd-blog-item .xb-item--title a {
  color: currentColor;
}

.cd-blog-item .xb-item--arrow {
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 23px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-white);
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.cd-blog-item .xb-item--arrow i {
  font-size: 15px;
  font-weight: 500;
  transform: rotate(-45deg);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  color: var(--color-heading-four);
}

.cd-blog-item:hover .xb-item--img img {
  transform: scale(1.1) rotate(2deg);
}

.cd-blog-item:hover .xb-item--arrow {
  background: #00b59f;
}

.cd-blog-item:hover .xb-item--arrow i {
  color: var(--color-white);
  transform: rotate(0deg);
}

.da-blog-item {
  overflow: hidden;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-white);
}

.da-blog-item .xb-item--holder {
  padding: 30px 24px 20px;
}

.da-blog-item .xb-item--img {
  width: 100%;
  overflow: hidden;
}

.da-blog-item .xb-item--img img {
  width: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.da-blog-item .xb-item--date {
  font-weight: 500;
  font-size: 12px;
  letter-spacing: -0.01em;
  color: #71767b;
}

.da-blog-item .xb-item--title {
  font-size: 24px;
  letter-spacing: -0.04em;
  color: #212877;
  margin: 5px 0 55px;
}

.da-blog-item .xb-item--title a {
  color: currentColor;
}

.da-blog-item .xb-item--arrow {
  font-weight: 500;
  letter-spacing: -0.03em;
  color: #212877;
  display: inline-flex;
  align-items: center;
}

.da-blog-item .xb-item--arrow span {
  height: 38px;
  width: 38px;
  background: #212877;
  margin-right: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.da-blog-item .xb-item--arrow span i {
  transform: rotate(-45deg);
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.da-blog-item:hover .xb-item--img img {
  transform: scale(1.1) rotate(2deg);
}

.da-blog-item:hover .xb-item--arrow span {
  background: #1438bc;
}

.da-blog-item:hover .xb-item--arrow span i {
  transform: rotate(0deg);
}

.da-blog-wrapper .title {
  font-weight: 500;
}

.blog_details_section {
  background: linear-gradient(0deg, #f6f6f8 0%, #fff 100%);
}

.blog-slide-item {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  z-index: 1;
}

.blog-slide-item:hover .xb-item--img img {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

.blog-slide-item::before {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  content: '';
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(12, 17, 29, 0.26) 16.87%, rgba(12, 17, 29, 0.66) 33.34%, rgba(12, 17, 29, 0.84) 54.83%, #000 100%);
  z-index: 1;
}

.blog-slide-item .xb-item--holder {
  max-width: 910px;
  position: absolute;
  left: 50px;
  bottom: 50px;
  z-index: 2;
}

@media (max-width: 991px) {
  .blog-slide-item .xb-item--holder {
    padding-right: 80px;
  }
}

.blog-slide-item .xb-item--img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 767px) {
  .blog-slide-item .xb-item--img {
    height: 600px;
  }

  .blog-slide-item .xb-item--img img {
    height: 100%;
    object-fit: cover;
  }
}

.blog-slide-item .xb-item--tag {
  padding: 5px 10px;
  font-weight: 600;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background: var(--color-white);
  color: var(--color-heading-two);
}

.blog-slide-item .xb-item--title {
  font-weight: 700;
  font-size: 48px;
  margin: 36px 0 25px;
  color: var(--color-white);
}

@media (max-width: 991px) {
  .blog-slide-item .xb-item--title {
    font-size: 28px;
  }
}

.blog-slide-item .xb-item--title a {
  color: currentColor;
}

.blog-slide-item .xb-item--content {
  color: var(--color-white);
}

.blog-slider .swiper-pagination {
  position: absolute;
  left: auto;
  right: 50px;
  width: auto !important;
  bottom: 50px;
  left: auto;
}

.blog-slider .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  opacity: 1;
  background: #fff;
}

.blog-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--color-yellow);
}

.blog-item_button .blog-swiper-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.blog-item_button .blog-swiper-btn::after {
  display: none;
}

.blog-item_button .swiper-button-next {
  left: auto;
  right: 0;
}

.blog_details_item {
  overflow: hidden;
  border: 1px solid #e7e8ec;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blog_details_item:hover {
  box-shadow: 0 8px 18px 0 rgba(221, 221, 231, 0.95);
}

.blog_details_item:hover .xb-item--img img {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

.blog_details_item:not(:last-child) {
  margin-bottom: 40px;
}

.blog_details_item .xb-item--img {
  margin-right: 50px;
  overflow: hidden;
}

@media (max-width: 767px) {
  .blog_details_item .xb-item--img {
    margin-right: 0;
  }
}

.blog_details_item .xb-item--img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  width: 100%;
  min-height: 316px;
  object-fit: cover;
  max-width: 310px;
}

.blog_details_item .xb-item--holder {
  padding: 40px 45px 40px 0;
  width: calc(100% - 362px);
}

@media (max-width: 767px) {
  .blog_details_item .xb-item--holder {
    width: 100%;
    padding: 35px 20px 45px;
  }

  .blog_details_item .xb-item--img img {
    max-width: 100%;
    width: 100%;
  }
}

.blog_details_item .xb-item--text {
  color: #0f55dc;
  font-weight: 600;
  text-transform: uppercase;
}

.blog_details_item .xb-item--title {
  font-weight: 700;
  font-size: 26px;
  line-height: 36px;
  margin: 15px 0 25px;
  letter-spacing: 0em;
}

.blog_details_item .xb-item--title a {
  color: currentColor;
}

.blog_details_item .xb-item--button a {
  padding: 9.5px 24px;
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0em;
  color: var(--color-white);
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-primary-two);
}

.blog_details_item .xb-item--button a i {
  margin-left: 15px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  transform: rotate(-45deg);
}

.blog_details_item .xb-item--button a:hover {
  background-color: var(--color-yellow);
  color: var(--color-heading-two);
}

.blog_details_item .xb-item--button a:hover i {
  transform: rotate(0deg);
}

.blog_details_item .xb-item--inner {
  padding: 35px 40px 45px;
}

@media (max-width: 767px) {
  .blog_details_item .xb-item--inner {
    padding: 35px 20px 45px;
  }
}

.blog_details_item.blog_details_item-two {
  box-shadow: 0 8px 18px 0 rgba(221, 221, 231, 0.95);
}

.blog-pagination {
  margin-top: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.blog-pagination li a {
  width: 50px;
  height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border: 1px solid #e7e8ec;
  background: var(--color-white);
  color: var(--color-heading-two);
}

.blog-pagination li a:not(:last-child) {
  margin-right: 15px;
}

.blog-pagination li.active a,
.blog-pagination li:hover a {
  color: var(--color-white);
  background: var(--color-black);
}

.parallax-section {
  margin: 0;
  padding: 0;
  position: relative;
  overflow: hidden;
  height: 850;
}

.parallax-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transform: translateY(0);
  transition: transform 0.1s linear;
}

.errorMessage {
  color: red;
}

@media only screen and (min-width: 1200px) {
  .parallax-section {
    min-height: 850px;
  }
}

@media (max-width: 991px) {
  .parallax-section {
    min-height: 500px;
  }
}

@media (max-width: 767px) {
  .parallax-section {
    min-height: 300px;
  }
}

.xb-faq .accordion-button::after {
  display: none;
}

.xb-faq .accordion_box {
  position: relative;
  padding: 10px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: linear-gradient(122deg, rgba(255, 217, 17, 0) 0%, rgba(233, 226, 255, 0.8) 34.54%, #cae1f7 62.5%, rgba(250, 232, 138, 0.56) 100%), radial-gradient(74.51% 50% at 50% 100%, white 24.0899994969%, white 100%);
  background: url(../images/bg/faq_bg.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.xb-faq .accordion_box .block {
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid #E7E8EC;
}

.xb-faq .accordion_box .block:last-child {
  border-bottom: 0;
}

.xb-faq .accordion_box .block:first-child {
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

.xb-faq .accordion_box .block:last-child {
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
}

.xb-faq .accordion_box .block.active-block .acc-btn .number {
  color: var(--color-heading-two);
}

.xb-faq .accordion_box .acc-btn .accordion-button:not(.collapsed) .arrow::before {
  content: "\f068" !important;
  transform: rotate(180deg);
  color: var(--color-white) !important;
}

.xb-faq .accordion_box .acc-btn .accordion-button:not(.collapsed) .arrow::after {
  opacity: 1 !important;
}

.xb-faq .accordion_box .block.active-block .acc-btn .accordion-button.collapsed .arrow::after {
  opacity: 1 !important;
}

.xb-faq .accordion_box .block .acc-btn .accordion-button {
  padding: 28px 40px;
  font-size: 24px;
  line-height: 1.3;
  font-weight: 500;
  position: relative;
  cursor: pointer;
  background: #f6f6f8;
  color: var(--color-heading-two);
}

.xb-faq .accordion_box .block .acc-btn .accordion-button:focus {
  box-shadow: none;
}

@media (max-width: 991px) {
  .xb-faq .accordion_box .block .acc-btn .accordion-button {
    font-size: 20px;
    padding: 20px;
    padding-right: 30px;
  }
}

.xb-faq .accordion_box .block .acc-btn .accordion-button .arrow {
  position: absolute;
  z-index: 1;
  width: 36px;
  height: 36px;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  border: 1px solid #e7e8ec;
  background-color: #fff;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

@media (max-width: 767px) {
  .xb-faq .accordion_box .block .acc-btn .accordion-button .arrow {
    right: 5px;
  }
}

.xb-faq .accordion_box .block .acc-btn .accordion-button .arrow::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0;
  background: var(--color-primary-two);
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.xb-faq .accordion_box .block .acc-btn .accordion-button .arrow::before {
  position: absolute;
  top: 5px;
  left: 11px;
  font-size: 14px;
  font-size: 16px;
  font-weight: 500;
  content: "\f067";
  font-family: "Font Awesome 5 Pro";
  color: var(--color-heading-two);
  transition: 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.xb-faq .accordion_box .block .acc-btn .accordion-button .number {
  color: rgba(12, 17, 29, 0.5);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.xb-faq .accordion_box .block .acc_body {
  position: relative;
}

.xb-faq .accordion_box .block .acc_body.current {
  display: block;
}

.xb-faq .accordion_box .block .content {
  font-size: 16px;
  line-height: 28px;
  padding: 30px 140px 40px 40px;
  background: var(--color-white);
}

@media (max-width: 991px) {
  .xb-faq .accordion_box .block .content {
    padding: 30px 40px 40px 40px;
  }
}

@media (max-width: 767px) {
  .xb-faq .accordion_box .block .content {
    padding: 20px;
  }
}

.xb-faq .accordion_box .block .content p {
  margin-bottom: 15px;
}

.xb-faq .accordion_box .block .content ul li:not(:last-child) {
  margin-bottom: 5px;
}

.xb-faq .accordion_box .block .content ul li i {
  margin-right: 10px;
  font-size: 16px;
  color: var(--color-primary-two);
}

.xb-faq-two .accordion_box {
  background: none;
  border-radius: none;
  padding: 0;
}

.xb-faq-two .accordion_box .block {
  border-top: 1px solid #ded7ca;
}

.xb-faq-two .accordion_box .block:first-child {
  border-radius: 0;
}

.xb-faq-two .accordion_box .block:last-child {
  border-radius: 0;
}

.xb-faq-two .accordion_box .block:last-child {
  border-bottom: 1px solid #ded7ca;
}

.xb-faq-two .accordion_box .block .acc-btn {
  font-size: 22px;
  font-weight: 600;
  padding: 25px 20px;
  background: transparent;
  color: var(--color-heading-three);
  cursor: pointer;
}

.xb-faq-two .accordion_box .block .acc-btn.active {
  padding-bottom: 15px;
}

.xb-faq-two .accordion_box .block .acc-btn .arrow {
  position: absolute;
  height: 34px;
  line-height: 34px;
  width: 34px;
  right: 20px;
  background: #fefaf2;
  border: 1px solid #ded7ca;
  text-align: center;
  border-radius: 50%;
}

.xb-faq-two .accordion_box .block .acc-btn .arrow::before {
  content: "\f107";
  color: var(--color-heading-three);
  font-family: "Font Awesome 5 Pro";
}

.xb-faq-two .accordion_box .block .acc-btn .arrow::after {
  background: #fefaf2;
}

.xb-faq-two .accordion_box .block .content {
  font-size: 18px;
  line-height: 26px;
  background: transparent;
  padding: 0px 89px 25px 20px;
}

.xb-faq-two .accordion_box .block.active-block {
  background: #f6f0e6;
  border-top: 1px solid #16140c;
}

.xb-faq-two .accordion_box .block.active-block .acc-btn .arrow::before {
  content: "\f106";
  color: var(--color-heading-three);
  font-family: "Font Awesome 5 Pro";
}

.da-faq .accordion_box .block {
  border-top: none;
  border-bottom: 1px solid #d5dcf7;
}

.da-faq .accordion_box .block.active-block {
  border-top: none;
  background: transparent;
  border-bottom: 1px solid #d5dcf7;
}

.da-faq .accordion_box .block.active-block .acc-btn .number {
  color: var(--color-white);
  background: var(--color-heading-four);
}

.da-faq .accordion_box .block.active-block .acc-btn .arrow::before {
  transform: rotate(360deg);
}

.da-faq .accordion_box .block:last-child {
  border-bottom: 1px solid #d5dcf7;
}

.da-faq .accordion_box .block .acc-btn {
  font-size: 20px;
  color: #212877;
  padding-left: 0;
  letter-spacing: -0.02em;
  padding-left: 68px;
}

.da-faq .accordion_box .block .acc-btn .number {
  width: 48px;
  height: 48px;
  font-weight: 600;
  color: #1438bc;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  display: inline-flex;
  border: 1px solid #e9eced;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  box-shadow: 0 4px 4px 0 #eaecf8;
  background: var(--color-white);
  position: absolute;
  left: 0;
  top: 14px;
}

.da-faq .accordion_box .block .acc-btn .arrow {
  position: absolute;
  right: 10px;
  border: none;
  background: transparent;
}


.da-faq .accordion_box .block .acc-btn .arrow::before,
.da-faq .accordion_box .block .acc-btn .arrow::after {
  color: #212877;
}

.da-faq .accordion_box .block .acc-btn .arrow::after {
  background: transparent;
}

.da-faq .accordion_box .block .content {
  padding: 0px 70px 25px 68px;
}

.da-faq .accordion_box .block .content p {
  margin: 0;
}

.da-right-faq {
  margin-left: 33px;
}

@media (max-width: 991px) {
  .da-right-faq {
    margin-left: 0;
  }
}

.da-left-faq {
  margin-right: 33px;
}

.cp-faq .accordion_box {
  border: 1px solid #e7e8ec;
  background: var(--color-white);
}

.cp-faq .accordion_box .block {
  margin: 0;
  border: 1px solid #e7e8ec;
}

.cp-faq .accordion_box .block:not(:last-child) {
  border-bottom: none;
}

.cp-faq .accordion_box .block .content {
  padding: 0;
  border-top: 0;
}

.cp-faq .accordion_box .block .content p {
  margin-bottom: 0;
}

.cp-faq .accordion_box .block .acc-btn {
  padding: 0;
}

.cta-wrap {
  height: 100%;
  width: 100%;
  border-radius: 12px;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -ms-border-radius: 12px;
  -o-border-radius: 12px;
  padding: 30px 79px 30px 70px;
  background-image: url(../images/cta/bg-img.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  margin-bottom: -220px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

@media (max-width: 1199px) {
  .cta-wrap {
    padding: 30px 58px 30px;
  }
}

@media (max-width: 767px) {
  .cta-wrap {
    padding: 30px 25px;
  }
}

.cta-wrap .xb-item--title {
  font-size: 48px;
  line-height: 60px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--color-white);
}

@media (max-width: 767px) {
  .cta-wrap .xb-item--title {
    font-size: 28px;
    line-height: 40px;
  }
}

.work.sbg {
  background-color: rgb(246, 246, 248);
}

.cta-wrap .xb-item--content {
  font-size: 20px;
  color: var(--color-white);
}

.cta-wrap .xb-item--holder {
  max-width: 515px;
}

.hd-cta .title {
  line-height: 65px;
  line-height: 70px;
}

@media (max-width: 1199px) {
  .hd-cta .title {
    line-height: 46px;
    font-size: 38px;
  }
}

.hd-cta .sub-title img {
  transform: translateY(-4px);
}

.hd-cta_shape .shape {
  position: absolute;
}

.hd-cta_shape .shape--one {
  left: 45px;
  top: 20%;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hd-cta_shape .shape--one {
    left: 20px;
    max-width: 300px;
  }
}

@media (max-width: 1199px) {
  .hd-cta_shape .shape--one {
    left: 45px;
    top: 22%;
    max-width: 220px;
  }
}

@media (max-width: 991px) {
  .hd-cta_shape .shape--one {
    left: 5px;
    top: 15%;
    max-width: 200px;
  }
}

@media (max-width: 767px) {
  .hd-cta_shape .shape--one {
    left: 3px;
    top: 8%;
    max-width: 140px;
  }
}

.hd-cta_shape .shape--two {
  right: 45px;
  top: 36%;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hd-cta_shape .shape--two {
    right: 20px;
    top: 40%;
    max-width: 300px;
  }
}

@media (max-width: 1199px) {
  .hd-cta_shape .shape--two {
    top: 51%;
    max-width: 200px;
  }
}

@media (max-width: 991px) {
  .hd-cta_shape .shape--two {
    top: 57%;
    max-width: 180px;
    right: 10px;
  }
}

@media (max-width: 767px) {
  .hd-cta_shape .shape--two {
    top: auto;
    max-width: 140px;
    bottom: 4%;
  }
}

.hd-cta_shape .shape--three {
  left: 0;
  bottom: -8px;
}

.cd-cta {
  position: absolute;
  bottom: 0;
  z-index: 11;
  text-align: center;
  left: 0;
  right: 0;
  bottom: 11%;
}

@media (max-width: 767px) {
  .cd-cta {
    bottom: 0;
    background: #fff;
    padding: 50px 10px;
  }
}

.cd-cta .sec-title--five .title {
  font-size: 40px;
  line-height: 50px;
}

@media (max-width: 767px) {
  .cd-cta .sec-title--five .title {
    font-size: 32px;
    line-height: 44px;
  }
}

.cta-inner {
  gap: 30px;
}

@media (max-width: 1199px) {
  .cta-right_img {
    max-width: 320px;
  }
}

.cta-bg::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(../images/bg/cta_bg_overly.png);
  background-repeat: no-repeat;
  background-size: cover;
  content: "";
  z-index: 1;
}

.cta-sec-title {
  position: relative;
  z-index: 2;
}

.cta-height {
  min-height: 833px;
}

@media (max-width: 1199px) {
  .cta-height {
    min-height: 700px;
  }
}

@media (max-width: 767px) {
  .cta-height {
    min-height: 500px;
  }
}

.cta-image {
  position: absolute;
  bottom: 0;
}

.cta-icons .icon {
  position: absolute;
}

@media (max-width: 767px) {
  .cta-icons .icon {
    display: none;
  }
}

.cta-icons .icon--1 {
  top: 68%;
  left: 14%;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .cta-icons .icon--1 {
    top: 67%;
    left: 7%;
  }
}

@media (max-width: 1199px) {
  .cta-icons .icon--1 {
    top: 68%;
    left: 5%;
  }
}

.cta-icons .icon--2 {
  top: 41%;
  left: 19%;
}

@media (max-width: 1199px) {
  .cta-icons .icon--2 {
    top: 37%;
    left: 12%;
  }
}

.cta-icons .icon--3 {
  left: 32%;
  top: 29%;
}

@media (max-width: 1199px) {
  .cta-icons .icon--3 {
    left: 29%;
  }
}

.cta-icons .icon--4 {
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  top: 20%;
}

.cta-icons .icon--5 {
  right: 32%;
  top: 29%;
}

@media (max-width: 1199px) {
  .cta-icons .icon--5 {
    right: 28%;
  }
}

.cta-icons .icon--6 {
  top: 41%;
  right: 19%;
}

@media (max-width: 1199px) {
  .cta-icons .icon--6 {
    top: 36%;
    right: 9%;
  }
}

.cta-icons .icon--7 {
  top: 68%;
  right: 14%;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .cta-icons .icon--7 {
    top: 67%;
    right: 7%;
  }
}

@media (max-width: 1199px) {
  .cta-icons .icon--7 {
    top: 68%;
    right: 5%;
  }
}

.contact-top {
  gap: 30px;
}

@media (max-width: 767px) {
  .contact-top {
    flex-wrap: wrap !important;
  }
}

.contact-top .cont_item {
  background: #f9f9f9;
  padding: 16px 0;
  width: 33.333%;
  display: inline-flex;
  justify-content: center;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media (max-width: 767px) {
  .contact-top .cont_item {
    width: 100%;
  }
}

.contact-top .cont_item .xb-item--star {
  color: #FECE00;
}

.contact-top .cont_item .xb-item--rating_num {
  margin: 0 8px;
  font-weight: 700;
  font-size: 26px;
  color: var(--color-heading);
  letter-spacing: -0.01em;
  font-family: var(--font-heading);
}

@media (max-width: 991px) {
  .contact-top .cont_item .xb-item--rating_num {
    font-size: 22px;
  }
}

@media (max-width: 991px) {
  .contact-top .cont_item .xb-item--logo {
    max-width: 110px;
  }
}

@media (max-width: 767px) {
  .contact-top .cont_item .xb-item--logo {
    max-width: 130px;
  }
}

.contact-wrap {
  padding: 57px 60px 60px;
  background-color: #f9f9f9;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media (max-width: 767px) {
  .contact-wrap {
    padding: 57px 25px 60px;
  }
}

.contact-wrap .xb-item--sub-title {
  font-size: 14px;
  color: var(--color-heading);
  font-weight: 600;
}

.contact-wrap .xb-item--title {
  font-size: 30px;
  margin: 20px 0 43px;
  letter-spacing: 0em;
}

@media (max-width: 767px) {
  .contact-wrap .xb-item--title {
    font-size: 25px;
  }
}

.contact-wrap .contact-btn button {
  padding: 21px 40px 20px 40px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.contact-wrap .contact-btn button img {
  margin-left: 20px;
}

.contact-form .input-field {
  margin-bottom: 30px;
}

.contact-form .input-field .img {
  position: absolute;
  left: 20px;
  top: 48%;
  transform: translateY(-50%);
  opacity: 0.5;
}

.contact-form .input-field input {
  height: 60px;
  padding: 0 20px 0 46px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background: transparent;
  border: 1px solid #dde3e5;
}

.contact-form .input-field input:focus,
.contact-form .input-field textarea:focus {
  border: 1px solid var(--color-primary);
}

.contact-form .input-field input:focus::placeholder,
.contact-form .input-field textarea:focus::placeholder {
  color: var(--color-heading);
}

.contact-form .input-field input:focus~.img,
.contact-form .input-field textarea:focus~.img {
  opacity: 1;
}

.contact-form .input-field.text-field textarea {
  height: 108px;
  padding: 17px 46px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background: transparent;
  border: 1px solid #dde3e5;
}

.contact-form .input-field.text-field textarea:focus {
  border: 1px solid var(--color-primary);
}

.contact-form .input-field.text-field .img {
  left: 20px;
  top: 29px;
  opacity: 0.5;
}

.contact-btn a {
  font-size: 20px;
  padding: 20.5px 37.5px;
}

.contact-btn a img {
  margin-left: 30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .contact-info {
    text-align: center;
  }
}

.contact-info .xb-item--img {
  overflow: hidden;
  display: inline-block;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media only screen and (min-width: 1200px) {
  .contact-info .xb-item--img img {
    min-height: 464px;
    object-fit: cover;
  }
}

.contact-info .xb-item--author {
  text-align: center;
  background: #fff;
  padding: 24px 0;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .contact-info .xb-item--author {
    width: 81%;
    display: inline-block;
  }
}

.contact-info .xb-item--name {
  font-size: 24px;
  letter-spacing: 0em;
  margin-bottom: 5px;
}

@media (max-width: 767px) {
  .social-icon {
    justify-content: center;
  }
}

.social-icon li {
  width: 24%;
  z-index: 1;
  height: 80px;
  margin-top: 6px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background-color: var(--color-white);
}

@media (max-width: 1199px) {
  .social-icon li {
    width: 23%;
  }
}

.social-icon li:not(:last-child) {
  margin-right: 5px;
}

@media (max-width: 1199px) {
  .social-icon li:not(:last-child) {
    margin-right: 8px;
  }
}

@media only screen and (max-width: 1023px) {
  .social-icon li:not(:last-child) {
    margin-right: 7px;
  }
}

.social-icon li::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  transform: scale(0.8);
  background: #111112;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  z-index: -1;
}

.social-icon li span svg path {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.social-icon li:hover::before {
  opacity: 1;
  transform: scale(1);
}

.social-icon li:hover span svg path {
  fill: var(--color-white);
}

.contact-two {
  padding: 20px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.cs-contact-wrap {
  padding: 60px;
  z-index: 1;
  min-height: 731px;
  position: relative;
  background: #010315;
  margin-right: 16px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

@media (max-width: 1199px) {
  .cs-contact-wrap {
    padding: 60px 30px;
  }
}

.cs-contact-wrap::before {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  content: '';
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background-image: url(../images/bg/cont-bg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.cs-contact-wrap .sub-title {
  margin-bottom: 22px;
}

.cs-contact-wrap .xb-item--content {
  color: #a9a4c0;
  display: inline-block;
  margin-bottom: 27px;
}

.cs-contact-wrap .xb-item--cont_info li {
  align-items: center;
  font-family: var(--font-heading-two);
  font-weight: 700;
  font-size: 20px;
}

.cs-contact-wrap .xb-item--cont_info li:not(:last-child) {
  margin-bottom: 12px;
}

.cs-contact-wrap .xb-item--cont_info li span {
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  display: inline-flex;
  background: #E2EA46;
  border-radius: 40px;
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -ms-border-radius: 40px;
  -o-border-radius: 40px;
}

.cs-contact-wrap .xb-item--img {
  position: absolute;
  bottom: 0;
  left: 0;
}

.cs-contact-form {
  margin-right: 0;
  margin-left: 0;
}

.cs-contact-form .title {
  font-size: 22px;
  line-height: 32px;
}

.cs-contact-form .contact-form {
  margin-top: 25px;
}

.cs-contact-form .contact-form .errorMessage{
  margin-top: -10px;
  margin-bottom: 5px;
}

.cs-contact-form .input-field {
  margin-bottom: 25px;
}

.cs-contact-form .input-field label {
  font-weight: 500;
  margin-bottom: 10px;
  letter-spacing: -0.01em;
}

.cs-contact-form .input-field .input-box {
  z-index: 1;
  position: relative;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
}

.cs-contact-form .input-field .input-box::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  z-index: -1;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  background: linear-gradient(334deg, rgba(103, 128, 210, 0.4) 0%, rgba(47, 59, 141, 0.4) 100%);
}

.cs-contact-form .input-field .input-box input,
.cs-contact-form .input-field .input-box textarea {
  color: #fff;
  border: none;
  padding-left: 20px;
  background: #1a1c2c;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
}

.cs-contact-form .input-field .input-box textarea {
  height: 100px;
}

.cs-contact-form .input-field .input-box textarea:focus {
  border: unset;
}

.cs-contact-form .contact-btn {
  margin-top: 13px;
}

.cs-contact-form .contact-btn button {
  background: transparent;
}

.contact-bg .xb-shape {
  left: 0;
  bottom: 0;
  position: absolute;
}

.cd-contact-form {
  background: #fff;
  padding: 70px 80px 75px;
  border: 1px solid #e7e8ec;
}

@media (max-width: 991px) {
  .cd-contact-form {
    padding: 40px 50px 55px;
  }
}

@media (max-width: 767px) {
  .cd-contact-form {
    padding: 30px 30px 35px;
  }
}

.cd-contact-form::before {
  display: none;
}

.cd-contact-form .xb-title {
  font-weight: 700;
  font-size: 34px;
  margin-bottom: 15px;
  color: var(--color-heading-two);
}

.cd-contact-form .xb-content {
  color: #494d57;
  line-height: 30px;
}

.cd-contact-form .contact-form {
  margin-top: 20px;
}

.cd-contact-form .input-field {
  margin-bottom: 35px;
}

.cd-contact-form .input-field label {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  letter-spacing: -0.01em;
  color: var(--color-heading-two);
}

.cd-contact-form .input-field .input-box::before {
  display: none;
}

.cd-contact-form .input-field .input-box input,
.cd-contact-form .input-field .input-box textarea {
  padding-left: 20px;
  background: #f6f6f8;
  border: 1px solid #e7e8ec;
  color: var(--color-heading-two);
}

.cd-contact-form .input-field .input-box input:focus,
.cd-contact-form .input-field .input-box textarea:focus {
  border: 1px solid var(--color-primary-two);
}

.cd-contact-form .input-field .input-box textarea {
  min-height: 105px;
}

.cp-contact-bottom .xb-item--download-btn {
  font-weight: 600;
  font-size: 20px;
  letter-spacing: 0em;
  color: #85888e;
  padding: 12px 30px;
  margin-bottom: 20px;
  background: var(--color-white);
  border: 1px solid #85888e;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
}

.cp-contact-bottom .xb-item--download-btn img {
  margin-right: 10px;
}

.cp-contact-bottom .xb-item--content {
  font-size: 18px;
  font-weight: 500;
  color: #494d57;
  display: block;
  margin-bottom: 0;
}

.cp-contact-bottom .xb-item--content span {
  color: var(--color-heading-two);
}

.item-contact_form {
  margin: 0;
  margin-right: 65px;
  min-height: 708px;
  padding: 40px 50px 50px;
}

.item-contact_form .contact-form {
  margin-top: 26px;
}

.item-contact_form .input-field {
  margin-bottom: 25px;
}

.item-contact_info {
  padding: 40px 50px;
  margin-left: -40px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  border: 1px solid #e7e8ec;
  background: var(--color-white);
}

.item-contact_info .xb-item--top {
  margin-bottom: 36px;
  border-bottom: 1px solid #e7e8ec;
}

.item-contact_info .xb-item--top span {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  display: block;
}

.item-contact_info .xb-item--top span img {
  margin-right: 10px;
}

.item-contact_info .xb-item--title {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 30px;
}

.item-contact_info .social_icons_block {
  margin: 37px 0 30px;
}

.item-contact_info .social_icons_block li a {
  height: 45px;
  width: 45px;
  font-size: 18px;
}

.item-contact_info .breack-line {
  margin: 35px 0 37px;
}

.contact-info_widget:not(:last-child) {
  margin-bottom: 34px;
}

.contact-info_widget .xb-title {
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.contact-info_widget .xb-location {
  font-weight: 500;
}

.gmap_canvas {
  margin-bottom: -219px;
}

.gmap_canvas iframe {
  width: 100%;
  height: 687px;
  display: block;
}

.cp-contact-bottom label {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  letter-spacing: -0.01em;
  color: var(--color-heading-two);
}

.cp-contact-bottom input {
  display: block;
  margin-bottom: 30px;
}

.xb-work-item {
  display: inline-block;
  padding: 18px;
}

.xb-work-item .xb-item--ineer {
  height: 195px;
  width: 195px;
  padding: 10px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
}

.xb-work-item .xb-item--title {
  font-size: 18px;
  margin-top: 15px;
}

.xb-work-item .xb-item--content {
  position: absolute;
  left: 50%;
  bottom: -54px;
  text-align: center;
  transform: translateX(-50%);
}

@media (max-width: 991px) {
  .xb-work-item .xb-item--line {
    display: none;
  }
}

.xb-work-item .xb-item--number {
  font-weight: 500;
}

@media (max-width: 991px) {
  .xb-work-item .xb-item--number {
    display: none;
  }
}

.xb-work-item .xb-item--arrow {
  position: absolute;
  top: 50%;
  right: -46%;
  transform: translateY(-50%);
}

@media (max-width: 991px) {
  .xb-work-item .xb-item--arrow {
    display: none;
  }
}

.xb-work-item .xb-img {
  position: absolute;
  content: '';
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  animation: spin 20s linear infinite;
}

.xb-work-item--middle {
  padding: 24px;
}

.xb-work-item--middle .xb-item--ineer {
  height: 279px;
  width: 279px;
}

.xb-work-item--middle .xb-item--arrow {
  right: -32%;
}

.xb-work-item--middle .xb-item--title {
  margin-top: 27px;
}

.cd-work-item {
  position: relative;
  padding-left: 57px;
}

.cd-work-item:not(:last-child) {
  padding-bottom: 24px;
}

.cd-work-item::before {
  position: absolute;
  content: '';
  left: 0;
  bottom: -51px;
  height: 100%;
  width: 2px;
  background: #00b59f;
}

@media (max-width: 767px) {
  .cd-work-item::before {
    left: 23px;
  }
}

.cd-work-item:nth-child(2)::before {
  background: #ff6a00;
}

.cd-work-item:nth-child(2) .xb-number {
  background: #ff6a00;
}

.cd-work-item:nth-child(3)::before {
  background: #bb33df;
}

.cd-work-item:nth-child(3) .xb-number {
  background: #bb33df;
}

.cd-work-item:nth-child(4)::before {
  height: 426px;
  bottom: 0px;
  background: #3eb3ff;
}

.cd-work-item:nth-child(4)::after {
  position: absolute;
  content: '';
  bottom: 0;
  left: -7px;
  height: 15px;
  width: 15px;
  background: #3eb3ff;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.cd-work-item:nth-child(4) .xb-number {
  background: #3eb3ff;
}

.cd-work-item .xb-number {
  position: absolute;
  top: 51px;
  left: -24px;
  height: 48px;
  width: 48px;
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  background: #00b59f;
  border-radius: 48px;
  -webkit-border-radius: 48px;
  -moz-border-radius: 48px;
  -ms-border-radius: 48px;
  -o-border-radius: 48px;
}

@media (max-width: 767px) {
  .cd-work-item .xb-number {
    left: 0;
  }
}

.cd-work-item .xb-item--inner {
  max-width: 608px;
  padding: 37px 47px 40px;
  border: 1px solid #eaedf0;
  border-radius: 16px;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
  background: var(--color-white);
}

@media (max-width: 767px) {
  .cd-work-item .xb-item--inner {
    padding: 20px 30px 30px;
  }
}

.cd-work-item .xb-item--title {
  font-size: 22px;
  line-height: 38.4px;
  letter-spacing: -0.03em;
}

@media (max-width: 767px) {
  .cd-work-item .xb-item--title {
    font-size: 18px;
    line-height: 32px;
  }
}

.cd-work-item .xb-item--content {
  font-weight: 500;
  line-height: 28.8px;
}

.cd-work-item .xb-item--img {
  margin-top: 50px;
  overflow: hidden;
  display: inline-block;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.cd-work-left {
  position: sticky;
  top: 150px;
}

.tp-work-item {
  padding: 40px 30px;
  background: #fff;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  box-shadow: 0 8px 18px 0 rgba(221, 221, 231, 0.95);
}

.tp-work-item .xb-item--inner {
  display: flex;
  align-items: center;
  justify-content: center;
  display: inline-flex;
  align-items: flex-start !important;
}

.tp-work-item .xb-item--icon {
  margin-right: 30px;
  width: 50px;
}

.tp-work-item .xb-item--holder {
  width: calc(100% - 80px);
}

.tp-work-item .xb-item--title {
  font-weight: 700;
  font-size: 28px;
  margin-bottom: 20px;
  letter-spacing: 0em;
  color: var(--color-heading-two);
}

.tp-work-item .xb-item--content {
  max-width: 461px;
}

.tp-work-item:hover {
  transform: scale(1.03);
}

.xb-work-wrap {
  border-radius: 25px;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  -ms-border-radius: 25px;
  -o-border-radius: 25px;
}

.award-item {
  z-index: 1;
  margin-top: 20px;
  text-align: center;
  position: relative;
  min-height: 392px;
  padding: 50px 25px 30px;
}

.award-item::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-image: url(../images/bg/award-bg01.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.award-item .xb-img {
  -webkit-transition: 0.8s;
  -o-transition: 0.8s;
  transition: 0.8s;
}

.award-item .xb-title {
  font-size: 18px;
  font-weight: 500;
  margin-top: 12px;
  display: inline-block;
}

.award-item:hover .xb-img {
  transform: rotateY(360deg);
}

.award-top .content {
  width: 34%;
  display: inline-block;
  transform: translateY(-7px);
}

@media (max-width: 991px) {
  .award-top .content {
    width: 100%;
  }
}

.hd-award-item {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 220px;
  z-index: 1;
  position: relative;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: var(--color-heading-three);
}

.hd-award-item::before {
  position: absolute;
  bottom: 1px;
  left: 0;
  content: '';
  height: 100%;
  width: 100%;
  z-index: -1;
  border-radius: inherit;
  background: #f6f0e6;
  border: 1px solid #ded7ca;
}

.hd-award-item::after {
  position: absolute;
  bottom: -7px;
  left: 18px;
  content: '';
  height: 2px;
  width: 83%;
  z-index: -2;
  background: #110f10;
  filter: blur(9px);
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.ab-award-content {
  position: unset;
}

.ab-award-content .sec-title--two .content {
  max-width: 465px;
  font-size: 16px;
  line-height: 26px;
  margin-top: 20px;
}

.ap-award-item {
  width: 160px;
  height: 180px;
  background: #fff;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  box-shadow: 0 8px 18px 0 rgba(221, 221, 231, 0.95);
}

@media (max-width: 767px) {
  .ap-award-item {
    width: 100px;
    height: 100px;
  }

  .ap-award-item .xb-img {
    width: 60px;
  }
}

.ap-award-inner:not(:last-child) {
  margin-right: 30px;
}

.ap-award-wrap {
  max-height: 775px;
  overflow: hidden;
  position: relative;
  justify-content: flex-end;
}

.ap-award-wrap::after,
.ap-award-wrap::before {
  position: absolute;
  top: -20px;
  right: 0;
  z-index: 1;
  content: '';
  width: 100%;
  height: 120px;
  background: #f6f6f8;
  filter: blur(17.7000007629px);
}

.ap-award-wrap:after {
  top: auto;
  bottom: -36px;
}

.xb-video {
  position: relative;
}

.xb-video .btn-video {
  position: absolute;
  height: 86px;
  width: 86px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: var(--color-white);
}

.xb-video .btn-video::before,
.xb-video .btn-video::after {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  content: '';
  z-index: -2;
  border-radius: inherit;
  background: var(--color-white);
}

.xb-video .btn-video::after {
  animation: pulse-border-big 1500ms ease-out infinite;
}

.video-wrap {
  position: relative;
}

.video-wrap .xb-shape .img {
  position: absolute;
}

.video-wrap .xb-shape .img--one {
  top: 15%;
  left: -12px;
}

.video-wrap .xb-shape .img--two {
  top: 50%;
  right: -29px;
}

.cp-video {
  position: relative;
  z-index: 1;
}

.cp-video::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background-color: #F6F6F8;
  content: "";
  z-index: -1;
}

.cp-video .xb-img img {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.cp-video .title {
  font-weight: 700;
  font-size: 270px;
  text-align: center;
  color: #fff;
  position: absolute;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
}

@media (max-width: 1199px) {
  .cp-video .title {
    font-size: 210px;
    bottom: -47px;
  }
}

@media (max-width: 991px) {
  .cp-video .title {
    font-size: 150px;
    bottom: -34px;
  }
}

@media (max-width: 767px) {
  .cp-video .title {
    font-size: 76px;
    bottom: -12px;
  }
}

.career_video video {
  width: 100%;
  border-radius: 20px;
  height: 100%;
  object-fit: cover;
  max-height: 600px;
}

.sd-video {
  overflow: hidden;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.sd-video::before {
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  height: 100%;
  width: 100%;
  border-radius: inherit;
  background: rgba(12, 17, 29, 0.4);
}

.sd-video .btn-video {
  width: 88px;
  height: 88px;
}

.item-details_image {
  overflow: hidden;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.integration-item {
  text-align: center;
  background: #f6f0e6;
  padding: 20px;
  width: 199px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  border: 1px solid #ded7ca;
  margin-bottom: 20px;
}

@media (max-width: 1199px) {
  .integration-item {
    width: 147px;
  }
}

.integration-item .xb-icon {
  height: 68px;
  width: 68px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 13px;
  background: #fefaf2;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.integration-item .xb-title {
  font-weight: 500;
  display: block;
  margin-bottom: 6px;
  color: var(--color-heading-three);
}

.integration-left {
  margin-right: -8px;
}

@media (max-width: 1199px) {
  .integration-left {
    margin-right: 0;
  }
}

.integration-right {
  margin-left: -8px;
  margin-right: 0;
}

.integration-inner:not(:last-child) {
  margin-right: 20px;
}

.integration-middle {
  padding: 30px;
  height: 100%;
  margin: 12px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: #fefaf2;
  border: 2px solid #16140c;
  border-radius: 205px;
  -webkit-border-radius: 205px;
  -moz-border-radius: 205px;
  -ms-border-radius: 205px;
  -o-border-radius: 205px;
}

.integration-middle .title {
  font-size: 36px;
  line-height: 46px;
}

.intgration-item--one::before,
.intgration-item--one::after,
.intgration-item--two::before,
.intgration-item--two::after {
  position: absolute;
  content: '';
  top: -9%;
  left: -11%;
  width: 263px;
  height: 153px;
  z-index: 1;
  background-image: url(../images/shape/int-shape.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.intgration-item--one::after,
.intgration-item--two::after {
  top: auto;
  bottom: -70px;
}

.intgration-item--one {
  height: 671px;
  overflow: hidden;
}

.intgration-item--two {
  height: 499px;
  overflow: hidden;
}

.intgration-item--two::before {
  top: -13%;
}

@media (max-width: 1199px) {
  .page-title {
    padding-top: 160px;
  }
}

.page-title-box .sub-title {
  font-weight: 600;
  font-size: 18px;
  display: inline-flex;
  align-items: center;
  margin-bottom: 26px;
  color: var(--color-heading-two);
}

.page-title-box .sub-title img {
  margin-right: 6px;
}

.page-title-box .title {
  font-weight: 700;
  font-size: 55px;
  line-height: 76px;
  color: var(--color-heading-two);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .page-title-box .title {
    font-size: 50px;
    line-height: 65px;
  }
}

@media (max-width: 1199px) {
  .page-title-box .title {
    font-size: 42px;
    line-height: 55px;
  }
}

@media (max-width: 991px) {
  .page-title-box .title {
    font-size: 28px;
    line-height: 42px;
  }
}

.page-title-box .page-update_time {
  font-weight: 600;
  font-size: 18px;
  margin-top: 25px;
  display: inline-block;
  color: var(--color-heading-two);
}

.count-box {
  float: right;
  transform: translateY(-15px);
}

@media (max-width: 991px) {
  .count-box {
    float: none;
  }
}

.count-box .number {
  font-weight: 700;
  font-size: 160px;
  margin-bottom: 7px;
  letter-spacing: -0.01em;
  color: var(--color-primary-two);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .count-box .number {
    font-size: 120px;
  }
}

@media (max-width: 1199px) {
  .count-box .number {
    font-size: 100px;
  }
}

@media (max-width: 767px) {
  .count-box .number {
    font-size: 60px;
  }
}

.count-box .number .suffix {
  font-weight: 250;
}

.count-box .text {
  font-weight: 700;
  font-size: 20px;
  text-transform: uppercase;
  color: var(--color-primary-two);
  font-family: var(--font-heading);
}

.page-title-wrap {
  padding-top: 40px;
  padding-bottom: 43px;
}

.cp-img-slide {
  position: absolute;
  top: 26%;
  right: 12%;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .cp-img-slide {
    right: 2%;
  }
}

@media (max-width: 1199px) {
  .cp-img-slide {
    right: 1%;
  }
}

@media (max-width: 991px) {
  .cp-img-slide {
    top: 29%;
  }
}

@media (max-width: 767px) {
  .cp-img-slide {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    position: unset;
  }
}

.cp-img-slide::before,
.cp-img-slide::after {
  position: absolute;
  top: -155px;
  left: -70px;
  content: '';
  z-index: 1;
  width: 683px;
  height: 213px;
  background: #e4eafc;
  filter: blur(25.7999992371px);
}

.cp-img-slide::after {
  top: auto;
  bottom: -110px;
  left: -70px;
  background: #fff;
}

@media (max-width: 1199px) {
  .cp-img-slide::after {
    bottom: -50px;
  }
}

@media (max-width: 991px) {
  .cp-img-slide::after {
    bottom: -120px;
  }
}

@media (max-width: 767px) {
  .cp-img-slide::after {
    bottom: -180px;
  }
}

.cp-img-inner {
  max-height: 535px;
  overflow: hidden;
}

@media (max-width: 991px) {
  .cp-img-inner {
    max-height: 400px;
  }
}

@media (max-width: 767px) {
  .cp-img-inner {
    gap: 20px;
    overflow: unset;
  }
}

.cp-img-inner .cp-item:not(:last-child) {
  margin-right: 20px;
}

.cp-img-inner .cp-item .xb-img {
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  .cp-img-inner .cp-item .xb-img {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    margin-bottom: 0;
  }
}

.cp-img-inner .cp-item .xb-img img {
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
}

.cp-page-title {
  overflow: hidden;
}

.cp-page-title .page-title-wrap {
  padding-top: 45px;
  padding-bottom: 143px;
}

@media (max-width: 991px) {
  .cp-page-title .page-title-wrap {
    padding-bottom: 80px;
  }
}

@media (max-width: 767px) {
  .cp-page-title .page-title-wrap {
    padding-bottom: 0;
  }
}

.pg-title-wrap {
  padding-top: 36px;
  padding-bottom: 47px;
}

.pg-img-right>img {
  float: right;
  margin-right: -75px;
  margin-top: -111px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .pg-img-right>img {
    margin-right: 0;
  }
}

@media (max-width: 1199px) {
  .pg-img-right>img {
    margin-right: 0;
    margin-top: -29px;
  }
}

@media (max-width: 991px) {
  .pg-img-right>img {
    float: none;
  }
}

.pg-img-right .shape {
  position: absolute;
  top: 0;
  right: 0;
}

.sd-right-img {
  float: right;
  margin-right: -17px;
}

@media (max-width: 991px) {
  .sd-right-img {
    float: none;
    margin-right: 0;
  }
}

.sd-title-wrap {
  padding-top: 10px;
}

@media (max-width: 1199px) {
  .cp-img-inner .cp-item {
    width: 160px;
  }
}

@media (max-width: 991px) {
  .cp-img-inner .cp-item {
    width: 130px;
  }
}

.pg-arrow-shape {
  position: absolute;
  top: -22px;
  right: -2px;
}

.sd-arrow-shape {
  position: absolute;
  top: 31%;
  right: 0;
}

.sd-arrow-shape.style-2 {
  top: 34px;
  right: 15%;
}

.sd-arrow-shape.style-3 {
  top: 41px;
  right: 9%;
}

.roadmap-item {
  padding: 40px 0 54px;
  max-height: 398px;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: var(--color-white);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .roadmap-item {
    padding: 40px 0 0;
  }
}

@media (max-width: 1199px) {
  .roadmap-item {
    max-height: none;
    padding-bottom: 0;
  }
}

.roadmap-item .xb-item--top {
  display: flex;
  align-items: center;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .roadmap-item .xb-item--top img {
    display: none;
  }
}

@media (max-width: 1199px) {
  .roadmap-item .xb-item--top img {
    display: none;
  }
}

.roadmap-item .xb-item--ques {
  margin: 0 20px;
  font-weight: 700;
  font-size: 36px;
  color: var(--color-heading-two);
  font-family: var(--font-heading);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .roadmap-item .xb-item--ques {
    margin: 0 auto;
  }
}

@media (max-width: 1199px) {
  .roadmap-item .xb-item--ques {
    margin: 0 auto;
  }
}

.roadmap-item .xb-item--holder {
  padding: 40px;
}

.roadmap-item .xb-item--year {
  padding: 8.5px 14.5px;
  font-weight: 700;
  font-size: 30px;
  letter-spacing: 0em;
  display: inline-block;
  border: 1px solid #e7e8ec;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  color: var(--color-primary-two);
  font-family: var(--font-heading);
  background: rgba(15, 85, 220, 0.1);
}

.roadmap-item .xb-item--title {
  font-weight: 700;
  font-size: 28px;
  letter-spacing: 0em;
  margin: 25px 0 25px;
  color: var(--color-heading-two);
}

@media (max-width: 1199px) {
  .roadmap-item .xb-item--title {
    font-size: 24px;
  }
}

.roadmap-item .xb-item--content {
  max-width: 529px;
}

.raodmap-slider {
  margin-left: -120px;
  margin-right: -120px;
  padding-bottom: 78px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .raodmap-slider {
    margin-left: -300px;
    margin-right: -300px;
    padding-bottom: 65px;
  }
}

@media (max-width: 767px) {
  .raodmap-slider {
    margin-left: 0;
    margin-right: 0;
    padding-bottom: 40px;
  }
}

.raodmap-slider .swiper-pagination {
  bottom: 19%;
  z-index: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .raodmap-slider .swiper-pagination {
    bottom: 12%;
  }
}

@media (max-width: 767px) {
  .raodmap-slider .swiper-pagination {
    bottom: 11%;
  }
}

.raodmap-slider .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  opacity: .2;
  background: var(--color-white);
}

.raodmap-slider .swiper-pagination .swiper-pagination-bullet:not(:last-child) {
  margin-right: 6px;
}

.raodmap-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
  transform: scale(1.4);
}

.roadmap-shape .shape {
  position: absolute;
}

.roadmap-shape .shape--one {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.roadmap-shape .shape--two {
  left: 50%;
  bottom: 10.7%;
  transform: translateX(-50%);
}

@media (max-width: 1300px) {
  .roadmap-shape .shape--two {
    display: none;
  }
}

.roadmap-shape .shape--three {
  bottom: 18px;
  left: 50%;
  transform: translateX(-50%);
}

.roadmap-button .sa-swiper-btn {
  background: #0f55dc;
  color: var(--color-white);
}

.roadmap-button .sa-swiper-btn:hover {
  background: var(--color-white);
}

.roadmap-button .sa-swiper-btn:hover i {
  color: var(--color-black);
}

@media only screen and (min-width: 1200px) and (max-width: 1300px) {
  .roadmap-pb {
    padding-bottom: 100px;
  }
}

@media (max-width: 767px) {
  .roadmap-pb {
    padding-bottom: 100px;
  }
}

.gallery-wrap {
  flex-wrap: nowrap;
}

.gallery-item:not(:last-child) {
  margin-right: 20px;
}

.gallery-item .img {
  margin-bottom: 20px;
}

.gallery-item .img img {
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
}

.gallery-item .img--1 {
  float: right;
}

.gallery-item .img--5 {
  float: left;
}

.gallery-item--two {
  transform: translateY(30px);
}

.gallery-item--three {
  transform: translateY(-10px);
}

.cp-details-wrap {
  margin-top: 25px;
  padding-right: 220px;
  border-top: 1px solid #E7E8EC;
}

@media (max-width: 1199px) {
  .cp-details-wrap {
    padding-right: 0;
  }
}

.cp-manager_info {
  padding-top: 115px;
}

.cp-manager_info .xb-item--title {
  font-weight: 700;
  font-size: 48px;
  margin-bottom: 40px;
  color: var(--color-heading-two);
}

@media (max-width: 1199px) {
  .cp-manager_info .xb-item--title {
    font-size: 38px;
  }
}

@media (max-width: 767px) {
  .cp-manager_info .xb-item--title {
    font-size: 28px;
  }
}

.cp-manager_info .xb-item--content {
  margin-top: 40px;
  display: inline-block;
  font-size: 18px;
  line-height: 32px;
  letter-spacing: -0.01em;
  color: #494d57;
}

.xb-details-content li {
  font-size: 18px;
  color: #494d57;
  letter-spacing: -0.01em;
}

.xb-details-content li:not(:last-child) {
  margin-bottom: 20px;
}

.xb-details-content li span {
  color: var(--color-heading-two);
}

.xb-details-item {
  margin-top: 60px;
}

.xb-details-item .xb-item--title {
  font-weight: 700;
  font-size: 34px;
  margin-bottom: 35px;
  color: var(--color-heading-two);
}

@media (max-width: 767px) {
  .xb-details-item .xb-item--title {
    font-size: 26px;
  }
}

.xb-details-item .xb-details-content {
  margin-left: 25px;
}

.xb-details-item .xb-details-content li {
  line-height: 32px;
}

.xb-details-item .xb-details-content li:not(:last-child) {
  margin-bottom: 0px;
}

.cp-det-bg {
  background: linear-gradient(360deg, #f6f6f8 0%, #fff 100%);
}

.post_meta {
  gap: 40px;
}

.post_meta .meta_label1 {
  font-weight: 600;
  text-transform: uppercase;
  color: var(--color-primary-two);
  position: relative;
}

.post_meta .meta_label1::before {
  position: absolute;
  content: '';
  top: 50%;
  right: -20px;
  height: 13px;
  width: 2px;
  background: #dadae5;
  transform: translateY(-50%);
}

.post_meta .meta_label {
  color: var(--color-default);
}

.post_meta .meta_label a {
  color: currentColor;
}

.post_meta .meta_icon img {
  transform: translateY(-2px);
}

.post_audio {
  margin-bottom: 40px;
}

.post_audio .audio_play_btn {
  gap: 26px;
  font-weight: 500;
  align-items: center;
  display: inline-flex;
  padding: 5px 30px 5px 5px;
  border: 1px solid #e7e8ec;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  background-color: var(--color-white);
}

.post_audio .audio_play_btn i {
  width: 40px;
  height: 40px;
  flex: 0 0 auto;
  padding-left: 4px;
  border-radius: 100%;
  align-items: center;
  display: inline-flex;
  justify-content: center;
  color: var(--color-white);
  background: var(--color-primary-two);
}

.post_audio .audio_play_btn span {
  color: var(--color-default);
}

.item_details_heading {
  font-weight: 700;
  font-size: 48px;
  line-height: 60px;
  margin: 26px 0 22px;
}

@media (max-width: 991px) {
  .item_details_heading {
    font-size: 32px;
    line-height: 45px;
  }
}

@media (max-width: 767px) {
  .item_details_heading {
    font-size: 28px;
    line-height: 40px;
  }
}

.item_details_info_heading {
  font-weight: 700;
  font-size: 26px;
  line-height: 36px;
  letter-spacing: 0em;
  margin-bottom: 35px;
  color: var(--color-heading-two);
}

.image_block {
  overflow: hidden;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.blog_details_content p {
  margin-bottom: 40px;
  line-height: 28px;
}

.blog-details-video {
  overflow: hidden;
  margin-bottom: 85px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.iconlist_block {
  display: flex;
  flex-direction: column;
  gap: 23px;
  padding-left: 38px;
}

.iconlist_block .iconlist_text {
  font-weight: 500;
}

.numlist_block {
  gap: 15px;
  padding-left: 0;
  margin-top: -23px;
}

.postabmin_block {
  padding: 35px 40px;
  margin: 80px 0;
  border: 1px solid #e7e8ec;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background: var(--color-white);
}

.postabmin_block .admin_image {
  margin-right: 30px;
  display: inline-block;
  width: 150px;
  height: 150px;
  flex: 0 0 auto;
  overflow: hidden;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.postabmin_block .admin_content {
  width: calc(100% - 180px);
}

@media (max-width: 767px) {
  .postabmin_block .admin_content {
    width: 100%;
    margin-top: 30px;
  }
}

.postabmin_block .admin_name {
  font-weight: 700;
  font-size: 20px;
  letter-spacing: 0em;
  color: var(--color-heading-two);
}

.postabmin_block .admin_designation {
  font-size: 14px;
  font-weight: 500;
  margin: 4px 0 15px;
  display: inline-block;
}

.postabmin_block .social_icons_blocks {
  gap: 20px;
}

.postabmin_block .social_icons_blocks li a i {
  opacity: 0.5;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  color: var(--color-primary-two);
}

.postabmin_block .social_icons_blocks li a:hover i {
  opacity: 1;
}

.postabmin_block p {
  margin-bottom: 10px;
}

.social_icons_block {
  gap: 10px;
}

.social_icons_block li a {
  height: 32px;
  width: 32px;
  font-weight: 400;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border: 1px solid #e7e8ec;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  color: var(--color-heading-two);
  background: var(--color-white);
}

.social_icons_block li a svg {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.social_icons_block li a:hover {
  color: var(--color-white);
  border: 1px solid #0f55dc;
  background: var(--color-primary-two);
}

.social_icons_block li a:hover svg {
  filter: brightness(10000%);
}

.post-nav-item {
  padding: 30px 40px;
  display: flex;
  align-items: center;
  gap: 48px;
  justify-content: space-between;
  background: var(--color-white);
  border: 1px solid #e7e8ec;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.post-nav-item:nth-child(1) .xb-item--holder {
  text-align: end;
}

.post-nav-item:nth-child(3) .xb-item--arrow span {
  left: auto;
  right: -10px;
}

.post-nav-item:hover .xb-item--arrow img {
  transform: translateX(-5px);
  filter: brightness(100%);
}

.post-nav-item:hover:nth-child(3) .xb-item--arrow img {
  transform: translateX(5px);
}

.post-nav-item .xb-item--title {
  font-weight: 700;
  font-size: 20px;
  letter-spacing: 0em;
  margin-bottom: 10px;
}

.post-nav-item .xb-item--text {
  display: inline-flex;
  align-items: center;
  gap: 9px;
  font-weight: 500;
  color: var(--color-default);
}

.post-nav-item .xb-item--arrow img {
  z-index: 2;
  position: relative;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  filter: brightness(0%);
}

.post-nav-item .xb-item--arrow span {
  height: 30px;
  width: 30px;
  position: absolute;
  top: -1px;
  left: -10px;
  background: #d7e5f3;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.other_post_nav {
  gap: 40px;
  margin: 62px 0 80px;
}

.other_post_nav .xb-bar i {
  font-size: 22px;
  color: var(--color-heading-two);
}

.item_details-newslatter {
  padding: 60px;
  background: var(--color-white);
  border: 1px solid #e7e8ec;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

@media (max-width: 767px) {
  .item_details-newslatter {
    padding: 30px;
  }

  .item_details-newslatter .xb-item--holder {
    margin-bottom: 30px;
  }
}

.item_details-newslatter .item_details_info_heading {
  margin-bottom: 15px;
}

.item_details-newslatter p {
  margin-bottom: 30px;
}

.item_details-newslatter .xb-item--bell-icon {
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f6f6f8;
  border: 1px solid #e7e8ec;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  transform: translateY(7px);
}

.item_details-newslatter .xb-item--bell-icon i {
  transform-origin: top;
  animation: ring 1.8s ease-out infinite;
}

.item_details-newslatter .xb-item--item-input_field input {
  height: 60px;
  padding: 0 140px 0 30px;
  background: #f6f6f8;
  border: 1px solid #e7e8ec;
  border-radius: 100px;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -ms-border-radius: 100px;
  -o-border-radius: 100px;
}

.item_details-newslatter .xb-item--item-input_field button {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0em;
  padding: 13px 20px;
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  background: var(--color-yellow);
}

.related-blog-title {
  font-size: 40px;
  margin-bottom: 60px;
}

.blog-details_item .xb-item--img {
  overflow: hidden;
  display: inline-block;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.blog-details_item .xb-item--img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blog-details_item .xb-item--meta {
  gap: 40px;
  margin: 20px 0 15px;
}

.blog-details_item .xb-item--meta_label1 {
  font-weight: 600;
  text-transform: uppercase;
  color: var(--color-primary-two);
  position: relative;
}

.blog-details_item .xb-item--meta_label1::before {
  position: absolute;
  content: '';
  top: 50%;
  right: -20px;
  height: 13px;
  width: 2px;
  background: #dadae5;
  transform: translateY(-50%);
}

.blog-details_item .xb-item--det-btn {
  position: relative;
  font-weight: 700;
  font-size: 18px;
  line-height: 12px;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  color: #494d57;
  display: inline-flex;
  align-items: center;
  gap: 15px;
  padding-bottom: 5px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blog-details_item .xb-item--det-btn i {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blog-details_item .xb-item--det-btn::before {
  position: absolute;
  content: '';
  bottom: 0;
  left: 0;
  height: 2px;
  width: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-default);
}

.blog-details_item .xb-item--det-btn:hover {
  color: var(--color-primary-two);
}

.blog-details_item .xb-item--det-btn:hover i {
  transform: translateX(5px);
}

.blog-details_item .xb-item--det-btn:hover::before {
  background: var(--color-primary-two);
}

.blog-details_item .item_details_info_heading {
  margin-bottom: 15px;
}

.blog-details_item .item_details_info_heading a {
  color: currentColor;
}

.blog-details_item:hover .xb-item--img img {
  transform: scale(1.05);
}

.terms-section_inner {
  margin-top: 25px;
  padding-right: 20px;
  border-top: 1px solid #E7E8EC;
}

.item_details_info_title {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 25px;
}

.item-details-widget {
  margin-bottom: 50px;
}

.item-details-widget p {
  font-size: 18px;
  line-height: 32px;
  letter-spacing: -0.01em;
  margin-bottom: 20px;
}

.item-details-widget p a {
  color: currentColor;
  text-decoration: underline;
}

.item-details-widget p .details-link {
  font-weight: 700;
}

.privacy-details {
  padding-left: 20px;
}

.privacy-details li {
  font-weight: 500;
  font-size: 18px;
  line-height: 34px;
  letter-spacing: -0.01em;
}

.pg-pricing-item {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.pg-pricing-item::before {
  position: absolute;
  content: '';
  left: 0;
  top: -5px;
  height: 100%;
  width: 100%;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: var(--color-primary-two);
}

@media (max-width: 991px) {
  .pg-pricing-item.active {
    margin-top: 40px;
  }
}

.pg-pricing-item .xb-item--inner {
  padding: 48px 30px 30px;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  -ms-border-radius: 15px;
  -o-border-radius: 15px;
  background: var(--color-white);
  border: rgba(12, 17, 29, 0.5);
  box-shadow: 0 8px 18px 0 rgba(221, 221, 231, 0.95);
}

.pg-pricing-item .xb-item--icon {
  margin-right: 25px;
}

.pg-pricing-item .xb-item--title {
  font-weight: 700;
  font-size: 24px;
  margin-bottom: 5px;
}

.pg-pricing-item .xb-item--number {
  font-weight: 700;
  font-size: 48px;
  margin-right: 5px;
  letter-spacing: -0.02em;
  display: inline-block;
}

.pg-pricing-item .xb-item--price {
  margin-top: 30px;
}

.pg-pricing-item .xb-item--line {
  height: 1px;
  background: #e3e4e6;
  margin: 30px 0 25px;
}

.pg-pricing-item .xb-item--feature {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0em;
}

.pg-pricing-item .xb-item--list li {
  font-weight: 500;
  margin-top: 20px;
}

.pg-pricing-item .xb-item--list li img {
  margin-right: 4px;
}

.pg-pricing-item .xb-item--list li.deactive {
  color: rgba(73, 77, 87, 0.5);
  text-decoration: line-through;
}

.pg-pricing-item .xb-item--top-text {
  z-index: 1;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 500;
  position: absolute;
  color: var(--color-white);
}

.pg-pricing-item .xb-shape {
  position: absolute;
  left: 2px;
  top: -4px;
}

.pg-pricing-item.active::before {
  top: -40px;
}

.pg-pricing-item:hover {
  transform: translateY(-3px) scale(1.03);
}

.pg-det-btn .cp-btn {
  font-size: 18px;
  margin-top: 40px;
  display: block;
  text-align: center;
  padding: 13px 40px;
}

.pg-det-btn .cp-btn:hover {
  background: var(--color-yellow);
  color: var(--color-heading-two);
}

.xb-pricing-nav {
  padding: 7px;
  display: inline-flex;
  background: #f6f6f8;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}

.xb-pricing-nav .nav-item .nav-link {
  font-size: 14px;
  color: #191B1E;
  font-weight: 700;
  padding: 11px 30px;
  text-transform: uppercase;
  border-radius: 32px;
  -webkit-border-radius: 32px;
  -moz-border-radius: 32px;
  -ms-border-radius: 32px;
  -o-border-radius: 32px;
  font-family: var(--font-heading);
  border: transparent;
  cursor: pointer;
}

.xb-pricing-nav .nav-item .nav-link span {
  font-size: 12px;
  font-weight: 500;
  background-color: #FF5050;
  border-radius: 29px;
  -webkit-border-radius: 29px;
  -moz-border-radius: 29px;
  -ms-border-radius: 29px;
  -o-border-radius: 29px;
  padding: 1px 7px;
  line-height: 1.2;
  color: var(--color-white);
  margin-left: 10px;
}

.xb-pricing-nav .nav-item .nav-link.active {
  border: 1px solid #e7e8ec;
  background: var(--color-white);
  box-shadow: 0 6px 13px 0 rgba(167, 197, 203, 0.39);
}

.casestudy-menu {
  margin: 0 15px;
}

.casestudy-item {
  position: relative;
}

.casestudy-item .casestudy-img {
  position: relative;
  overflow: hidden;
  display: inline-block;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.casestudy-item .casestudy-img:before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: '';
  opacity: 0;
  z-index: 1;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: rgba(0, 0, 0, 0.5);
}

.casestudy-item .casestudy-img img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.casestudy-item .casestudy-img .content_wrap {
  left: 0;
  right: 0;
  bottom: 0;
  gap: 5px;
  z-index: 2;
  padding: 30px;
  display: flex;
  position: absolute;
  flex-direction: column;
  align-items: flex-start;
}

.casestudy-item .casestudy-img .content_wrap .item_title,
.casestudy-item .casestudy-img .content_wrap .item_tag {
  opacity: 0;
  top: 16px;
  line-height: 1;
  display: inline-block;
  position: relative;
  color: var(--color-heading-two);
  background-color: var(--color-white);
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  transform-origin: bottom;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  transform: perspective(500px) rotateX(20deg);
}

.casestudy-item .casestudy-img .content_wrap .item_title {
  font-size: 24px;
  font-weight: 700;
  padding: 16px 20px;
  margin-bottom: 5px;
}

.casestudy-item .casestudy-img .content_wrap .item_tag {
  font-size: 16px;
  font-weight: 600;
  padding: 10px 21px;
  color: var(--color-heading-two);
}

.casestudy-item .xb-overlay {
  z-index: 2;
}

.casestudy-item:hover .casestudy-img::before {
  opacity: 1;
}

.casestudy-item:hover .casestudy-img img {
  transform: scale(1.09) rotate(3deg);
}

.casestudy-item:hover .casestudy-img .content_wrap .item_title,
.casestudy-item:hover .casestudy-img .content_wrap .item_tag {
  top: 0;
  opacity: 1;
  transform: perspective(1000px) rotateX(0deg);
}

.casestudy-item:hover .casestudy-img .content_wrap .item_tag {
  transition-delay: .1s;
}

.sidebar {
  margin-left: 80px;
}

@media (max-width: 1199px) {
  .sidebar {
    margin-left: 40px;
  }
}

@media (max-width: 991px) {
  .sidebar {
    margin-left: 0;
  }
}

.sidebar .sidebar_widget:not(:last-child) {
  margin-bottom: 70px;
}

.sidebar_widget_title {
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 27px;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  color: var(--color-heading-two);
}

.form-group {
  position: relative;
}

.form-group .form-control {
  height: 60px;
  padding-right: 70px;
  background: var(--color-white);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  border: 1px solid #e7e8ec;
}

.form-group .form-control:focus {
  box-shadow: none;
  border: 1px solid var(--color-primary-two);
}

.form-group .search_icon {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 10px 10px 0;
  background: var(--color-primary-two);
}

.recent_post_block .recent_post_item {
  padding: 25px 30px;
  background: var(--color-white);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  border: 1px solid #e7e8ec;
}

.recent_post_block .recent_post_item:not(:last-child) {
  margin-bottom: 30px;
}

.recent_post_block .recent_post_item .post-title {
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 15px;
  line-height: 26px;
  letter-spacing: 0.01em;
}

.recent_post_block .recent_post_item .post-title a {
  color: currentColor;
}

.recent_post_block .recent_post_item span {
  font-weight: 500;
}

.recent_post_block .recent_post_item span img {
  margin-right: 10px;
}

.category_list_block li {
  border-bottom: 1px solid #e7e8ec;
}

.category_list_block a {
  display: flex;
  padding: 12px 0 16px;
  font-size: 20px;
  font-weight: 500;
  align-items: center;
  justify-content: space-between;
}

.category_list_block a span {
  gap: 10px;
  font-weight: 500;
  align-items: center;
  display: inline-flex;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  color: var(--color-default);
}

.category_list_block a span:first-child {
  transform: translateX(-24px);
}

.category_list_block a span:first-child i {
  opacity: 0;
  color: inherit;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  transform: rotate(-45deg);
}

.category_list_block a:hover span {
  color: var(--color-primary-two);
}

.category_list_block a:hover span:first-child {
  transform: translateX(0px);
}

.category_list_block a:hover span:first-child i {
  opacity: 1;
}

.tags_block li {
  display: inline-block;
  margin-bottom: 20px;
  margin-right: 10px;
}

.tags_block li a {
  font-weight: 500;
  font-size: 14px;
  color: #494d57;
  padding: 6px 19px;
  border: 1px solid #e7e8ec;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-white);
}

.tags_block li:hover a {
  color: var(--color-white);
  border: 1px solid #0f55dc;
  background: var(--color-primary-two);
}

/*----------------------------------------*/
/*  03. globel
/*----------------------------------------*/
@media (min-width: 1024px) {

  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1320px;
    padding-left: 15px;
    padding-right: 15px;
  }
}

.sco_agency {
  background-color: var(--color-white);
  color: var(--color-default-two);
}

.sco_agency main {
  background-color: var(--color-white);
}

.cybersecurity {
  background: #010315;
  color: var(--color-white);
}

.cybersecurity main {
  background: #010315;
}

.cybersecurity h1,
.cybersecurity h2,
.cybersecurity h3,
.cybersecurity h4,
.cybersecurity h5,
.cybersecurity h6 {
  font-weight: 700;
  color: var(--color-white);
  text-transform: capitalize;
  font-family: var(--font-heading-two);
}

.help_desk {
  background: #fefaf2;
}

.help_desk main {
  background: #fefaf2;
}

.help_desk h1,
.help_desk h2,
.help_desk h3,
.help_desk h4,
.help_desk h5,
.help_desk h6 {
  font-weight: 600;
  color: var(--color-heading-three);
  font-family: var(--font-heading-three);
}

.cloud-and-devops {
  font-weight: 500;
  background: #f8f6f1;
  color: var(--color-heading-four);
}

.cloud-and-devops main {
  background: #f8f6f1;
}

.cloud-and-devops h1,
.cloud-and-devops h2,
.cloud-and-devops h3,
.cloud-and-devops h4,
.cloud-and-devops h5,
.cloud-and-devops h6 {
  font-weight: 800;
  text-transform: uppercase;
  color: var(--color-heading-four);
  font-family: var(--font-heading-four);
}

.data_analytics {
  background: var(--color-white);
  color: var(--color-heading-four);
  font-family: var(--font-heading-five);
}

.data_analytics main {
  background: var(--color-white);
}

.data_analytics h1,
.data_analytics h2,
.data_analytics h3,
.data_analytics h4,
.data_analytics h5,
.data_analytics h6 {
  font-weight: 600;
  text-transform: capitalize;
  color: var(--color-heading-four);
  font-family: var(--font-heading-five);
}

.bg-white {
  background: var(--color-white);
}

.mxw_1660 {
  max-width: 1660px;
}

.mxw_1820 {
  max-width: 1820px;
}

.pd-70 {
  padding: 0 70px;
}

@media (max-width: 991px) {
  .container.px-60 {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.g-10 {
  margin: 0 -5px;
}

.g-10>* {
  padding-left: 5px;
  padding-right: 5px;
}

.o-hidden {
  overflow: hidden;
}

.bg-yellow {
  background: var(--color-yellow) !important;
  color: var(--color-heading-two) !important;
}

.m-lr {
  margin-left: 160px;
  margin-right: 160px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .m-lr {
    margin-left: 10px;
    margin-right: 10px;
  }
}

@media (max-width: 1199px) {
  .m-lr {
    margin-left: 10px;
    margin-right: 10px;
  }
}

.border-blue::before {
  background: #0f55dc !important;
}

.border-sky::before {
  background: #3cc !important;
}

.clr-white {
  color: #fff !important;
}

.clr-blue {
  background: #0f55dc !important;
}

.clr-sky {
  background: #3cc !important;
}

.clr-orange {
  background: #f65a0b !important;
}

.sec-bg {
  position: relative;
  z-index: 1;
}

.sec-bg::before {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  content: '';
  z-index: -1;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  background: linear-gradient(122deg, rgba(255, 217, 17, 0) 0%, rgba(233, 226, 255, 0.8) 34.54%, #cae1f7 62.5%, rgba(250, 232, 138, 0.56) 100%), radial-gradient(74.51% 50% at 50% 100%, white 24.0899994969%, white 100%);
  background: url(../images/bg/about_bg.png) center center/cover no-repeat;
}

.sec-bg--2::before {
  background-image: url(../images/bg/sec_bg.png);
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}

.cd-sec-shape .shape {
  z-index: -1;
  position: absolute;
}

.cd-sec-shape .shape--one {
  left: 0;
  top: 0;
}

.cd-sec-shape .shape--two {
  left: 0;
  bottom: 0;
}

.cd-sec-shape .shape--three {
  right: 0;
  top: 0;
}

.cd-sec-shape .shape--four {
  right: 0;
  bottom: 0;
}

.tagcloud,
.tags-links {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  margin: -10px;
}

.tagcloud a,
.tags-links a {
  display: block;
  color: #5D6A83;
  border: 1px solid #EEE5E5;
  background-color: var(--color-white);
  min-height: 36px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  text-transform: capitalize;
  text-decoration: none;
  font-weight: 500;
  padding: 4px 17px;
  margin: 7px;
  position: relative;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.tagcloud a:hover,
.tags-links a:hover {
  color: var(--color-white);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.body-overlay {
  background-color: #000000;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 1010;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s linear 0s;
  transition: all 600ms ease;
  -webkit-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease;
}

.tags-links a {
  min-height: 32px;
  padding: 2px 17px;
}

.body-overlay.active {
  opacity: .5;
  visibility: visible;
}

.demo-senior-care .body_wrap {
  background-color: var(--color-white);
  padding-left: 80px;
  padding-right: 80px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .demo-senior-care .body_wrap {
    padding-left: 15px;
    padding-right: 15px;
  }
}

@media (max-width: 1199px) {
  .demo-senior-care .body_wrap {
    padding: 0;
  }
}

.post-tags li {
  font-size: 15px;
  text-transform: capitalize;
  position: relative;
}

.post-tags li:not(:first-child, :last-child) {
  margin-right: 11px;
  padding-right: 15px;
}

.post-tags li:not(:first-child, :last-child)::before {
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 5px;
  height: 5px;
  background-color: var(--color-primary);
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  content: "";
}

.post-tags li span {
  display: inline-block;
  background-color: var(--color-primary);
  padding: 0px 10px;
  line-height: 25px;
  color: var(--color-white);
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  margin-right: 12px;
}

.post-tags li a {
  color: var(--color-black);
}

.post-tags li a:hover {
  color: var(--color-black);
}

.mfp-zoom-in .mfp-content {
  opacity: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transform: scale(0.9);
  -ms-transform: scale(0.9);
  transform: scale(0.9);
}

.mfp-zoom-in.mfp-ready .mfp-content {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

body .mfp-wrap .mfp-container .mfp-content .mfp-close {
  padding: 0;
  right: 0;
  text-align: center;
  top: -36px;
  width: 36px;
  height: 36px;
  -webkit-border-radius: 0;
  -khtml-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  border-radius: 0;
  background: var(--color-white);
  cursor: pointer;
  opacity: 1;
  font-size: 0;
  border: 9px solid transparent;
  position: absolute;
}

body .mfp-wrap .mfp-container .mfp-content .mfp-figure .mfp-close {
  top: 4px;
}

body .mfp-wrap .mfp-container .mfp-content .mfp-close::before,
body .mfp-wrap .mfp-container .mfp-content .mfp-close::after {
  content: '';
  position: absolute;
  height: 2px;
  width: 100%;
  top: 50%;
  left: 0;
  margin-top: -1px;
  transform-origin: 50% 50%;
  -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #222;
}

body .mfp-wrap .mfp-container .mfp-content .mfp-close::before {
  -webkit-transform: rotate(45deg);
  -khtml-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

body .mfp-wrap .mfp-container .mfp-content .mfp-close::after {
  -webkit-transform: rotate(-45deg);
  -khtml-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::before,
body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::after {
  -webkit-transform: rotate(0);
  -khtml-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}

body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::before,
body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::after {
  -webkit-transform: rotate(0);
  -khtml-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}

.mfp-iframe-holder .mfp-content {
  max-width: 1170px;
}

.xb-carousel-inner {
  margin: -30px;
}

.xb-swiper-sliders {
  position: relative;
}

.xb-swiper-container {
  overflow: hidden;
  padding: 30px;
}

.xb-swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

.xb-swiper-slide:not(.swiper-slide-visible) {
  opacity: 0;
}

.xb-swiper-slide {
  -webkit-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -khtml-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -moz-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -ms-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -o-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
}

.z-index-2 {
  z-index: 2;
  position: relative;
}

.xb-overlay {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.xb-overlay-link {
  z-index: 1;
}

.text-12 {
  font-size: 12px;
}

.letter-spacing-0 {
  letter-spacing: 0;
}

@media (max-width: 991px) {
  .margin-none-md {
    margin-bottom: 0;
  }
}

@media (max-width: 767px) {
  .mb-30-xs {
    margin-bottom: 30px;
  }
}

.br-20 {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.z-1 {
  z-index: 1;
}

.z-3 {
  position: relative;
  z-index: 3;
}

.ml-auto {
  margin-left: auto;
}

.align-end {
  align-items: flex-end;
}

.xb-hover-zoom:hover .xb-item--img img {
  -webkit-transform: scale(1.08);
  -ms-transform: scale(1.08);
  transform: scale(1.08);
}

.xb-hover-zoom .xb-item--img {
  overflow: hidden;
}

.xb-hover-zoom .xb-item--img img {
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
}

.xb-law-swiper-arrow .xb-swiper-arrow {
  border: 1px solid rgba(255, 255, 255, 0.12);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.xb-law-swiper-arrow .xb-swiper-arrow.xb-swiper-arrow-next {
  margin-left: 10px;
}

.xb-law-swiper-arrow .xb-swiper-arrow:hover,
.xb-law-swiper-arrow .xb-swiper-arrow.xb-swiper-arrow-next {
  color: var(--color-law);
  background-color: var(--color-white);
}

.xb-law-swiper-arrow-main-color .xb-swiper-arrow {
  background-color: var(--color-law);
  color: var(--color-white);
}

.pagination-style1 {
  bottom: -60px;
}

.pagination-style1 .swiper-pagination-bullet {
  width: 14px;
  height: 14px;
  background-color: #162432;
}

.pagination-style1.style-white .swiper-pagination-bullet {
  background-color: var(--color-white);
}

.text-heading {
  color: var(--color-heading);
}

.text-24 {
  font-size: 24px;
}

.weight-medium {
  font-weight: 500;
}

.xb-blur {
  background: var(--color-primary);
  filter: blur(139px);
  height: 481px;
  width: 481px;
  border-radius: 50%;
  z-index: -2;
}

@media (max-width: 991px) {
  .xb-blur {
    height: 300px;
    width: 300px;
  }
}

@media (max-width: 767px) {
  .xb-blur {
    height: 150px;
    width: 150px;
  }
}

.row.g-150 {
  margin-left: -75px;
  margin-right: -75px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .row.g-150 {
    margin-left: -35px;
    margin-right: -35px;
  }
}

@media (max-width: 1199px) {
  .row.g-150 {
    margin-left: -20px;
    margin-right: -20px;
  }
}

.row.g-150>* {
  padding-left: 75px;
  padding-right: 75px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .row.g-150>* {
    padding-left: 35px;
    padding-right: 35px;
  }
}

@media (max-width: 1199px) {
  .row.g-150>* {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.line_wrap {
  top: 0px;
  bottom: 0px;
  width: 100%;
  display: block;
  position: absolute;
  max-width: 1292px;
  margin: auto;
}

@media only screen and (min-width: 1200px) and (max-width: 1300px) {
  .line_wrap {
    max-width: 1171px;
  }
}

@media (max-width: 1199px) {
  .line_wrap {
    display: none;
  }
}

.line_wrap .xb-line::before,
.line_wrap .xb-line::after {
  position: absolute;
  top: 0;
  left: -1px;
  width: 1px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.18);
  content: "";
  z-index: 2;
}

.line_wrap .xb-line::after {
  left: auto;
  right: 1px;
}

.xb-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.xb-svg svg {
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.hero-style-one {
  z-index: 1;
  min-height: 937px;
  padding-top: 128px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-one {
    max-height: 900px;
  }
}

@media only screen and (max-width: 1200px) {
  .hero-style-one {
    min-height: 810px;
  }
}

@media (max-width: 1199px) {
  .hero-style-one {
    min-height: 750px;
  }
}

@media (max-width: 991px) {
  .hero-style-one {
    min-height: 635px;
  }
}

.hero-style-one::before {
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  z-index: -1;
  background: rgba(17, 17, 18, 0.87);
}

.hero-style-one .xb-hero {
  margin-top: 119px;
}

@media only screen and (max-width: 1200px) {
  .hero-style-one .xb-hero {
    margin-top: 90px;
  }
}

@media (max-width: 1199px) {
  .hero-style-one .xb-hero {
    margin-top: 75px;
  }
}

@media (max-width: 991px) {
  .hero-style-one .xb-hero {
    margin-top: 20px;
  }
}

@media (max-width: 767px) {
  .hero-style-one .xb-hero {
    margin-top: 20px;
  }
}

.hero-style-one .xb-hero .xb-item--title {
  font-weight: 500;
  font-size: 62px;
  margin-bottom: 35px;
  line-height: 80px;
  color: var(--color-white);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-one .xb-hero .xb-item--title {
    font-size: 60px;
    line-height: 75px;
  }
}

@media (max-width: 1199px) {
  .hero-style-one .xb-hero .xb-item--title {
    font-size: 50px;
    line-height: 65px;
  }
}

@media (max-width: 991px) {
  .hero-style-one .xb-hero .xb-item--title {
    font-size: 35px;
    line-height: 50px;
    margin-bottom: 25px;
  }
}

.hero-style-one .xb-hero .xb-item--title span {
  font-weight: 400;
}

.hero-style-one .xb-hero .xb-item--content {
  font-size: 22px;
  line-height: 34px;
  color: var(--color-white);
  display: inline-block;
}

@media only screen and (max-width: 1200px) {
  .hero-style-one .xb-hero .xb-item--content {
    font-size: 20px;
  }
}

@media (max-width: 991px) {
  .hero-style-one .xb-hero .xb-item--content {
    font-size: 18px;
  }
}

.hero-style-one .hero-right_img {
  position: absolute;
  bottom: 0;
  right: 15.7%;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-one .hero-right_img {
    right: 8%;
    max-width: 580px;
  }
}

@media only screen and (max-width: 1200px) {
  .hero-style-one .hero-right_img {
    right: 4%;
    max-width: 540px;
  }
}

@media (max-width: 1199px) {
  .hero-style-one .hero-right_img {
    right: 0%;
    max-width: 490px;
  }
}

@media (max-width: 991px) {
  .hero-style-one .hero-right_img {
    max-width: 380px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero-style-one .hero-right_img {
    margin-left: 80px;
  }
}

@media (max-width: 767px) {
  .hero-style-one .hero-right_img {
    position: unset;
    margin-top: 40px;
  }
}

.hero-style-one .hero-shape .shape {
  position: absolute;
  z-index: -1;
}

.hero-style-one .hero-shape .shape--one {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-style-one .hero-shape .shape--two {
  bottom: 0;
  left: 19px;
}

.hero-style-one .hero-shape .shape--three {
  right: 16px;
  bottom: 0;
}

.hero-style-two {
  min-height: 920px;
}

@media (max-width: 1199px) {
  .hero-style-two {
    padding-top: 150px;
    min-height: 790px;
  }
}

@media (max-width: 991px) {
  .hero-style-two {
    padding-top: 190px;
    padding-bottom: 50px;
  }
}

.hero-style-two .xb-hero {
  margin-top: -55px;
}

.hero-style-two .xb-hero .xb-item--title {
  font-weight: 700;
  font-size: 60px;
  line-height: 72px;
  margin-bottom: 38px;
  color: var(--color-heading-two);
}

@media (max-width: 1199px) {
  .hero-style-two .xb-hero .xb-item--title {
    font-size: 48px;
    line-height: 60px;
  }
}

@media (max-width: 991px) {
  .hero-style-two .xb-hero .xb-item--title {
    font-size: 38px;
    line-height: 50px;
  }
}

.hero-style-two .xb-hero .xb-item--title span {
  color: var(--color-primary-two);
}

.hero-style-two .xb-hero .xb-item--content {
  font-weight: 600;
  font-size: 22px;
  color: #596275;
}

.hero-style-two .xb-hero .xb-item--item {
  margin-top: 40px;
}

.hero-style-two .xb-hero .xb-item--item li {
  font-size: 20px;
  font-weight: 500;
  color: var(--color-heading-two);
}

.hero-style-two .xb-hero .xb-item--item li:not(:last-child) {
  margin-bottom: 14px;
}

.hero-style-two .xb-hero .xb-item--item li i {
  margin-right: 20px;
}

.hero-style-two .hero-right_img {
  margin-top: 15px;
}

.hero-style-two .hero-right_img .img {
  position: absolute;
}

.hero-style-two .hero-right_img .img--one {
  top: 25px;
  left: 17%;
}

@media (max-width: 1199px) {
  .hero-style-two .hero-right_img .img--one {
    top: 20px;
    left: 9%;
    max-width: 200px;
  }
}

@media (max-width: 991px) {
  .hero-style-two .hero-right_img .img--one {
    top: 14%;
  }
}

@media (max-width: 767px) {
  .hero-style-two .hero-right_img .img--one {
    top: 9%;
    left: 0;
    max-width: 150px;
  }
}

.hero-style-two .hero-right_img .img--two {
  right: -28%;
  bottom: 12%;
  animation: updown-3 3s linear infinite;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-two .hero-right_img .img--two {
    right: -3%;
    bottom: 0%;
  }
}

@media (max-width: 1199px) {
  .hero-style-two .hero-right_img .img--two {
    right: 0;
    bottom: -3%;
    max-width: 180px;
  }
}

@media (max-width: 767px) {
  .hero-style-two .hero-right_img .img--two {
    max-width: 140px;
  }
}

.hero-style-two .xb-shape {
  position: absolute;
  left: 50%;
  bottom: 18%;
}

.hero-style-three {
  min-height: 1010px;
  position: relative;
  z-index: 1;
}

@media (max-width: 1199px) {
  .hero-style-three {
    min-height: 900px;
  }
}

@media (max-width: 991px) {
  .hero-style-three {
    padding-top: 70px;
  }
}

@media (max-width: 767px) {
  .hero-style-three {
    min-height: 750px;
  }
}

.hero-style-three::before {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  content: "";
}

.hero-style-three .xb-hero .xb-item--holder {
  padding: 0 107px 0 75px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
  position: relative;
  flex-wrap: wrap;
  gap: 30px;
}

@media (max-width: 1199px) {
  .hero-style-three .xb-hero .xb-item--holder {
    padding: 0;
  }
}

@media (max-width: 767px) {
  .hero-style-three .xb-hero .xb-item--holder {
    justify-content: center;
  }
}

.hero-style-three .xb-hero .xb-item--title {
  font-weight: 500;
  font-size: 160px;
  letter-spacing: 0.2em;
  margin-bottom: 16px;
  display: inline-block;
  text-transform: uppercase;
  background: linear-gradient(180deg, #fff 0%, #292152 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 1199px) {
  .hero-style-three .xb-hero .xb-item--title {
    font-size: 120px;
  }
}

@media (max-width: 991px) {
  .hero-style-three .xb-hero .xb-item--title {
    font-size: 92px;
  }
}

@media (max-width: 767px) {
  .hero-style-three .xb-hero .xb-item--title {
    font-size: 50px;
    letter-spacing: 2px;
  }
}

.hero-style-three .xb-hero .xb-item--content {
  font-weight: 500;
  font-size: 32px;
  line-height: 45px;
  max-width: 588px;
  text-align: start;
  text-transform: capitalize;
}

@media (max-width: 767px) {
  .hero-style-three .xb-hero .xb-item--content {
    font-size: 26px;
    line-height: 40px;
    text-align: center;
  }
}

.hero-style-three .xb-btn a span i {
  margin-left: 85px;
}

.hero-style-three .hero-btn a {
  padding: 26px 36px 26px 50px;
}

.hero-style-three .hero-btn a span i {
  margin-left: 85px;
}

.hero-style-three .xb-shape .shape {
  position: absolute;
  z-index: 1;
}

.hero-style-three .xb-shape .shape--one {
  bottom: -5px;
  left: 0;
  right: 0;
}
.hero-style-three .xb-shape .shape--one img{
  width: 100%;
}

.hero-style-three .xb-shape .shape--two {
  bottom: 12%;
  left: 49%;
  transform: translateX(-50%);
}

@media (max-width: 991px) {
  .hero-style-three .xb-shape .shape--two {
    bottom: 5%;
    left: 47%;
  }
}

.hero-style-three .xb-shape .shape--two img {
  animation: updown-3 5s linear infinite;
}

.hero-style-three .xb-shape .shape--three {
  top: 5%;
  left: 0;
}

.hero-style-three .xb-shape .shape--four {
  top: 5%;
  right: 0;
}

.hero-style-three .xb-shape .shape--five {
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
}

.hero-style-four .xb-hero .xb-item--title {
  font-weight: 700;
  font-size: 88px;
  line-height: 96px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-four .xb-hero .xb-item--title {
    font-size: 80px;
    line-height: 92px;
  }
}

@media (max-width: 1199px) {
  .hero-style-four .xb-hero .xb-item--title {
    font-size: 56px;
    line-height: 75px;
  }
}

@media (max-width: 767px) {
  .hero-style-four .xb-hero .xb-item--title {
    font-size: 42px;
    line-height: 60px;
  }
}

.hero-style-four .xb-hero .xb-item--content {
  font-size: 22px;
  font-weight: 600;
  color: #596275;
  display: inline-block;
  margin: 27px 0 35px;
}

.hero-style-four .xb-hero .xb-item--item li {
  font-size: 20px;
  font-weight: 500;
  color: var(--color-heading-three);
}

.hero-style-four .xb-hero .xb-item--item li:not(:last-child) {
  margin-bottom: 16px;
}

.hero-style-four .xb-hero .xb-item--item li span {
  margin-right: 5px;
}

.hero-style-four .hero-right_img {
  margin-right: -107px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-four .hero-right_img {
    margin-right: 0;
    margin-left: -80px;
  }
}

@media (max-width: 1199px) {
  .hero-style-four .hero-right_img {
    margin-right: 0;
    margin-left: -30px;
  }
}

.hero-style-four .hero-right_img .xb-img .img {
  position: absolute;
}

.hero-style-four .hero-right_img .xb-img .img--one {
  top: 23%;
  right: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-four .hero-right_img .xb-img .img--one {
    top: 29%;
    right: -5px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1300px) {
  .hero-style-four .hero-right_img .xb-img .img--one {
    top: 40%;
    right: 0;
    max-width: 200px;
  }
}

@media (max-width: 1199px) {
  .hero-style-four .hero-right_img .xb-img .img--one {
    top: 33%;
    right: 0;
    max-width: 200px;
  }
}

@media (max-width: 991px) {
  .hero-style-four .hero-right_img .xb-img .img--one {
    max-width: 300px;
    right: auto;
    left: 44%;
  }
}

@media (max-width: 767px) {
  .hero-style-four .hero-right_img .xb-img .img--one {
    max-width: 150px;
    right: 0;
    left: auto;
    top: auto;
    bottom: 23%;
  }
}

.hero-style-four .hero-right_img .xb-img .img--two {
  right: 21%;
  bottom: 10px;
}

@media (max-width: 767px) {
  .hero-style-four .hero-right_img .xb-img .img--two {
    bottom: 10px;
    max-width: 180px;
    right: auto;
    left: 10%;
  }
}

@media (max-width: 1199px) {
  .hero-right-image {
    max-width: 400px;
  }
}

@media (max-width: 767px) {
  .hero-right-image {
    max-width: 300px;
  }
}

.hero-style-five .xb-hero {
  transform: translateY(-20px);
}

.hero-style-five .xb-hero .xb-item--title {
  font-size: 62px;
  line-height: 85px;
  color: #00b59f;
  letter-spacing: -0.02em;
  margin-bottom: 20px;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-five .xb-hero .xb-item--title {
    font-size: 52px;
    line-height: 70px;
  }
}

@media (max-width: 1199px) {
  .hero-style-five .xb-hero .xb-item--title {
    font-size: 42px;
    line-height: 60px;
  }
}

@media (max-width: 991px) {
  .hero-style-five .xb-hero .xb-item--title {
    font-size: 34px;
    line-height: 52px;
  }
}

.hero-style-five .xb-hero .xb-item--title span {
  color: var(--color-heading-four);
}

.hero-style-five .xb-hero .xb-item--content {
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  text-transform: capitalize;
  color: var(--color-heading-four);
}

.hero-style-five .hero-right_img {
  display: flex;
  justify-content: flex-end;
  margin-right: -14%;
  position: relative;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-five .hero-right_img {
    margin-right: 0;
  }
}

@media (max-width: 1199px) {
  .hero-style-five .hero-right_img {
    margin-right: 0;
  }
}

.hero-style-five .xb-shape .shape {
  z-index: -1;
  position: absolute;
}

.hero-style-five .xb-shape .shape--one {
  bottom: 0;
  right: 0;
}

.hero-style-five .xb-shape .shape--two {
  height: 188px;
  width: 188px;
  top: 42%;
  left: -111px;
  background: #eeece8;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.hero-style-five .xb-shape .shape--three {
  height: 20px;
  width: 20px;
  left: 60px;
  top: 60%;
  background: #eeece8;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}

.cd-hero-shape {
  position: absolute;
  top: 17%;
  right: 12%;
}

.hero-style-six {
  min-height: 1006px;
  background: linear-gradient(360deg, rgba(19, 55, 187, 0) 0%, #1337bb 65%);
  z-index: 1;
}

@media (max-width: 1199px) {
  .hero-style-six {
    min-height: 800px;
  }
}

@media (max-width: 991px) {
  .hero-style-six {
    min-height: 680px;
    padding-top: 60px;
  }
}

.hero-style-six .xb-hero {
  padding-top: 93px;
}

.hero-style-six .xb-hero .xb-item--title {
  font-size: 72px;
  line-height: 90px;
  margin-bottom: 30px;
  color: var(--color-white);
}

@media (max-width: 1199px) {
  .hero-style-six .xb-hero .xb-item--title {
    font-size: 50px;
    line-height: 65px;
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .hero-style-six .xb-hero .xb-item--title {
    font-size: 36px;
    line-height: 50px;
  }
}

.hero-style-six .xb-hero .xb-item--content {
  font-weight: 500;
  font-size: 24px;
  line-height: 40px;
  max-width: 545px;
  color: var(--color-white);
}

.hero-style-six .xb-shape {
  position: absolute;
  top: 0;
  right: 219px;
  z-index: -1;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .hero-style-six .xb-shape {
    right: 20px;
  }
}

@media (max-width: 1199px) {
  .hero-style-six .xb-shape {
    right: 0;
    max-width: 480px;
  }
}

@media (max-width: 991px) {
  .hero-style-six .xb-shape {
    max-width: 407px;
  }
}

@media (max-width: 767px) {
  .hero-style-six .xb-shape {
    display: none;
  }
}

.sc_hero_video {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  opacity: .5;
  height: 100%;
}

.sc_hero_video video {
  width: 100%;
  height: 100%;
}

.banner-scroll-down {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 15%;
}

.banner-scroll-down a {
  color: var(--color-primary-two);
  display: block;
  width: 30px;
  height: 50px;
  text-align: center;
}

.banner-scroll-down a span {
  display: block;
  width: 15px;
  height: 15px;
  border-bottom: 2px solid var(--color-primary-two);
  border-right: 2px solid var(--color-primary-two);
  transform: rotate(45deg);
  margin: -10px 0 0 8px;
  animation: animate 2s infinite;
}

.banner-scroll-down a span:nth-child(2) {
  animation-delay: -0.2s;
}

.banner-scroll-down a span:nth-child(3) {
  animation-delay: -0.4s;
}

.banner-shape-wrap img {
  position: absolute;
}

.banner-shape-wrap img:nth-child(1) {
  left: 107px;
  bottom: 134px;
}

.banner-shape-wrap img:nth-child(2) {
  right: 107px;
  bottom: 120px;
}

@keyframes animate {
  0% {
    opacity: 0;
    transform: rotate(45deg) translate(-15px, -15px);
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: rotate(45deg) translate(15px, 15px);
  }
}

@keyframes alltuchtopdown {
  0% {
    transform: rotateX(0deg) translateY(0px);
  }

  50% {
    transform: rotateX(0deg) translateY(-30px);
  }

  100% {
    transform: rotateX(0deg) translateY(0px);
  }
}

.alltuchtopdown {
  -webkit-animation-name: alltuchtopdown;
  animation-name: alltuchtopdown;
  -webkit-animation-duration: 6s;
  animation-duration: 6s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

/*----------------------------------------*/
/*  14. cursor
/*----------------------------------------*/
.xb-cursor,
.xb-cursor-section {
  visibility: hidden;
  position: fixed;
  pointer-events: none;
  z-index: 999999;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: transform 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: transform 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: transform 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: transform 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: transform 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  pointer-events: none;
  top: 0;
  left: 0;
}

.xb-cursor .xb-cursor-wrapper,
.xb-cursor-section .xb-cursor-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.xb-cursor .xb-cursor--follower,
.xb-cursor-section .xb-cursor--follower {
  position: absolute;
  background-color: var(--color-primary);
  opacity: .25;
  border-radius: 100%;
  width: 1.5rem;
  height: 1.5rem;
  -webkit-transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), background-color 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), background-color 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), background-color 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), background-color 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), background-color 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s cubic-bezier(0.165, 0.84, 0.44, 1), background-color 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.xb-cursor.style-2 .xb-cursor--follower {
  background-color: var(--color-primary-2);
}

.xb-cursor .xb-cursor--label,
.xb-cursor-section .xb-cursor--label {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 15px;
  transform: scale(0);
  will-change: transform, opacity;
  -webkit-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.xb-cursor .xb-cursor--drap,
.xb-cursor-section .xb-cursor--drap {
  line-height: 87px;
  text-align: center;
  width: 87px;
  height: 87px;
  background-color: #000;
  font-size: 0;
  -webkit-border-radius: 87px;
  -khtml-border-radius: 87px;
  -moz-border-radius: 87px;
  -ms-border-radius: 87px;
  -o-border-radius: 87px;
  border-radius: 87px;
  -webkit-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  transform: scale(0);
  will-change: transform, opacity;
  color: #fff;
}

.xb-cursor .xb-cursor--drap:before,
.xb-cursor-section .xb-cursor--drap:before,
.xb-cursor .xb-cursor--drap:after,
.xb-cursor-section .xb-cursor--drap:after {
  content: "\f10b";
  font-family: caseicon;
  font-size: 10px;
  display: inline-flex;
}

.xb-cursor .xb-cursor--drap:before,
.xb-cursor-section .xb-cursor--drap:before {
  -webkit-transform: scaleX(-1);
  -khtml-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  transform: scaleX(-1);
}

.xb-cursor .xb-cursor--drap:after,
.xb-cursor.is-enabled,
.xb-cursor-section.is-enabled {
  visibility: visible;
}

.xb-cursor.is-hidden .xb-cursor-wrapper,
.xb-cursor-section.is-hidden .xb-cursor-wrapper {
  transform: scale(0) !important;
}

.xb-cursor.is-mouse-down .xb-cursor-wrapper,
.xb-cursor-section.is-mouse-down .xb-cursor-wrapper {
  transform: scale(0.8);
}

.xb-cursor.is-active .xb-cursor--follower,
.xb-cursor-section.is-active .xb-cursor--follower {
  -webkit-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  -khtml-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  -ms-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  -o-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.12);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.12);
  -webkit-transform: scale(4);
  -khtml-transform: scale(4);
  -moz-transform: scale(4);
  -ms-transform: scale(4);
  -o-transform: scale(4);
  transform: scale(4);
  opacity: 0;
}

.breadcrumb {
  padding-top: 100px;
  padding-bottom: 100px;
  min-height: 450px;
  background-position: 90% center;
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .breadcrumb {
    min-height: 370px;
  }
}

@media (max-width: 1199px) {
  .breadcrumb {
    min-height: 330px;
    background-position: 75% center;
  }
}

@media (max-width: 767px) {
  .breadcrumb {
    padding-top: 80px;
    padding-bottom: 80px;
    background-position: 45% center;
    min-height: 260px;
  }
}

.breadcrumb__title {
  font-size: 54px;
  line-height: 65px;
  letter-spacing: -0.02em;
  margin-bottom: 15px;
  color: var(--color-white);
}

@media (max-width: 767px) {
  .breadcrumb__title {
    font-size: 34px;
    line-height: 45px;
    letter-spacing: -0.44px;
    margin-bottom: 5px;
  }
}

.breadcrumb__list {
  display: inline-block;
}

.breadcrumb__list li {
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.2px;
  display: inline-block;
  color: var(--color-white);
  position: relative;
  text-transform: capitalize;
}

.breadcrumb__list li+.breadcrumb-item::before {
  content: "|";
  color: #fff;
}

@media (max-width: 767px) {
  .breadcrumb__list li {
    font-size: 16px;
  }
}

.breadcrumb__list li a {
  color: rgba(221, 221, 221, 0.5);
}

.thm-btn {
  z-index: 1;
  font-size: 20px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: .02em;
  padding: 10px 20px;
  border-style: none;
  align-self: center;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-transform: inherit;
  color: var(--color-white);
  background: var(--color-primary);
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 767px) {
  .thm-btn {
    font-size: 16px;
  }
}

.thm-btn .white-icon i {
  color: var(--color-white);
}

.thm-btn--fill_icon {
  padding: 24px 0;
  overflow: hidden;
  color: var(--color-white);
  background: var(--color-primary);
}

.thm-btn--fill_icon .xb-item--hidden-text {
  visibility: hidden;
  margin-right: 6.8rem;
}

@media (max-width: 767px) {
  .thm-btn--fill_icon .xb-item--hidden-text {
    margin-right: 9rem;
  }
}

.thm-btn--fill_icon .xb-item--holder {
  left: 40px;
  position: absolute;
  width: 100%;
  white-space: nowrap;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  display: inline-block;
}

.thm-btn--fill_icon .xb-item--icon {
  height: 44px;
  width: 44px;
  margin: 0px 23px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  background: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--fill_icon .xb-item--icon i {
  color: var(--color-heading);
  transform: rotate(-45deg);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--fill_icon .xb-item--text {
  font-size: 20px;
  line-height: 16px;
  display: inline;
  letter-spacing: -0.42px;
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--fill_icon:hover {
  background: var(--color-heading);
}

.thm-btn--fill_icon:hover .xb-item--holder {
  transform: translateX(-74%);
}

.thm-btn--fill_icon:hover .xb-item--icon i {
  transform: rotate(0deg);
}

.thm-btn--fill_icon:hover .xb-item--text {
  color: var(--color-white);
}

.thm-btn--white_icon {
  font-weight: 700;
  background: var(--color-white);
}

.thm-btn--white_icon .xb-item--icon {
  background: var(--color-primary);
}

.thm-btn--white_icon .xb-item--icon i {
  color: var(--color-white);
}

.thm-btn--white_icon .xb-item--text {
  color: var(--color-heading);
}

.thm-btn--white_icon:hover {
  background: var(--color-primary);
}

.thm-btn--white_icon:hover .xb-item--icon {
  background-color: var(--color-white);
}

.thm-btn--white_icon:hover .xb-item--icon i {
  color: var(--color-heading);
}

.thm-btn--white_icon:hover .xb-item--text {
  color: var(--color-white);
}

.thm-btn--strock {
  background: transparent;
  border: 1px solid rgba(17, 17, 18, 0.1);
}

.thm-btn--strock .xb-item--hidden-text {
  margin-right: 8.5rem;
}

@media (max-width: 767px) {
  .thm-btn--strock .xb-item--hidden-text {
    margin-right: 10.5rem;
  }
}

.thm-btn--strock .xb-item--icon {
  background: var(--color-heading);
}

.thm-btn--strock .xb-item--icon i {
  color: var(--color-white);
}

.thm-btn--strock .xb-item--text {
  color: var(--color-heading);
}

.thm-btn--strock:hover {
  background: var(--color-primary);
}

.thm-btn--strock:hover .xb-item--holder {
  transform: translateX(-76%);
}

.thm-btn--strock:hover .xb-item--icon {
  background: var(--color-white);
}

.thm-btn--strock:hover .xb-item--icon i {
  color: var(--color-heading);
}

.thm-btn--strock:hover .xb-item--text {
  color: var(--color-white);
}

.thm-btn--data {
  padding: 34px 0;
  background: var(--color-white);
  font-family: var(--font-heading-five);
  box-shadow: 0 7px 8px 0 rgba(6, 19, 39, 0.01);
}

.thm-btn--data .xb-item--text {
  font-weight: 700;
  color: #2042BF;
  letter-spacing: 0em;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--data .xb-item--holder {
  left: 57px;
}

.thm-btn--data .xb-item--icon {
  height: 30px;
  width: 30px;
  margin: 0px 17px;
  background: #2042BF;
}

.thm-btn--data .xb-item--icon i {
  transform: rotate(0deg);
  color: var(--color-white);
}

.thm-btn--data .xb-item--hidden-text {
  margin-right: 8.5rem;
}

@media (max-width: 767px) {
  .thm-btn--data .xb-item--hidden-text {
    margin-right: 10.5rem;
  }
}

.thm-btn--data .xb-item--text1 {
  opacity: 1;
}

.thm-btn--data .xb-item--text2 {
  opacity: 0;
}

.thm-btn--data:hover {
  background: #1438bc;
}

.thm-btn--data:hover .xb-item--holder {
  transform: translateX(-70%);
}

.thm-btn--data:hover .xb-item--text {
  color: var(--color-white);
}

.thm-btn--data:hover .xb-item--text1 {
  opacity: 0;
}

.thm-btn--data:hover .xb-item--text2 {
  opacity: 1;
}

.thm-btn--data_blue {
  background: #1438bc;
}

.thm-btn--data_blue .xb-item--text {
  color: var(--color-white);
}

.thm-btn--data_blue:hover {
  background: var(--color-white);
}

.thm-btn--data_blue:hover .xb-item--text {
  color: #1438bc;
}

.thm-btn--data-strock {
  border: 1px solid #1438bc;
}

.thm-btn--data-strock_white {
  background: transparent;
  border: 1px solid var(--color-white);
}

.thm-btn--data-strock_white .xb-item--text {
  color: var(--color-white);
}

.thm-btn--data-strock_white:hover {
  background: var(--color-white);
}

.thm-btn--data-strock_white:hover .xb-item--text {
  color: #1438bc;
}

.thm-btn--header {
  font-size: 18px;
  font-weight: 700;
  overflow: hidden;
  border: none;
  padding: 16.5px 25.7px;
  color: var(--color-heading);
  background: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--header span {
  margin-left: 18px;
}

.thm-btn--header span svg path {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--header:hover {
  color: var(--color-white);
}

.thm-btn--header:hover svg path {
  fill: var(--color-white);
}

.thm-btn--aso {
  font-weight: 700;
  padding: 27px 40px;
  overflow: hidden;
  color: var(--color-white);
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-primary-two);
}

.thm-btn--aso::before {
  position: absolute;
  content: '';
  z-index: -1;
  height: 200%;
  width: 200%;
  left: 50%;
  bottom: -200%;
  transform: translateX(-50%);
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-yellow);
}

.thm-btn--aso img {
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.thm-btn--aso:hover {
  color: var(--color-heading-two);
  transform: translateY(-2px);
}

.thm-btn--aso:hover::before {
  bottom: -70%;
}

.thm-btn--aso:hover img {
  filter: grayscale(1) brightness(0);
}

.thm-btn--aso_yellow {
  background: var(--color-yellow);
  color: var(--color-heading-two);
}

.thm-btn--aso_yellow:hover {
  color: var(--color-white);
}

.thm-btn--aso_yellow:hover::before {
  background: var(--color-primary-two);
}

.thm-btn--aso_white {
  background: var(--color-yellow);
  color: var(--color-heading-two);
}

.thm-btn--aso_white::before {
  background: var(--color-white);
}

.thm-btn--aso_white:hover::before {
  background: var(--color-white);
}

.thm-btn--aso_black {
  background: var(--color-black);
}

.thm-btn--header-black {
  padding: 15.5px 19.5px;
  background-color: var(--color-heading-two);
}

.thm-btn--header-black img {
  margin-left: 18px;
}

.thm-btn--border {
  padding: 0;
  padding-bottom: 20px;
  border-radius: 0;
  background: transparent;
}

.thm-btn--border span {
  height: 25px;
  width: 25px;
  right: 0;
  top: 25%;
}

.thm-btn--border:hover {
  color: var(--color-white);
}

.thm-btn--border::before {
  position: absolute;
  content: '';
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 4px;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  background-color: #fff;
}

.thm-btn--three {
  font-size: 18px;
  letter-spacing: 0em;
  position: relative;
  padding: 20px 80px 20px 22px;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background: var(--color-primary-three);
}

.thm-btn--three span {
  width: 48px;
  height: 28px;
  right: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  background: #e5a0e3;
  overflow: hidden;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.thm-btn--three span .img--one {
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  transform: translateX(8px);
}

.thm-btn--three span .img--two {
  opacity: 0;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  transform: translateX(-43px);
}

.thm-btn--three::before {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
  content: '';
  height: 100%;
  width: 0;
  opacity: 0;
  border-radius: inherit;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  background: #000;
}

.thm-btn--three:hover {
  color: #fff;
}

.thm-btn--three:hover::before {
  width: 100%;
  opacity: 1;
}

.thm-btn--three:hover span .img--one {
  opacity: 0;
  transform: translateX(50px);
}

.thm-btn--three:hover span .img--two {
  opacity: 1;
  transform: translateX(-8px);
}

.thm-btn--black {
  background: var(--color-heading-three);
}

.thm-btn--black:hover::before {
  background: var(--color-primary-three);
}

.thm-btn--devops {
  font-size: 16px;
  font-weight: 800;
  letter-spacing: 0em;
  overflow: hidden;
  text-transform: uppercase;
  padding: 27px 70px 23px 70px;
  background: var(--color-primary-four);
  font-family: var(--font-heading-four);
}

@media (max-width: 767px) {
  .thm-btn--devops {
    padding: 27px 25px 23px;
  }
}

.thm-btn--devops::before {
  position: absolute;
  content: '';
  width: 101%;
  height: 0;
  left: 0;
  right: auto;
  top: 50%;
  z-index: -1;
  transform: translateY(-50%);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-yellow);
}

.thm-btn--devops:hover {
  color: var(--color-heading-two);
}

.thm-btn--devops:hover::before {
  height: 100%;
}

.thm-btn--devops_yellow {
  background: #ffd00e !important;
  color: #111 !important;
}

.thm-btn--devops_yellow:hover {
  color: var(--color-white) !important;
}

.thm-btn--devops_yellow:hover::before {
  background: var(--color-primary);
}

.blc-btn {
  z-index: 1;
  font-weight: 500;
  font-size: 18px;
  padding: 18px 29px 18px 40px;
  position: relative;
  display: inline-block;
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blc-btn span i {
  margin-left: 45px;
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blc-btn .btn-shape,
.blc-btn .btn-shape2 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.blc-btn .btn-shape svg,
.blc-btn .btn-shape2 svg {
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.blc-btn .btn-shape2 {
  opacity: 0;
}

.blc-btn:hover .btn-shape {
  opacity: 0;
}

.blc-btn:hover .btn-shape2 {
  opacity: 1;
}

.blc-btn:hover span i {
  transform: translateX(5px);
}

.xb-btn-circle {
  font-family: var(--font-heading-four);
  font-weight: 800;
  font-size: 24px;
  line-height: 41px;
  text-transform: uppercase;
  text-align: center;
  height: 447px;
  width: 447px;
  letter-spacing: -0.01em;
  color: var(--color-white);
  background: var(--color-heading-four);
  display: flex;
  align-items: center;
  justify-content: center;
  display: inline-flex;
  flex-direction: column;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

@media (max-width: 767px) {
  .xb-btn-circle {
    height: 300px;
    width: 300px;
  }
}

.xb-btn-circle-dot {
  position: absolute;
  bottom: -70px;
  left: 130px;
  width: 79px;
  height: 79px;
  line-height: 20px;
  background-color: #ffd00e;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-transition: 0.7s;
  -o-transition: 0.7s;
  transition: 0.7s;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
}

.xb-btn-circle-arrow {
  font-size: 40px;
  margin-bottom: 40px;
  transform: rotate(-45deg);
}

.xb-btn-circle:hover .xb-btn-circle-dot {
  height: 1000px;
  width: 1000px;
  left: 100px;
  top: 100px;
}

.btn-effect_1 {
  position: relative;
  overflow: hidden;
}

.btn-effect_1:before {
  left: auto;
  right: 0;
  height: 100%;
  width: 0;
  content: '';
  z-index: -1;
  position: absolute;
  border-radius: inherit;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-primary);
}

.btn-effect_1:hover::before {
  width: 100%;
  left: 0;
  right: auto;
}

.btn-effect_2 {
  position: relative;
  overflow: hidden;
}

.btn-effect_2:before {
  top: 0;
  left: auto;
  right: 0;
  height: 100%;
  width: 0;
  content: '';
  z-index: -1;
  position: absolute;
  border-radius: inherit;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-heading);
}

.btn-effect_2:hover::before {
  width: 100%;
  left: 0;
  right: auto;
}

.cp-btn {
  font-weight: 700;
  font-size: 20px;
  padding: 23px 40px;
  letter-spacing: 0em;
  color: var(--color-white);
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  background: var(--color-primary-two);
  display: inline-block;
}

.cp-btn i {
  margin-left: 15px;
  font-weight: 400;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  transform: rotate(-45deg);
}

.cp-btn:hover i {
  transform: rotate(-45deg) translateX(5px);
}

.cp-det-btn .cp-btn {
  color: var(--color-heading-two);
  background: var(--color-yellow);
}

.cp-det-btn .cp-btn i {
  color: var(--color-heading-two);
}

.cp-det-btn .cp-btn:hover {
  color: var(--color-white);
  background: var(--color-primary-two);
}

.cp-det-btn .cp-btn:hover i {
  color: var(--color-white);
}

/*----------------------------------------*/
/*  27. backtotop
/*----------------------------------------*/
.xb-backtotop {
  right: 30px;
  z-index: 999;
  bottom: 20px;
  position: fixed;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
  visibility: hidden;
}

.xb-backtotop.active {
  bottom: 40px;
  opacity: 1;
  visibility: visible;
}

.xb-backtotop.style-2 .scroll {
  background-color: var(--color-primary);
}

.xb-backtotop.style-3 .scroll {
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
}

.xb-backtotop.style-4 .scroll {
  background: var(--color-primary-three);
}

.xb-backtotop.style-5 .scroll {
  background: var(--color-primary-four);
}

.xb-backtotop.style-6 .scroll {
  background: #2042BF;
}

.xb-backtotop .scroll {
  z-index: 1;
  width: 40px;
  height: 40px;
  display: block;
  background-color: var(--color-primary-two);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: var(--color-white);
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  font-size: 14px;
}

.xb-backtotop .scroll:hover {
  margin-bottom: 4px;
}

.xb-backtotop.style-ins .scroll {
  background: var(--color-ins);
  color: #052328;
}

.xb-backtotop.style-law .scroll {
  background: var(--color-law);
}

.xb-backtotop.style-advisor .scroll {
  background: var(--color-advisor);
}

.xb-backtotop.style-marketing .scroll {
  background: var(--color-marketing);
}

.xb-backtotop.style-business .scroll {
  background: var(--color-business);
  color: #03080B;
}

/*----------------------------------------*/
/*  19. mobile-menu
/*----------------------------------------*/
.xb-hamburger-menu {
  display: none;
}

@media (max-width: 991px) {
  .xb-hamburger-menu {
    display: block;
  }
}

.xb-nav-mobile-button {
  position: relative;
  cursor: pointer;
  font-size: 23px;
  color: var(--color-dark);
}

.xb-header-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  opacity: 0;
  visibility: hidden;
}

.xb-header-menu {
  position: fixed;
  height: 100vh;
  width: 300px;
  top: 0;
  left: 0;
  bottom: 0;
  margin: 0;
  z-index: 1010;
  overflow: hidden;
  -webkit-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -webkit-transform: translateX(-100%);
  -khtml-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  visibility: hidden;
  background-color: #fff;
  overflow-y: scroll;
}

.xb-header-menu.active {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateX(0%);
  -khtml-transform: translateX(0%);
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -o-transform: translateX(0%);
  transform: translateX(0%);
}

.xb-header-menu.active+.xb-header-menu-backdrop {
  opacity: 1;
  visibility: visible;
}

.xb-header-menu-scroll {
  padding: 50px 25px 40px;
}

.xb-header-menu-scroll .xb-close {
  position: absolute;
  top: 0;
  right: 0;
}

.xb-header-menu-scroll .xb-menu-primary>li {
  padding-left: 0;
  padding-right: 0;
}

.xb-header-menu-scroll .xb-menu-primary>li>a .left-icon {
  margin-right: 4px;
}

.xb-header-menu-scroll .xb-menu-primary>li .elementor-section {
  max-width: 100%;
}

.xb-header-menu-scroll .xb-menu-primary>li .elementor-container {
  margin: 10px 0 !important;
}

.xb-header-menu-scroll .xb-menu-primary>li .sub-menu,
.xb-header-menu-scroll .xb-menu-primary>li .children {
  padding-left: 15px;
}

.xb-header-menu-scroll .xb-menu-primary>li .sub-menu a,
.xb-header-menu-scroll .xb-menu-primary>li .children a {
  padding-left: 0;
}

.xb-header-menu-scroll .xb-menu-primary>li .sub-menu.xb-mega-menu,
.xb-header-menu-scroll .xb-menu-primary>li .children.xb-mega-menu {
  padding-left: 0;
}

.xb-menu-primary .MuiList-padding {
  padding: 0;
}

.xb-menu-toggle,
.xb-menu-primary i{
  position: absolute;
  top: 10px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  right: 0;
  width: 28px;
  cursor: pointer;
  font-size: 15px;
  -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  color: #1b1b1b;
  -webkit-border-radius: 3px;
  -khtml-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.03);
  display: inline-block;
}

.xb-menu-toggle:before {
  content: "\f107";
  font-weight: 400;
  font-family: "Font Awesome 5 Pro";
}

.xb-menu-toggle.active:before {
  content: "\f106";
}

.xb-menu-primary {
  list-style: none;
  margin: 0;
  padding: 0;
}

.xb-menu-primary li {
  position: relative;
}

.xb-menu-primary li a,
.xb-menu-primary a,
.xb-menu-primary p {
  display: block;
  line-height: 46px;
  color: var(--color-black);
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.xb-menu-primary li>a:hover,
.xb-menu-primary li>a.current,
.xb-menu-primary li.current_page_item>a,
.xb-menu-primary li.current-menu-item>a,
.xb-menu-primary li.current_page_ancestor>a,
.xb-menu-primary li.current-menu-ancestor>a {
  color: var(--color-primary-two);
}

.xb-menu-primary .sub-menu,
.xb-menu-primary .children {
  position: relative;
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
}

.xb-menu-primary .sub-menu li a,
.xb-menu-primary .children li a {
  font-weight: 500;
  padding-left: 14px;
}

.xb-menu-primary>li>.sub-menu>li>.sub-menu>li a {
  padding-left: 0px;
}

.xb-menu-primary .xb-megamenu .elementor-container>.elementor-column {
  width: 100%;
}

.xb-menu-primary .xb-megamenu .elementor-container .elementor-widget-wrap {
  padding: 0 !important;
}

.xb-menu-primary .xb-megamenu .xb-link li {
  margin: 0;
}

.xb-menu-primary .xb-megamenu .xb-heading {
  padding-left: 14px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 12px;
  padding-bottom: 12px;
}

.xb-menu-primary .xb-megamenu .xb-heading .xb-item--title {
  margin-bottom: 0 !important;
  font-size: 15px !important;
  border-bottom: none !important;
}

.xb-menu-primary .xb-megamenu .elementor-widget-heading>.elementor-widget-container {
  margin-bottom: 0 !important;
  border-bottom: none !important;
}

.xb-menu-primary .xb-megamenu .elementor-section .elementor-container {
  flex-wrap: wrap;
}

.xb-menu-primary .xb-menu-toggle:hover {
  color: var(--color-primary-two);
}

.xb-logo-mobile {
  margin-bottom: 40px;
}

.xb-logo-mobile img {
  height: 40px;
}

.xb-header-mobile-search {
  margin-bottom: 20px;
}

.xb-header-mobile-search form {
  position: relative;
}

.xb-header-mobile-search .search-field {
  height: 50px;
  border: none;
  padding: 0 25px;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  color: #000;
  border: 2px solid rgba(150, 144, 162, 0.09);
  padding: 12px 15px 13px;
  background-color: transparent;
}

.xb-header-mobile-search .search-field:focus {
  color: #000;
  border-color: var(--color-primary-two);
}

.xb-header-mobile-search .search-submit {
  position: absolute;
  top: 5px;
  right: 10px;
  -webkit-border-radius: 0px;
  -khtml-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
  line-height: 36px;
  padding: 0;
  width: 30px;
  height: 40px;
  line-height: 40px;
  background: none;
  color: var(--primary-color);
}

.xb-menu-close {
  background-color: rgba(0, 0, 0, 0.03);
  position: absolute;
  top: 0;
  right: 0;
  -webkit-border-radius: 0px;
  -khtml-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
}

.xb-header-mobile-search .search-submit {
  position: absolute;
  top: 5px;
  right: 10px;
  -webkit-border-radius: 0px;
  -khtml-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
  line-height: 36px;
  padding: 0;
  width: 30px;
  height: 40px;
  line-height: 40px;
  background: none;
  color: var(--color-primary-two);
}

.xb-nav-mobile {
  font-size: 26px;
  color: var(--color-primary-two);
}

.xb-nav-mobile:hover {
  color: var(--color-primary-two);
}

.xb-menu-primary .mega_menu_wrapper {
  position: unset;
}

.xb-menu-primary .mega_menu_wrapper .container {
  padding-left: 0;
  padding-right: 0;
}

.xb-menu-primary .mega_menu_wrapper .mega_menu_wrapper_inner {
  padding: 10px;
}

.xb-header-menu-scroll .xb-menu-primary .megamenu .sub-menu,
.xb-header-menu-scroll .xb-menu-primary .megamenu .children {
  padding-left: 0 !important;
}

/*----------------------------------------*/
/*  12. preloader
/*----------------------------------------*/
#xb-loadding {
  height: 100%;
  position: fixed;
  width: 100%;
  z-index: 999999;
  top: 0;
  left: 0;
  -webkit-transition: all 300ms linear 0ms;
  -khtml-transition: all 300ms linear 0ms;
  -moz-transition: all 300ms linear 0ms;
  -ms-transition: all 300ms linear 0ms;
  -o-transition: all 300ms linear 0ms;
  transition: all 300ms linear 0ms;
  background-color: #fff;
  -webkit-transform: scale(1);
  -khtml-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

#xb-loadding.xb-loaded {
  opacity: 0;
  visibility: hidden;
}

#xb-loadding.xb-loaded.style9 {
  -webkit-transform: scale(1.5);
  -khtml-transform: scale(1.5);
  -moz-transform: scale(1.5);
  -ms-transform: scale(1.5);
  -o-transform: scale(1.5);
  transform: scale(1.5);
}

#xb-loadding .xb-dual-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -khtml-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 64px;
  height: 64px;
}

#xb-loadding .xb-dual-ring:after {
  content: " ";
  display: block;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border-width: 4px;
  border-style: solid;
  border-right-color: transparent;
  border-left-color: transparent;
  animation: xb_dual_ring 1.2s linear infinite;
  border-bottom-color: var(--color-primary-two);
  border-top-color: var(--color-primary-two);
}

#xb-loadding .loading-spin {
  display: block;
  height: 65px;
  margin: -32px auto 0;
  position: relative;
  top: 50%;
  width: 65px;
}

#xb-loadding .loading-spin .spinner {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  -webkit-animation: spin-rotate-all 1s linear infinite;
  animation: spin-rotate-all 1s linear infinite;
}

#xb-loadding .loading-spin .spinner .right-side,
#xb-loadding .loading-spin .spinner .left-side {
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  overflow: hidden;
  -webkit-animation: spin-fade-in-first 1.2s linear infinite alternate;
  animation: spin-fade-in-first 1.2s linear infinite alternate;
}

#xb-loadding .loading-spin .spinner .left-side {
  left: 0;
}

#xb-loadding .loading-spin .spinner .right-side {
  right: 0;
}

#xb-loadding .loading-spin .spinner.color-2 .right-side,
#xb-loadding .loading-spin .spinner.color-2 .left-side {
  -webkit-animation: spin-fade-in-second 1.2s linear infinite alternate;
  animation: spin-fade-in-second 1.2s linear infinite alternate;
}

#xb-loadding .loading-spin .spinner .bar {
  width: 100%;
  height: 100%;
  -webkit-border-radius: 200px 0 0 200px;
  -khtml-border-radius: 200px 0 0 200px;
  -moz-border-radius: 200px 0 0 200px;
  -ms-border-radius: 200px 0 0 200px;
  -o-border-radius: 200px 0 0 200px;
  border-radius: 200px 0 0 200px;
  border: 6px solid var(--color-primary-two);
  position: relative;
}

#xb-loadding .loading-spin .spinner .bar:after {
  content: "";
  width: 6px;
  height: 6px;
  display: block;
  background: var(--color-primary-two);
  position: absolute;
  -webkit-border-radius: 6px;
  -khtml-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
  border-radius: 6px;
}

#xb-loadding .loading-spin .spinner .right-side .bar {
  -webkit-border-radius: 0 200px 200px 0;
  -khtml-border-radius: 0 200px 200px 0;
  -moz-border-radius: 0 200px 200px 0;
  -ms-border-radius: 0 200px 200px 0;
  -o-border-radius: 0 200px 200px 0;
  border-radius: 0 200px 200px 0;
  border-left: none;
  -webkit-transform: rotate(-10deg);
  -webkit-transform-origin: left center;
  transform: rotate(-10deg);
  transform-origin: left center;
  -webkit-animation: spin-rotate-right .75s linear infinite alternate;
  animation: spin-rotate-right 0.75s linear infinite alternate;
}

#xb-loadding .loading-spin .spinner .right-side .bar:after {
  bottom: -6px;
  left: -3px;
}

#xb-loadding .loading-spin .spinner .left-side .bar {
  border-right: none;
  -webkit-transform: rotate(10deg);
  transform: rotate(10deg);
  -webkit-transform-origin: right center;
  transform-origin: right center;
  -webkit-animation: spin-rotate-left .75s linear infinite alternate;
  animation: spin-rotate-left 0.75s linear infinite alternate;
}

#xb-loadding .loading-spin .spinner .left-side .bar:after {
  bottom: -6px;
  right: -3px;
}

#xb-loadding.xb-loader.style-3 {
  background-color: #010315;
}

#xb-loadding.xb-loader.style-2 .xb-dual-ring:after {
  border-bottom-color: var(--color-primary);
  border-top-color: var(--color-primary);
}

#xb-loadding.xb-loader.style-3 .xb-dual-ring:after {
  border-bottom-color: #ae6dfe;
  border-top-color: #ae6dfe;
}

#xb-loadding.xb-loader.style-4 {
  background-color: #FEFAF2;
}

#xb-loadding.xb-loader.style-4 .xb-dual-ring:after {
  border-bottom-color: var(--color-primary-three);
  border-top-color: var(--color-primary-three);
}

#xb-loadding.xb-loader.style-5 .xb-dual-ring:after {
  border-bottom-color: var(--color-primary-four);
  border-top-color: var(--color-primary-four);
}

#xb-loadding.xb-loader.style-6 .xb-dual-ring:after {
  border-bottom-color: #2042BF;
  border-top-color: #2042BF;
}

@keyframes xb_dual_ring {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes xb_dual_ring {
  0% {
    -webkit-transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

.xb-newslatter {
  margin-top: -80px;
  padding: 60px 50px;
  background-color: var(--color-white);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

@media (max-width: 1199px) {
  .xb-newslatter {
    padding: 60px 25px;
  }
}

@media (max-width: 991px) {
  .xb-newslatter {
    padding: 60px 50px;
    margin-bottom: 20px;
  }
}

.xb-newslatter .xb-item--icon {
  height: 70px;
  width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background-color: #eaeef0;
}

.xb-newslatter .xb-item--title {
  font-size: 48px;
  margin: 15px 0 25px;
  letter-spacing: -0.02em;
}

@media (max-width: 1199px) {
  .xb-newslatter .xb-item--title {
    font-size: 37px;
  }
}

.xb-newslatter .xb-item--input_field input {
  height: 60px;
  background: #eaeef0;
  padding: 0 20px 0 46px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.xb-newslatter .xb-item--input_field input:focus~.img {
  opacity: 1;
}

.xb-newslatter .xb-item--input_field .img {
  position: absolute;
  left: 20px;
  top: 48%;
  opacity: 0.5;
  transform: translateY(-50%);
}

.xb-newslatter .xb-item--button {
  font-size: 20px;
  font-weight: 500;
  display: block;
  width: 100%;
  padding: 20px 0;
  text-align: center;
  margin: 10px 0 13px;
  z-index: 1;
  color: var(--color-white);
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background-color: var(--color-primary);
}

.xb-newslatter .xb-item--text {
  font-size: 14px;
}

@media (max-width: 1199px) {
  .is-footer {
    margin-left: 10px;
  }
}

@media (max-width: 767px) {
  .is-footer {
    margin-left: 0px;
  }
}

.is-footer .footer-inner {
  margin-top: 30px;
}

.footer-widget {
  margin-top: 30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget {
    width: 50%;
  }
}

.footer-widget .xb-item--sub-title {
  color: #c7c7d6;
  margin-bottom: 15px;
  display: inline-block;
  text-transform: capitalize;
}

.footer-widget .xb-item--list {
  font-weight: 500;
  font-size: 20px;
  line-height: 37px;
  letter-spacing: 0em;
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  font-family: var(--font-heading);
}

@media (max-width: 1199px) {
  .footer-widget .xb-item--list {
    font-size: 18px;
  }
}

.footer-widget .xb-item--list a {
  color: currentColor;
}

.footer-widget .xb-item--list a:hover {
  color: #fff;
  text-decoration: underline;
}

.footer-nav {
  padding: 43px 0;
  border-bottom: 1px solid #29292a;
}

@media (max-width: 767px) {
  .footer-nav {
    padding: 43px 0 0px;
  }
}

.footer-nav li {
  font-weight: 500;
  font-size: 20px;
  position: relative;
  padding-bottom: 5px;
  color: var(--color-white);
  font-family: var(--font-heading);
}

.footer-nav li:not(:last-child) {
  margin-right: 86px;
}

@media (max-width: 1199px) {
  .footer-nav li:not(:last-child) {
    margin-right: 50px;
    margin-bottom: 20px;
  }
}

@media only screen and (max-width: 1023px) {
  .footer-nav li:not(:last-child) {
    margin-right: 45px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-nav li:not(:last-child) {
    margin-right: 114px;
  }
}

@media (max-width: 1199px) {
  .footer-nav li {
    margin-bottom: 20px;
  }
}

.footer-nav li a {
  color: currentColor;
}

.footer-nav li::before {
  position: absolute;
  left: 0;
  bottom: 0;
  content: '';
  height: 1px;
  width: 0;
  opacity: 0;
  background-color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.footer-nav li:hover::before {
  width: 100%;
  opacity: 1;
}

.footer-nav li span {
  position: absolute;
  top: -21px;
  right: -54px;
  content: '';
  font-size: 10px;
  font-weight: 500;
  height: 22px;
  width: 73px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  background: var(--color-primary);
}

@media (max-width: 1199px) {
  .footer-nav li span {
    top: -19px;
    right: -36px;
    height: 18px;
    width: 65px;
  }
}

.footer-nav li span::before {
  position: absolute;
  content: '';
  bottom: -10px;
  left: 9px;
  border-right: 10px solid #d44a00;
  border-bottom: 10px solid transparent;
  transform: rotate(-90deg);
}

.footer-info .xb-item--email,
.footer-info .xb-item--number {
  display: block;
  font-weight: 700;
  font-size: 26px;
  color: var(--color-white);
  font-family: var(--font-heading);
}

@media (max-width: 1199px) {

  .footer-info .xb-item--email,
  .footer-info .xb-item--number {
    font-size: 24px;
  }
}

@media (max-width: 767px) {

  .footer-info .xb-item--email,
  .footer-info .xb-item--number {
    font-size: 22px;
  }
}

.footer-info .xb-item--email {
  margin-bottom: 45px;
}

.footer-info .xb-item--sub-title {
  color: #c7c7d6;
  display: block;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.footer-copyright .copyright,
.footer-copyright .privacy {
  color: var(--color-white);
}

.footer-copyright .copyright a,
.footer-copyright .privacy a {
  color: currentColor;
}

.footer-copyright .copyright a:hover,
.footer-copyright .privacy a:hover {
  text-decoration: underline;
}

@media (max-width: 767px) {
  .footer-copyright .privacy {
    margin-top: 20px;
  }
}

.footer-bottom {
  padding: 40px 0 55px;
}

@media (max-width: 991px) {
  .footer-bottom {
    padding: 30px 0 40px;
  }
}

@media (max-width: 767px) {
  .footer-bottom {
    padding: 20px 0 20px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-bottom {
    padding: 30px 0 35px;
  }
}

.footer-bottom .title {
  font-size: 200px;
  line-height: 1;
  display: inline-block;
  letter-spacing: -0.02em;
  color: var(--color-white);
  font-family: var(--font-heading);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .footer-bottom .title {
    font-size: 170px;
  }
}

@media (max-width: 1199px) {
  .footer-bottom .title {
    font-size: 120px;
  }
}

@media (max-width: 1199px) {
  .footer-bottom .title {
    font-size: 70px;
  }
}

@media (max-width: 767px) {
  .footer-bottom .title {
    font-size: 30px;
  }
}

.footer-style-two .footer-info {
  border-bottom: 1px solid #3d414a;
}

@media (max-width: 767px) {
  .footer-style-two .footer-info {
    padding-bottom: 20px;
  }
}

.footer-style-two .info-item {
  position: relative;
  padding: 46px 0;
}

@media (max-width: 767px) {
  .footer-style-two .info-item {
    padding: 15px 0;
  }
}

.footer-style-two .info-item:first-child::before {
  display: none;
}

.footer-style-two .info-item::before {
  position: absolute;
  content: '';
  left: -70px;
  bottom: 0;
  height: 156px;
  width: 20px;
  background-image: url(../images/icon/line.png);
  background-position: center;
  background-repeat: no-repeat;
}

@media (max-width: 1199px) {
  .footer-style-two .info-item::before {
    left: -47px;
  }
}

.footer-style-two .info-item .xb-item--icon {
  height: 56px;
  width: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: #FF8139;
}

.footer-style-two .info-item .xb-item--title {
  font-size: 22px;
  margin-bottom: 10px;
  line-height: 30px;
  color: var(--color-white);
}

.footer-style-two .info-item .xb-item--content {
  font-size: 14px;
  font-weight: 500;
  color: #d2dbef;
}

.footer-style-two .sa-newslatter {
  max-width: 330px;
}

.footer-style-two .sa-newslatter .xb-item--sub-title {
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  color: #d2dbef;
  margin-bottom: 30px;
  display: inline-block;
}

.footer-style-two .sa-newslatter .xb-item--input_field {
  margin: 40px 0 25px;
}

.footer-style-two .sa-newslatter .xb-item--input_field input {
  height: 60px;
  color: var(--color-white);
  background: transparent;
  padding: 0 20px 0 46px;
  border: 2px solid #242934;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.footer-style-two .sa-newslatter .xb-item--input_field input::-webkit-input-placeholder {
  color: #54565c;
  opacity: 1;
}

.footer-style-two .sa-newslatter .xb-item--input_field input::-moz-placeholder {
  color: #54565c;
  opacity: 1;
}

.footer-style-two .sa-newslatter .xb-item--input_field input:-ms-input-placeholder {
  color: #54565c;
  opacity: 1;
}

.footer-style-two .sa-newslatter .xb-item--input_field input:-moz-placeholder {
  color: #54565c;
  opacity: 1;
}

.footer-style-two .sa-newslatter .xb-item--input_field .img {
  position: absolute;
  left: 20px;
  top: 48%;
  opacity: 0.5;
  transform: translateY(-50%);
}

.footer-style-two .sa-newslatter .xb-item--btn {
  position: absolute;
  top: 50%;
  right: 0;
  color: var(--color-white);
  height: 100%;
  padding: 0 20px;
  background: #0c111d;
  transform: translateY(-50%);
  border: 2px solid #242934;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.footer-style-two .sa-newslatter .xb-item--text {
  color: #d2dbef;
  line-height: 26px;
  letter-spacing: -0.01em;
}

.footer-style-two .sa-newslatter .xb-item--text a {
  color: currentColor;
  text-decoration: underline;
}

.footer-style-two .sa-newslatter .xb-item--text a:hover {
  color: var(--color-white);
}

.footer-style-two .footer-widget .xb-item--sub-title {
  font-size: 14px;
  text-transform: uppercase;
}

.footer-style-two .footer-widget .xb-item--list {
  line-height: 40px;
}

.footer-style-two .footer-copyright {
  padding: 12px 0 32px;
  border-top: 2px solid rgba(255, 255, 255, 0.2);
}

.footer-style-two .footer-copyright .footer-link li:not(:last-child) {
  margin-right: 15px;
}

.footer-style-two .footer-copyright .footer-link li span {
  font-weight: 500;
  font-size: 14px;
  letter-spacing: -0.01em;
  text-transform: uppercase;
  color: var(--color-white);
}

.footer-style-two .footer-copyright .footer-link li a {
  font-size: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.5);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.footer-style-two .footer-copyright .footer-link li a:hover {
  color: var(--color-white);
}

.footer-style-two .footer-copyright a {
  color: var(--color-white);
}

.footer-style-two .footer-copyright a:hover {
  text-decoration: underline;
}

.social-link {
  gap: 22px;
  flex-wrap: nowrap;
}

@media (max-width: 767px) {
  .social-link {
    flex-wrap: wrap;
  }
}

.footer-style-three {
  z-index: 1;
}

.footer-style-three::before {
  position: absolute;
  left: 0;
  top: 0;
  content: '';
  z-index: -1;
  height: 100%;
  width: 100%;
  background-image: url(../images/footer/footer-bg-shape.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.footer-style-three .social-item {
  z-index: 1;
  position: relative;
}

.footer-style-three .social-item::before,
.footer-style-three .social-item::after {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  z-index: -1;
  content: '';
  background: linear-gradient(334deg, rgba(103, 128, 210, 0.3) 0%, rgba(47, 59, 141, 0.3) 100%);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.footer-style-three .social-item::after {
  background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
  opacity: 0;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.footer-style-three .social-item img {
  opacity: 0.5;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.footer-style-three .social-item:hover::after {
  opacity: 1;
}

.footer-style-three .social-item:hover img {
  opacity: 2;
}

.footer-style-three .social-item .xb-icon {
  padding: 20px 51px;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s;
  background: #01041E;
  display: inline-flex;
  min-height: 86px;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1199px) {
  .footer-style-three .social-item .xb-icon {
    padding: 20px 30px;
    min-height: 77px;
  }
}

@media (max-width: 767px) {
  .footer-style-three .social-item .xb-icon {
    padding: 16px 15px;
    min-height: 62px;
  }
}

.footer-style-three .social-item .xb-icon img {
  opacity: 0.5;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.footer-style-three .social-item .xb-icon:hover img {
  opacity: 2;
}

.footer-style-three .footer-widget .xb-item--sub-title {
  color: #a9a4c0;
  margin-bottom: 20px;
}

.footer-style-three .footer-widget .xb-item--list {
  text-transform: capitalize;
  font-family: var(--font-heading-two);
}

.footer-style-three .sa-newslatter .xb-item--sub-title {
  color: #a9a4c0;
  margin-bottom: 25px;
}

.footer-style-three .sa-newslatter .xb-item--input_field {
  margin: 0;
  z-index: 1;
}

.footer-style-three .sa-newslatter .xb-item--input_field input {
  background: #1A1C2C;
  border: none;
  padding-left: 20px;
  padding-right: 65px;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
}

.footer-style-three .sa-newslatter .xb-item--input_field::before {
  position: absolute;
  content: '';
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  z-index: -1;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  background: linear-gradient(334deg, rgba(103, 128, 210, 0.4) 0%, rgba(47, 59, 141, 0.4) 100%);
}

.footer-style-three .sa-newslatter .xb-item--btn {
  border: none;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  background: linear-gradient(86deg, #431dab 0%, #ae6dfe 100%);
}

.footer-style-three .sa-newslatter .xb-item--input-box {
  margin-top: 25px;
}

.footer-style-three .sa-newslatter .xb-item--input-box label {
  font-weight: 500;
  margin-bottom: 10px;
}

@media (max-width: 991px) {
  .footer-style-four {
    padding-top: 70px;
  }
}

.footer-style-four .footer-widget {
  padding-top: 105px;
  padding-bottom: 110px;
  margin-top: 0;
  position: relative;
}

@media (max-width: 1199px) {
  .footer-style-four .footer-widget {
    padding-bottom: 80px;
  }
}

@media (max-width: 1199px) {
  .footer-style-four .footer-widget {
    padding-bottom: 0;
    padding-top: 50px;
  }
}

.footer-style-four .footer-widget .xb-item--sub-title {
  color: #f6f0e6;
  font-weight: 500;
}

.footer-style-four .footer-widget .xb-item--list {
  font-weight: 600;
}

.footer-style-four .footer-widget .xb-item--number,
.footer-style-four .footer-widget .xb-item--email {
  color: #fff;
  font-size: 30px;
  font-weight: 700;
  line-height: 40px;
  transform: translateY(4px);
}

@media (max-width: 1199px) {

  .footer-style-four .footer-widget .xb-item--number,
  .footer-style-four .footer-widget .xb-item--email {
    font-size: 25px;
  }
}

.footer-style-four .footer-widget .xb-item--number:not(:last-child),
.footer-style-four .footer-widget .xb-item--email:not(:last-child) {
  margin-bottom: 30px;
}

.footer-style-four .footer-widget .xb-item--number span,
.footer-style-four .footer-widget .xb-item--email span {
  margin-right: 9px;
}

.footer-style-four .footer-widget .xb-item--social-icon:not(:last-child) {
  margin-right: 12.63px;
}

.footer-style-four .footer-widget .xb-item--social-icon a {
  height: 50px;
  width: 50px;
  background: #16140c;
  border: 1px solid #2d2b24;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-style-four .footer-widget .xb-item--social-icon a i {
  color: var(--color-white);
}

.footer-style-four .footer-widget .xb-item--social-icon:hover a {
  border: 1px solid #9a4497;
  background: var(--color-primary-three);
}

.footer-style-four .footer-widget .xb-item--apps {
  gap: 7px;
}

@media (max-width: 1199px) {
  .footer-style-four .footer-widget .xb-item--app {
    max-width: 120px;
  }
}

.footer-style-four .footer-widget .xb-item--input-box {
  margin-top: 44px;
}

.footer-style-four .footer-widget .xb-item--input-box label {
  color: #f6f0e6;
  font-weight: 500;
  text-transform: uppercase;
}

.footer-style-four .footer-widget .xb-item--input_field {
  margin-bottom: 0;
  margin-top: 15px;
}

.footer-style-four .footer-widget .xb-item--input_field input {
  height: 50px;
  background: #16140c;
  padding: 0 60px 0 20px;
  border: 1px solid #2d2b24;
}

.footer-style-four .footer-widget .xb-item--input_field button {
  border: none;
  right: 2px;
  height: 89%;
  padding: 0 15px;
  background: #9a4497;
}

.footer-style-four .footer-nees-helf::before,
.footer-style-four .sa-newslatter::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  height: 100%;
  width: 1px;
  background: #2d2b24;
}

.footer-style-four .footer-nees-helf::after,
.footer-style-four .sa-newslatter::after {
  position: absolute;
  content: '';
  bottom: -10px;
  left: -70px;
  height: 20px;
  width: 20px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  background: #16140c;
}

.footer-style-four .footer-nees-helf::before {
  left: -60px;
}

.footer-style-four .sa-newslatter {
  margin-left: 95px;
}

@media (max-width: 1199px) {
  .footer-style-four .sa-newslatter {
    margin-left: 50px;
  }
}

@media (max-width: 991px) {
  .footer-style-four .sa-newslatter {
    margin-left: 0;
  }
}

.footer-style-four .sa-newslatter::before {
  left: -54px;
}

.footer-style-four .sa-newslatter::after {
  left: -64px;
}

.footer-style-four .footer-copyright {
  border-top: 1px solid #2d2b24;
}

.footer-style-four .bottom-right .xb-line {
  width: 1px;
  height: 12px;
  margin: 0 20px;
  background: #2d2b24;
  display: inline-block;
}

@media (max-width: 991px) {
  .footer-style-four .footer-copyright {
    padding-top: 60px;
    border: 0;
  }
}

.footer-style-five .cd-newslatter-wrap {
  padding: 49px 0;
  margin: 0 15px;
  border: 1px solid #ccd4d6;
  border-radius: 24px;
  -webkit-border-radius: 24px;
  -moz-border-radius: 24px;
  -ms-border-radius: 24px;
  -o-border-radius: 24px;
  background: var(--color-white);
  box-shadow: 0 44px 38px 0 rgba(102, 80, 65, 0.12);
}

@media (max-width: 767px) {
  .footer-style-five .cd-newslatter-wrap {
    margin: 0;
    padding: 40px 20px;
  }
}

@media (max-width: 991px) {
  .footer-style-five .cd-newslatter-wrap .cb-shape {
    display: none;
  }
}

.footer-style-five .cd-newslatter-wrap .cb-shape .shape {
  position: absolute;
}

.footer-style-five .cd-newslatter-wrap .cb-shape .shape--one {
  left: 15px;
  bottom: 15px;
}

.footer-style-five .cd-newslatter-wrap .cb-shape .shape--two {
  top: 0;
  right: 0;
}

.footer-style-five .cd-newslatter .xb-item--title {
  font-size: 28px;
  line-height: 48px;
  letter-spacing: -0.05em;
}

.footer-style-five .cd-newslatter .xb-item--content {
  font-size: 14px;
  margin: 5px 0 35px;
}

.footer-style-five .cd-newslatter .xb-item--input_field {
  gap: 7px;
  width: 100%;
  max-width: 472px;
  margin-bottom: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 767px) {
  .footer-style-five .cd-newslatter .xb-item--input_field {
    flex-wrap: wrap;
  }
}

.footer-style-five .cd-newslatter .xb-item--input_field input {
  height: 48px;
  background: var(--color-white);
  border-radius: 8px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -ms-border-radius: 8px;
  -o-border-radius: 8px;
  border: 1px solid #ccd4d6;
  color: var(--color-heading-four);
}

.footer-style-five .cd-newslatter .xb-item--input_field input::placeholder {
  color: var(--color-heading-four);
}

.footer-style-five .cd-newslatter .xb-item--button {
  padding: 14px 34px;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--color-white);
  border-radius: 8px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -ms-border-radius: 8px;
  -o-border-radius: 8px;
  font-family: var(--font-heading-four);
  background: var(--color-heading-four);
}

.footer-style-five .cd-newslatter .xb-item--privacy {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 7px;
}

.footer-style-five .cd-newslatter .xb-item--privacy input {
  width: 20px;
  height: 20px;
  transform: translateY(-1px);
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}

.footer-style-five .cd-newslatter .xb-item--privacy label {
  font-size: 12px;
  color: #212877;
  font-weight: 400;
}

.footer-style-five .cd-newslatter .xb-item--privacy label a {
  color: currentColor;
  text-decoration: underline;
}

.footer-style-five .cd-newslatter .xb-item--privacy span {
  font-size: 12px;
  font-weight: 400;
}

.footer-style-five .cd-newslatter .xb-item--privacy span a {
  font-weight: 500;
  color: currentColor;
  text-decoration: underline;
}

.footer-style-five .cd-newslatter .xb-item--privacy .form-check {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.footer-style-five .cd-newslatter .xb-item--privacy .form-check-input:checked {
  background-color: #212877;
  border-color: #212877;
}

.footer-style-five .cd-newslatter .xb-item--privacy .form-check-input:focus {
  border-color: #212877;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(33, 40, 119, 0.25);
}

.footer-style-five .footer-widget {
  margin: 0;
  max-width: 747px;
}

.footer-style-five .footer-widget .xb-logo {
  margin-bottom: 25px;
}

.footer-style-five .footer-link li:not(:last-child) {
  margin-right: 20px;
}

.footer-style-five .footer-link li span {
  font-size: 14px;
  margin-right: 10px;
}

.footer-style-five .footer-link li a {
  color: currentColor;
}

.footer-style-five .footer-link li a i {
  -webkit-transition: 0.3S;
  -o-transition: 0.3S;
  transition: 0.3S;
  color: var(--font-heading-four);
}

.footer-style-five .footer-link li:hover a i {
  color: var(--color-primary-four);
}

.footer-style-five .footer-copyright {
  margin-top: 47px;
  padding: 17px 0;
  border-top: 1px solid #ccd4d6;
}

.footer-style-five .footer-copyright .copyright,
.footer-style-five .footer-copyright .text {
  font-weight: 400;
  color: var(--color-heading-four);
}

.footer-style-five .footer-bottom {
  position: relative;
  overflow: hidden;
  padding: 126px 0 90px;
  background: var(--color-heading-four);
  z-index: 1;
}

.footer-style-five .footer-bottom .title {
  font-size: 150px;
  color: #fff;
  z-index: -1;
}

.footer-style-five .footer-bottom .footer-btn {
  position: absolute;
  left: 50%;
  top: -94px;
  transform: translateX(-50%);
  font-family: var(--font-heading-four);
  font-weight: 800;
  font-size: 24px;
  line-height: 41px;
  text-transform: uppercase;
  text-align: center;
  height: 489px;
  width: 489px;
  padding: 97px;
  color: var(--color-heading-four);
  background: #ffd00e;
  display: flex;
  align-items: center;
  justify-content: center;
  display: inline-flex;
  flex-direction: column;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  z-index: 11;
}

.footer-style-five .footer-bottom .footer-btn i {
  font-size: 40px;
  margin-bottom: 30px;
  transform: rotate(-45deg);
}

.footer-style-five .footer-links li::before {
  background-color: #212877;
}

.footer-style-five .footer-links li a {
  color: #212877;
}

.footer-bottom .marquee {
  display: flex;
  overflow: hidden;
  white-space: nowrap;
}

.footer-bottom .marquee-content {
  margin-left: 0;
}

.da-footer-title .title {
  font-weight: 800;
  font-size: 150px;
  color: #1438bc;
  letter-spacing: -0.02em;
  display: inline-block;
  text-transform: capitalize;
}
.da-footer-title {
  overflow: hidden;
}
.da-footer-title .marquee {
  display: flex;
  overflow: hidden;
  white-space: nowrap;
}

@media (max-width: 1199px) {
  .da-footer-title .title {
    font-size: 80px;
  }
}

@media (max-width: 767px) {
  .da-footer-title .title {
    font-size: 40px;
  }
}

.da-footer-title .title a {
  color: currentColor;
}

.da-footer-title .title span {
  color: transparent;
  margin-right: 30px;
  -webkit-text-stroke: 2px #1438BC;
}

.footer-style-six .xb-footer {
  background: #fff;
  margin-top: 117px;
  padding: 78px 105px 76px;
  border-radius: 24px;
  -webkit-border-radius: 24px;
  -moz-border-radius: 24px;
  -ms-border-radius: 24px;
  -o-border-radius: 24px;
  box-shadow: 0 19px 28px 0 rgba(25, 28, 51, 0.05);
}

@media (max-width: 991px) {
  .footer-style-six .xb-footer {
    padding: 50px;
  }
}

.footer-style-six .da-footer-inner .xb-item--logo {
  margin-right: 10px;
  display: inline-block;
}

.footer-style-six .da-footer-inner .xb-item--content {
  font-weight: 600;
  font-size: 12px;
  padding: 6px 17px;
  background: #f4f5fc;
  border-radius: 100px;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -ms-border-radius: 100px;
  -o-border-radius: 100px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.06);
}

.footer-style-six .da-footer-inner .xb-item--nav_item li {
  font-weight: 800;
  font-size: 16px;
  position: relative;
}

.footer-style-six .da-footer-inner .xb-item--nav_item li:not(:last-child) {
  margin-right: 51px;
}

.footer-style-six .da-footer-inner .xb-item--nav_item li a {
  color: currentColor;
}

.footer-style-six .da-footer-inner .xb-item--nav_item li::before {
  position: absolute;
  left: 0;
  bottom: 0;
  content: '';
  height: 2px;
  width: 0;
  opacity: 0;
  transition: 0.3s;
  background-color: #1438bc;
}

.footer-style-six .da-footer-inner .xb-item--nav_item li:hover {
  color: #1438bc;
}

.footer-style-six .da-footer-inner .xb-item--nav_item li:hover::before {
  width: 100%;
  opacity: 1;
}

.footer-style-six .footer-copyright {
  padding-top: 35px;
  border-top: 1px solid #e3e3e3;
}

.footer-style-six .footer-copyright .copyright,
.footer-style-six .footer-copyright .privacy {
  color: #212877;
}

.footer-style-six .footer-links li::before {
  background-color: #212877;
}

.footer-style-six .footer-links li a {
  color: #212877;
}

.footer-links li {
  margin-right: 11px;
  padding-right: 11px;
  position: relative;
}

.footer-links li::before {
  position: absolute;
  bottom: 4px;
  right: 0;
  width: 2px;
  height: 2px;
  background-color: var(--color-white);
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  content: "";
}

.footer-links li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.footer-links li:last-child::before {
  display: none;
}

.footer-links li a {
  color: var(--color-white);
}

.footer-links li a:hover {
  text-decoration: underline;
}

.hds-footer-links li {
  margin-right: 20px;
  padding-right: 20px;
  position: relative;
}

.hds-footer-links li::before {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  width: 2px;
  height: 12px;
  background-color: #2D2B24;
  content: "";
}

.hds-footer-links li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.hds-footer-links li:last-child::before {
  display: none;
}

.hds-footer-links li a {
  color: #fff;
}

.hds-footer-links li a:hover {
  text-decoration: underline;
}

.cs-brand_wrap {
  overflow: hidden;
}

.cs-brand-inner {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.da-brand_marquee {
  display: flex;
  will-change: transform;
}

.cs-brand-item {
  flex-shrink: 0;
  margin-right: 20px;
}

.cs-brand-inner.marquee-right .da-brand_marquee {
  display: flex;
  animation: marquee-right 200s linear infinite;
}

@keyframes marquee-right {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.cta.bg {
  background: #f7f7f9;
}

.roadmap .swiper-pagination {
  bottom: 50px;
}
.roadmap .swiper-pagination .swiper-pagination-bullet{
  background-color: #fff;
  width: 10px;
  height: 10px;
}