# Ignore node modules
node_modules/

# Ignore vendor folder (for PHP/Composer)
vendor/

# Ignore environment files
.env
.env.*

# Ignore logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore system files
.DS_Store
Thumbs.db

# Ignore build and cache directories
dist/
build/
.cache/
.tmp/

# Ignore zip and other archive files
*.zip
*.rar
*.tar
*.tar.gz

# Ignore IDE/editor folders
.vscode/
.idea/

# Ignore compiled files
*.class
*.pyc

# Ignore coverage reports
coverage/

.next/
*.pdf

# Ignore virtual environment
venv/
env/
